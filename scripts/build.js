import { exec } from "child_process";
import path from "path";
import fs from "fs";

// 兼容 __dirname
const __dirname = decodeURIComponent(path.dirname(new URL(import.meta.url).pathname));

// 定义打包任务
const tasks = [
  { script: "build:dev", outputDir: "dev" },
  { script: "build:test", outputDir: "test" },
  { script: "build:pre", outputDir: "pre" },
  { script: "build:prod", outputDir: "prod" },
];

// 清空文件夹
const clearFolder = (folderPath) => {
  if (fs.existsSync(folderPath)) {
    fs.rmSync(folderPath, { recursive: true, force: true });
    console.log(`已清空文件夹：${folderPath}`);
  }
};

// 执行命令的函数
const runCommand = (command) => {
  return new Promise((resolve, reject) => {
    const child = exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ 执行失败：${command}`);
        console.error(stderr);
        reject(error);
      } else {
        console.log(`✅ 执行成功：${command}`);
        resolve();
      }
    });
    child.stdout.on("data", (data) => process.stdout.write(data));
    child.stderr.on("data", (data) => process.stderr.write(data));
  });
};

// 主函数
const build = async () => {
  for (const task of tasks) {
    const outputPath = path.resolve(__dirname, "../distAll", task.outputDir);
    console.log(`\n=== 开始执行任务：${task.script}，输出目录：${outputPath} ===`);
    clearFolder(outputPath);

    // 传递 outDir 给 vite.config.ts，需在 vite.config.ts 里读取 process.env.npm_config_outdir
    const command = `npm run ${task.script} -- --outDir=${outputPath}`;
    try {
      await runCommand(command);
    } catch (error) {
      console.error(`任务失败：${task.script}`);
      process.exit(1);
    }
  }
  console.log("\n🎉 所有打包任务完成！");
};

build();
