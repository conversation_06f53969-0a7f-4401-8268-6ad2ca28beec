#!/usr/bin/env node

/**
 * 简单构建脚本
 * 使用最简单的配置进行构建，避免复杂的代码分割问题
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 项目根目录
const projectRoot = path.resolve(__dirname, '..');

/**
 * 执行命令
 */
function runCommand(command, description) {
  console.log(`\n🔧 ${description}...`);
  console.log(`执行命令: ${command}`);
  
  try {
    const output = execSync(command, { 
      cwd: projectRoot, 
      stdio: 'inherit',
      encoding: 'utf8'
    });
    console.log(`✅ ${description} 完成`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} 失败:`, error.message);
    return false;
  }
}

/**
 * 清理构建目录
 */
function cleanBuildDir() {
  const distDir = path.join(projectRoot, 'dist');
  
  if (fs.existsSync(distDir)) {
    console.log('🧹 清理构建目录...');
    try {
      fs.rmSync(distDir, { recursive: true, force: true });
      console.log('✅ 构建目录清理完成');
    } catch (error) {
      console.warn('⚠️  清理构建目录失败:', error.message);
    }
  }
}

/**
 * 检查构建结果
 */
function checkBuildResult() {
  const distDir = path.join(projectRoot, 'dist');
  
  if (!fs.existsSync(distDir)) {
    console.error('❌ 构建失败：dist 目录不存在');
    return false;
  }
  
  const jsDir = path.join(distDir, 'js');
  if (!fs.existsSync(jsDir)) {
    console.error('❌ 构建失败：js 目录不存在');
    return false;
  }
  
  const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
  console.log(`\n📊 构建结果:`);
  console.log(`- 生成了 ${jsFiles.length} 个 JavaScript 文件`);
  
  // 检查文件大小
  let hasLargeFiles = false;
  jsFiles.forEach(file => {
    const filePath = path.join(jsDir, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    console.log(`  - ${file}: ${sizeKB} KB`);
    
    if (stats.size > 2 * 1024 * 1024) { // 2MB
      console.log(`    ⚠️  文件较大 (${sizeMB} MB)`);
      hasLargeFiles = true;
    }
  });
  
  if (hasLargeFiles) {
    console.log('\n💡 提示: 发现较大的文件，但这是正常的（我们已禁用代码分割）');
  }
  
  console.log('✅ 构建成功完成');
  return true;
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始简单构建（无代码分割）\n');
  
  // 1. 清理构建目录
  cleanBuildDir();
  
  // 2. 执行构建
  const buildSuccess = runCommand('npm run build:prod', '生产环境构建');
  
  if (!buildSuccess) {
    console.error('\n❌ 构建失败');
    process.exit(1);
  }
  
  // 3. 检查构建结果
  const checkSuccess = checkBuildResult();
  
  if (!checkSuccess) {
    console.error('\n❌ 构建验证失败');
    process.exit(1);
  }
  
  console.log('\n🎉 简单构建完成！');
  console.log('💡 提示: 此构建使用了最简单的配置，避免了代码分割问题');
  console.log('📝 如需分析构建结果，请运行: npm run analyze');
}

// 运行构建
main();
