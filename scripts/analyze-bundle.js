#!/usr/bin/env node

/**
 * 构建产物分析脚本
 * 分析打包后的文件大小和分包情况
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 项目根目录
const projectRoot = path.resolve(__dirname, '..');
const distDir = path.join(projectRoot, 'dist');

/**
 * 格式化文件大小
 */
function formatSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件大小
 */
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * 递归获取目录下所有文件
 */
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      getAllFiles(filePath, fileList);
    } else {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

/**
 * 分析构建产物
 */
function analyzeBuild() {
  if (!fs.existsSync(distDir)) {
    console.log('❌ 构建目录不存在，请先运行 npm run build');
    return;
  }

  console.log('📊 构建产物分析\n');

  // 获取所有文件
  const allFiles = getAllFiles(distDir);
  
  // 按类型分类
  const filesByType = {
    js: [],
    css: [],
    fonts: [],
    images: [],
    other: []
  };

  let totalSize = 0;

  allFiles.forEach(filePath => {
    const relativePath = path.relative(distDir, filePath);
    const size = getFileSize(filePath);
    const ext = path.extname(filePath).toLowerCase();
    
    totalSize += size;

    const fileInfo = {
      path: relativePath,
      size: size,
      formattedSize: formatSize(size)
    };

    if (ext === '.js') {
      filesByType.js.push(fileInfo);
    } else if (ext === '.css') {
      filesByType.css.push(fileInfo);
    } else if (['.woff', '.woff2', '.ttf', '.eot'].includes(ext)) {
      filesByType.fonts.push(fileInfo);
    } else if (['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', '.ico'].includes(ext)) {
      filesByType.images.push(fileInfo);
    } else {
      filesByType.other.push(fileInfo);
    }
  });

  // 排序（按大小降序）
  Object.keys(filesByType).forEach(type => {
    filesByType[type].sort((a, b) => b.size - a.size);
  });

  console.log(`🎯 总体积: ${formatSize(totalSize)}\n`);

  // JavaScript 文件分析
  console.log('📦 JavaScript 文件:');
  if (filesByType.js.length > 0) {
    filesByType.js.forEach(file => {
      const sizePercent = ((file.size / totalSize) * 100).toFixed(1);
      console.log(`  ${file.formattedSize.padEnd(10)} (${sizePercent}%) ${file.path}`);
      
      // 标记大文件
      if (file.size > 500 * 1024) { // 500KB
        console.log(`    ⚠️  文件较大，建议进一步分割`);
      }
    });
    
    const jsTotal = filesByType.js.reduce((sum, file) => sum + file.size, 0);
    console.log(`  总计: ${formatSize(jsTotal)}\n`);
  } else {
    console.log('  无 JavaScript 文件\n');
  }

  // CSS 文件分析
  console.log('🎨 CSS 文件:');
  if (filesByType.css.length > 0) {
    filesByType.css.forEach(file => {
      const sizePercent = ((file.size / totalSize) * 100).toFixed(1);
      console.log(`  ${file.formattedSize.padEnd(10)} (${sizePercent}%) ${file.path}`);
    });
    
    const cssTotal = filesByType.css.reduce((sum, file) => sum + file.size, 0);
    console.log(`  总计: ${formatSize(cssTotal)}\n`);
  } else {
    console.log('  无 CSS 文件\n');
  }

  // 字体文件分析
  console.log('🔤 字体文件:');
  if (filesByType.fonts.length > 0) {
    filesByType.fonts.forEach(file => {
      console.log(`  ${file.formattedSize.padEnd(10)} ${file.path}`);
    });
    
    const fontsTotal = filesByType.fonts.reduce((sum, file) => sum + file.size, 0);
    console.log(`  总计: ${formatSize(fontsTotal)}\n`);
  } else {
    console.log('  无字体文件\n');
  }

  // 图片文件分析
  console.log('🖼️  图片文件:');
  if (filesByType.images.length > 0) {
    filesByType.images.forEach(file => {
      console.log(`  ${file.formattedSize.padEnd(10)} ${file.path}`);
    });
    
    const imagesTotal = filesByType.images.reduce((sum, file) => sum + file.size, 0);
    console.log(`  总计: ${formatSize(imagesTotal)}\n`);
  } else {
    console.log('  无图片文件\n');
  }

  // 分包效果分析
  console.log('📈 分包效果分析:');
  const chunks = filesByType.js.filter(file => file.path.includes('js/'));
  
  if (chunks.length > 0) {
    console.log(`  总共生成 ${chunks.length} 个 JavaScript 块`);
    
    const largeChunks = chunks.filter(chunk => chunk.size > 500 * 1024);
    if (largeChunks.length > 0) {
      console.log(`  ⚠️  发现 ${largeChunks.length} 个大块 (>500KB):`);
      largeChunks.forEach(chunk => {
        console.log(`    - ${chunk.path}: ${chunk.formattedSize}`);
      });
      console.log('  建议进一步优化这些大块的分割');
    } else {
      console.log('  ✅ 所有块都在合理大小范围内');
    }
  }

  // 优化建议
  console.log('\n💡 优化建议:');
  
  const jsTotal = filesByType.js.reduce((sum, file) => sum + file.size, 0);
  const jsPercent = (jsTotal / totalSize) * 100;
  
  if (jsPercent > 70) {
    console.log('  - JavaScript 占比较高，考虑更细粒度的代码分割');
  }
  
  if (filesByType.images.some(img => img.size > 100 * 1024)) {
    console.log('  - 发现大图片文件，建议压缩或使用 WebP 格式');
  }
  
  if (chunks.some(chunk => chunk.size > 1024 * 1024)) {
    console.log('  - 发现超大块，强烈建议进一步分割');
  }
  
  console.log('  - 考虑启用 gzip/brotli 压缩');
  console.log('  - 使用 CDN 加速静态资源加载');
}

// 运行分析
analyzeBuild();
