eval(
  (function (p, a, c, k, e, r) {
    e = function (c) {
      return (
        (c < 62 ? "" : e(parseInt(c / 62))) +
        ((c = c % 62) > 35 ? String.fromCharCode(c + 29) : c.toString(36))
      );
    };
    if ("0".replace(0, e) == 0) {
      while (c--) r[e(c)] = k[c];
      k = [
        function (e) {
          return r[e] || e;
        },
      ];
      e = function () {
        return "([246-9bce-mo-suvx-zA-Z]|[12]\\w)";
      };
      c = 1;
    }
    while (c--) if (k[c]) p = p.replace(new RegExp("\\b" + e(c) + "\\b", "g"), k[c]);
    return p;
  })(
    '8 1j(Z,10){7 11={};12(7 B G Z){h(Z.1I(B)){11[B]=Z[B]}}12(7 B G 10){h(10.1I(B)){11[B]=10[B]}}l 11}8 1k(1J){1K{l U.1L(1J)}1M(error){l p 1l()}}!(8(w,d){7 13;7 14;7 _={1m:K,V:[],};7 9={1N:"g-1n-1O-4",1P:"g-1O-4",1Q:"g-initialized",1R:"g-1o",1S:"g-1T",1U:"g-1V-1W-1X",1Y:"g-reset-1W-1X",AIHELP_SHOW_WITH_ENTRANCE_ID:"g-15-with-entrance-id",1Z:"g-L-C-D-M",20:"g-L-21-D-M",AIHELP_START_UNREAD_MESSAGE_COUNT_POLLING:"g-22-L-C-D-23",AIHELP_STOP_UNREAD_MESSAGE_COUNT_POLLING:"g-stop-L-C-D-23",24:"g-25-L-C-D",26:"g-25-L-21-D",H:"g-17-E-27",28:"g-17-N-btn-27",W:"g-1V-4",29:"g-1n-2a",2b:"g-2a-M",2c:"g-1o-result-M",2d:"g-15-single-faq",1p:"g-18-upload",2e:"g-O-2f",2g:"g-conversation-22",2h:"g-15-O",2i:"g-set-2j-C-D",1q:"g-1n-2j-C-D",};7 c={2k:"2l",2m:"userLogin",2n:"enterpriseAuth",1r:"sessionOpen",1s:"sessionClose",2o:"messageArrival",2p:"unreadTaskCount",2q:"logUpload",FORM_SUBMISSION:"formSubmission",2r:"urlClick",2s:"conversationStart",};7 19=8(e){12(7 n G c)h(c[n]===e)l!0;l!1};7 19=8(e){12(7 n G c)h(c[n]===e)l!0;l!1};7 o=8(c,2,M){_.V.forEach(8(t){t.c===c&&t.v(2,M)})};7 i=8(6){13.2t&&13.2t.postMessage(U.stringify({e:6.e,2:6.2}),"*")};7 2u=8(2){h(2.isSuccess){_.1m=1a}1t({x:K});o(c.2k,2)};7 2v=8(){7 f=P.I("f");7 x=f.q.F==="Q";i({e:9.H,2:{x:x,},})};7 2w=8(){i({e:9.1q,2:{2x:2y.getItem("2z")||"",},})};7 2A=8(2){2y.setItem("2z",2.2x)};7 1t=8(2){1u(8(){7 N=P.I("r");N.q.F=2.x?"Q":"1b"},300)};7 2B=8(2){i({e:9.W,2:2,})};7 2C=8(2){i({e:9.1P,2:2,})};7 2D=8(2E){7 1v=1k(2E);h(1l.2F(1v).2G>0){i({e:9.2b,2:1v,})}m{y p z("2H 2I 2J 2 E 1w G a 2K U 2L.");}};7 2M=8(2N){7 18=1k(2N);h(1l.2F(18).2G>0){i({e:9.1p,2:18,})}m{y p z("2H 2I 2J 2 E 1w G a 2K U 2L.");}};7 1x=8(2O){7 e,2;1K{7 1y=U.1L(2O.2);(e=1y.e),(2=1y.2)}1M(err){l}switch(e){k 9.1Q:2u(2);j;k 9.1Z:o(c.2o,2);j;k 9.20:o(c.2p,2);j;k 9.H:2v();j;k 9.28:1t(2);j;k 9.W:2B(w.R.4);j;k 9.29:o(c.2n,2,2D);j;k 9.2c:o(c.2m,2);j;k 9.1p:o(c.2q,2,2M);j;k 9.2e:o(c.2r,2);j;k 9.2g:o(c.2s,2);j;k 9.1N:2C({X:w.R.4.X,S:w.R.4.S,});j;k 9.1q:2w();j;k 9.2i:2A(2);j;default:j}};w.R={2P:"5.3.1",4:{1c:"",X:"",S:"",1d:"",J:{},1e:{},s:{},1f:{},1z:K,},additionalSupportFor(6){h(6==="cn"||6==="G"){b.4.1d=6}},1A:8(6){h(!6||!6.X||!6.1c){y p z("X & 1c E 1g");}h(P&&P.2Q){14&&clearTimeout(14);b.4=1j(b.4,6);7 f=d.I("f");h(!f){f=d.1B("2R");f.Y("id","f");f.q.F="Q";f.2S="f";7 r=d.1B("2R");r.Y("id","r");r.2S="AIHelpSupportClose";r.q.F="Q";f.1C(r);h(r.2T){r.2T("onclick",b.N)}m{r.2U("2f",b.N)}7 u=d.1B("17");u.id="ah-web-sdk-17";u.src="https://".1D(b.4.1c).1D(b.4.1d?"."+b.4.1d:"").1D("/webchatv5/#/");u.Y("role","Q");u.Y("frameborder","no");u.Y("border","0");u.q.width="1E%";u.q.height="1E%";13=u;f.1C(u);d.2Q.1C(f);w.removeEventListener("C",1x,!1);w.2U("C",1x,!1)}m{i({e:9.H,2:{x:1a,},});1u(8(){f.remove();w.R.1A(6)},1E)}}m{14=1u(8(){w.R.1A(6)},500)}b.4.1z=1a},registerAsyncEventListener:8(c,v){h(19(c)&&v){_.V.push({c:c,v:v})}},unRegisterAsyncEventListener:8(c,v){h(19(c)&&v){_.V=_.V.filter(8(t){l!(t.c===c&&t.v===v)})}},fetchUnreadMessageCount(){i({e:9.24,2:{},})},fetchUnreadTaskCount(){i({e:9.26,2:{},})},1o:8(6){h(!b.4.1z){y p z("Please perform 2l first");}h(A 6==="1h"&&A 6.T!=="undefined"){b.4.J=6;b.4.s=6.s||{}}m h(A 6==="1i"){b.4.J={T:6,}}m{y p z("T E 1g");}b.4.J.T=encodeURIComponent(b.4.J.T);i({e:9.1R,2:b.4.J,})},1T:8(){b.4.J={T:"",};b.4.s={};i({e:9.1S,2:b.4,})},updateUserInfo:8(s){b.4.s=1j(b.4.s,s);i({e:9.1U,2:b.4.s,})},resetUserInfo:8(){b.4.s={};i({e:9.1Y,2:{},})},updateSDKLanguage:8(S){b.4.S=S;i({e:9.W,2:b.4,})},15:8(6){h(A 6==="1h"&&6.1F){b.4.1e=6}m h(A 6==="1i"&&6!==""){b.4.1e={1F:6,}}m{y p z("1F E 1g");}h(!_.1m){y p z("Initialization 1w completed");}b.4.1f={};i({e:9.W,2:b.4,});7 f=d.I("f");f.q.F="1b";i({e:9.H,2:{x:K,},});o(c.1r,{c:c.1r,})},showSingleFAQ:8(6){h(A 6==="1h"&&6.1G){b.4.1f=6}m h(A 6==="1i"){b.4.1f={1G:6,}}m{y p z("1G E 1g");}b.4.1e={};i({e:9.2d,2:b.4,});7 f=d.I("f");f.q.F="1b";i({e:9.H,2:{x:K,},})},2V:8(6){7 4={};h(A 6==="1h"&&6.O){4.O=6.O;6.1H&&(4.1H=6.1H)}m h(A 6==="1i"){4.O=6}m{y p z("2V E missing parameter");}i({e:9.2h,2:4,})},N:8(){7 f=d.I("f");h(!f){l}f.q.F="Q";i({e:9.H,2:{x:1a,},});o(c.1s,{c:c.1s,})},isAIHelpShowing:8(){7 f=P.I("f");h(!f){l K}l f.q.F==="1b"},getSDKVersion:8(){l b.2P},}})(window,P);',
    [],
    182,
    "||data||config||params|var|function|postMessageType||this|eventType||type|AIHelpSupportBox|aihelp|if|sendMessage|break|case|return|else||eventReceived|new|style|AIHelpSupportCloseBtn|userConfig||ahIframe|eventHandler||isHidden|throw|Error|typeof|key|message|count|is|display|in|AIHELP_IFRAME_IS_HIDDEN|getElementById|loginConfig|false|unread|callback|close|url|document|none|AIHelpSupport|language|userId|JSON|apiEvents|AIHELP_UPDATE_CONFIG|appId|setAttribute|obj1|obj2|mergedObj|for|AIHelpSupportIframe|domLoadedTimer|show||iframe|log|checkEventType|true|block|domain|domainSuffix|apiConfig|faqConfig|required|object|string|mergeObjects|getJsonObject|Object|isInitialized|get|login|AIHELP_LOG_UPLOAD|AIHELP_GET_READ_MESSAGE_COUNT|SESSION_OPEN|SESSION_CLOSE|aihelpIframeCloseBtnHidden|setTimeout|token|not|postMessageCallback|payload|isRunAIHelpInitialize|initialize|createElement|appendChild|concat|100|entranceId|faqId|target|hasOwnProperty|jsonStr|try|parse|catch|AIHELP_GET_INIT_CONFIG|init|AIHELP_INIT_CONFIG|AIHELP_INITIALIZED|AIHELP_LOGIN|AIHELP_LOGOUT|logout|AIHELP_UPDATE_USER_INFO|update|user|info|AIHELP_RESET_USER_INFO|AIHELP_UNREAD_MESSAGE_COUNT_CALLBACK|AIHELP_UNREAD_TASK_COUNT_CALLBACK|task|start|polling|AIHELP_FETCH_UNREAD_MESSAGE_COUNT|fetch|AIHELP_FETCH_UNREAD_TASK_COUNT|hidden|AIHELP_IFRAME_CLOSE_BTN_HIDDEN|AIHELP_GET_AUTH|auth|AIHELP_AUTH_CALLBACK|AIHELP_LOGIN_RESULT_CALLBACK|AIHELP_SHOW_SINGLE_FAQ|AIHELP_URL_CLICK|click|AIHELP_CONVERSATION_START|AIHELP_SHOW_URL|AIHELP_SET_READ_MESSAGE_COUNT|read|INITIALIZATION|initialization|USER_LOGIN|ENTERPRISE_AUTH|MESSAGE_ARRIVAL|UNREAD_TASK_COUNT|LOG_UPLOAD|URL_CLICK|CONVERSATION_START|contentWindow|onAIHelpInitializedCallback|aihelpIframeIsHidden|sendReadMessageCount|rmc|localStorage|ah_rmc|setReadMessageCount|aihelpConfigPostMessage|aihelpInitConfigPostMessage|onEnterpriseAuthCallback|tokenJsonString|keys|length|The|provided|acknowledgement|valid|format|onUploadLog|logJsonString|event|sdkVersion|body|div|className|attachEvent|addEventListener|showURL".split(
      "|"
    ),
    0,
    {}
  )
);
