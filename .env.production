
# 接口前缀
VITE_API_URL=https://io.nustargame.com
#前端域名地址
VITE_WEB_URL = https://h5.nustaronline.vip

# 静态资源前缀
VITE_ASSETS_URL=https://nustar-static.nustargame.com/

# Gcash 小程序商城链接
VITE_GCASH_SHOP_URL=https://gcashapp.page.link?link=https://gcash.splashscreen/?redirect%3Dgcash%3A%2F%2Fcom.mynt.gcash%2Fapp%2F006300121300%3FappId%3D2170020216334562%2526page%253Dpages%252Fgame%252Flist%2526apn%253Dcom.globe.gcash.android%2526isi%253D520020791%2526ibi%253Dcom.globetel.gcash&apn=com.globe.gcash.android&isi=520020791&ibi=com.globetel.gcash

# 是否删除console
VITE_DROP_CONSOLE=false

# 是否启用 VConsole 调试工具（生产环境建议关闭）
VITE_ENABLE_VCONSOLE=false

# 是否启用gzip压缩或brotli压缩
# 可选: gzip | brotli | none
# 如果你需要多种形式，你可以用','来分隔
VITE_BUILD_COMPRESS = 'gzip'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = true
