# Promos 模块技术文档

## 📋 目录结构

```
src/views/promos/
├── Components/                    # 公共组件
├── index.vue                     # 主入口页面 (Promos + Tournament 标签页)
├── promos.vue                    # 活动列表组件
├── tournament.vue                # 锦标赛列表组件
├── promos-detail/                # 活动详情页面
│   ├── promo_0.vue              # 通用配置活动页面
│   ├── promo_1.vue              # 注册奖励活动
│   ├── promo_2.vue              # 特定活动页面
│   ├── promo_3.vue              # 每日反水活动
│   ├── promo_4.vue              # VIP 返水活动
│   ├── promo_5.vue              # Casino 排行榜
│   ├── promo_7.vue              # Jili 排行榜
│   ├── promo_8.vue              # 周统计活动
│   ├── promo_9.vue              # 特定活动
│   ├── promo_10.vue             # Daily Wins 活动
│   ├── promo_11.vue             # 夜间返水活动
│   ├── promo_12.vue             # 特定活动
│   ├── promo_14.vue             # 特定活动
│   ├── promo_15.vue             # 特定活动
│   ├── promo_16.vue             # 特定活动
│   ├── promo5_tip.vue           # Casino 排行榜说明
│   ├── promo7_tip.vue           # Jili 排行榜说明
│   ├── promo9_tip.vue           # 活动说明
│   └── promo_webview.vue        # WebView 页面
└── tournament-detail/            # 锦标赛详情页面
    ├── waiting.vue              # 等待开始状态
    ├── ongoing.vue              # 进行中状态
    ├── ended.vue                # 已结束状态
    └── rank-list.vue            # 排行榜页面
```

## 🎯 核心功能模块

### 1. 主入口页面 (`index.vue`)

**功能**: 双标签页入口，包含 Promos 和 Tournament 两个模块

**技术特性**:

- 使用 `van-tabs` 实现标签页切换
- 集成下拉刷新功能 (`van-pull-refresh`)
- 响应式数据管理
- 统一的数据获取和错误处理

**关键 API**:

```typescript
// 获取活动 Banner 数据
const getBannersData = async () => {
  const res = await getBanners({});
  banners.value = sortArray(res.banner, "sort");
};

// 获取锦标赛数据
const getTournamentDatas = async () => {
  const res = await getTournamentList();
  tournamentList.value = sortArray(res.list, "sort");
};
```

### 2. 活动列表组件 (`promos.vue`)

**功能**: 展示活动 Banner 列表，支持点击跳转

**技术特性**:

- 懒加载图片优化 (`ZImage` 组件)
- 统一跳转逻辑处理
- 空状态展示
- 响应式布局

**跳转逻辑**:

```typescript
// 根据 jump_type 判断跳转类型
// 0: 不跳转
// 1: 内跳 (内置打开网页)
// 2: 外跳 (另外打开网页)
// 4: 跳转 promos 详情页
// 7: 跳转后台配置活动详情页
```

### 3. 锦标赛组件 (`tournament.vue`)

**功能**: 展示锦标赛列表，支持状态管理和路由跳转

**技术特性**:

- 实时状态计算 (`getActivityStatus`)
- 时间格式化处理
- 状态徽章映射
- TypeScript 类型安全

**状态管理**:

```typescript
// 锦标赛状态枚举
const TournamentStatus = {
  0: "waiting", // 未开始
  1: "ongoing", // 进行中
  2: "ended", // 已结束
} as const;

// 状态计算函数
const getActivityStatus = (periodType: number, startTime: number, endTime: number): 0 | 1 | 2 => {
  // 根据 periodType 和时间计算当前状态
};
```

## 🔧 活动详情页面

### 通用配置页面 (`promo_0.vue`)

**功能**: 动态配置的活动页面，支持多种跳转逻辑

**配置参数**:

- `pictures_jump`: 跳转类型
  - 1: 跳转游戏列表页面
  - 2: 展示游戏列表面板
  - 3: 打开充值面板
  - 4: 内跳到指定路由
  - 5: 外跳到指定 URL

### 特定活动页面

#### 注册奖励 (`promo_1.vue`)

- 新用户注册奖励说明
- 静态内容展示
- 交易记录跳转

#### 每日反水 (`promo_3.vue`)

- 双标签页 (Today/Yesterday)
- 实时数据展示
- 投注跳转功能

#### VIP 返水 (`promo_4.vue`)

- VIP 状态检测
- 条件弹窗提示
- 数据对比展示

#### Casino 排行榜 (`promo_5.vue`)

- 前三名奖台展示
- 排行榜列表
- 个人排名显示
- 实时数据更新

## 🏆 锦标赛详情页面

### 等待状态 (`waiting.vue`)

**功能**: 锦标赛开始前的等待页面

**特性**:

- 倒计时显示
- 奖金展示
- 前三名奖台
- 进度条显示

### 进行中状态 (`ongoing.vue`)

**功能**: 锦标赛进行中的实时页面

**特性**:

- 实时排行榜
- 个人数据统计
- 继续游戏按钮
- 历史排名查看

### 结束状态 (`ended.vue`)

**功能**: 锦标赛结束后的结果页面

**特性**:

- 最终排名展示
- 获奖者信息
- 装饰动画效果
- 结果分享功能

### 排行榜页面 (`rank-list.vue`)

**功能**: 详细的排行榜展示页面

**特性**:

- 前三名特殊展示
- 完整排行榜列表
- 个人排名高亮
- 投注引导按钮

## 📡 API 接口

### 活动相关 API (`/api/promos.ts`)

```typescript
// 获取活动 Banner
export const getBanners = (data: any) =>
  http.post("/avt/api/banner/activity/list", data, { type: "formData" });

// 获取锦标赛列表
export const getTournamentList = (data = {}) =>
  http.post("/bmp-activity/v1/custom-leaderboard/tournament-list", data, { type: "formData" });

// 排行榜相关
export const casinoRank = (data = {}) =>
  http.post("/avt/api/rank/casino", data, { type: "formData" });

export const rankSolt = (data = {}) => http.post("/avt/api/rank/slot", data, { type: "formData" });

// 反水相关
export const dailyToday = (data = {}) =>
  http.post("/avt/api/activity/daily-rebate", data, { type: "formData" });

export const dailyYesterday = (data = {}) =>
  http.post("/avt/api/activity/daily-rebate-yesterday", data, { type: "formData" });

export const cashback = (data = {}) =>
  http.post("/avt/api/activity/jili_game/cashback", data, { type: "formData" });

// 锦标赛相关
export const registerTournament = (data = {}) =>
  http.post("/bmp-activity/v1/custom-leaderboard/register", data, { type: "formData" });

export const getLeaderboardList = (data = {}) =>
  http.post("/bmp-activity/v1/custom-leaderboard/leaderboard-list", data, { type: "formData" });
```

## 🛠 工具函数

### 跳转工具 (`/utils/JumpPromo.ts`)

```typescript
export interface PromoItem {
  home_page_jump?: number;
  jump_type?: string | number;
  activity_list?: string | number;
  url?: string;
}

// 活动跳转主函数
export const jumpPromo = (item: PromoItem, source: string = "promos") => {
  // 根据 jump_type 执行不同跳转逻辑
};

// 便捷方法
export const jumpBanner = (item: PromoItem) => jumpPromo(item, "banner");
export const jumpPromosItem = (item: PromoItem) => jumpPromo(item, "promos");
```

### 游戏跳转 (`/utils/JumpGame.ts`)

```typescript
// 游戏跳转主函数
export const jumpGame = async (data: { id: number; [key: string]: any }) => {
  // KYC 验证
  // 游戏登录
  // 跳转处理
};

// 异常处理
export const handleException = (response) => {
  // 维护状态处理
  // 错误码处理
  // 用户提示
};
```

## 🎨 样式规范

### 设计系统

- **主色调**: 紫色渐变 (`#6C5CE7` → `#A29BFE`)
- **辅助色**: 金色 (`#FFD700`)、绿色 (`#00CEC9`)
- **字体**: Inter, PingFang SC
- **圆角**: 统一使用 `border-radius: 20px`
- **阴影**: `box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2)`

### 响应式设计

- 移动端优先设计
- 使用 `vw`、`vh` 单位
- 弹性布局 (`flexbox`)
- 网格布局 (`grid`)

## 🔄 状态管理

### 全局状态

- `useGlobalStore`: 用户信息、VIP 状态
- `useDepositStore`: 充值面板状态
- `useAutoPopMgrStore`: 弹窗管理

### 本地状态

- 活动数据缓存
- 排行榜数据
- 倒计时状态
- 加载状态

## 🚀 性能优化

### 图片优化

- 懒加载 (`ZImage` 组件)
- 占位图系统
- 渐进式加载

### 数据优化

- API 请求缓存
- 分页加载
- 防抖处理

### 代码优化

- TypeScript 类型安全
- 组件复用
- 工具函数提取

## 📱 移动端适配

### 交互优化

- 触摸友好的按钮尺寸
- 滑动手势支持
- 下拉刷新
- 无限滚动

### 性能优化

- 虚拟滚动
- 图片懒加载
- 组件按需加载
- 内存管理

## 🔐 安全考虑

### 数据安全

- 用户 ID 脱敏显示
- API 请求加密
- 敏感信息保护

### 权限控制

- 登录状态验证
- VIP 权限检查
- KYC 验证集成

## 📊 监控与分析

### 错误监控

- API 请求失败处理
- 组件渲染错误捕获
- 用户行为异常监控

### 性能监控

- 页面加载时间
- API 响应时间
- 用户交互延迟

## 🧩 组件复用指南

### 公共组件

#### ZImage 组件

```vue
<ZImage :src="imageUrl" :lazyLoad="true" fit="cover" type="promos" />
```

#### IconCoin 组件

```vue
<IconCoin :size="20" />
```

#### ZPage 组件

```vue
<ZPage
  :request="initData"
  backgroundColor="#6C5CE7"
  :narBarStyle="{ backgroundColor: '#6C5CE7', color: '#fff' }"
>
  <template #right>
    <van-icon name="question-o" @click="showHelp" />
  </template>
</ZPage>
```

### 数据格式规范

#### 活动数据结构

```typescript
interface PromoItem {
  id: string | number;
  home_page_banner: string; // Banner 图片路径
  jump_type: string | number; // 跳转类型
  activity_list: string | number; // 活动类型
  url?: string; // 跳转 URL
  sort: number; // 排序权重
  picture_details_title?: string; // 详情标题
  picture_details?: string; // 详情内容
  button_text?: string; // 按钮文本
  pictures_jump?: number; // 详情页跳转类型
  game_type?: string; // 游戏类型
}
```

#### 锦标赛数据结构

```typescript
interface Tournament {
  id: string | number;
  banner_image: string; // Banner 图片
  total_price: number; // 总奖金
  start_time: number; // 开始时间 (时间戳)
  end_time: number; // 结束时间 (时间戳)
  period_type: number; // 时间类型 (0: 时间段, 1: 全天, 2: 日期范围)
  match_user_count: number; // 参与人数
  registration_mode: number; // 报名模式 (0: 自动, 1: 手动)
  registration_rule: number; // 报名规则 (0: 无限制, 1: 充值, 2: 投注)
  status?: string; // 计算后的状态
  rangeTimes?: string; // 格式化时间范围
  entryMode?: string; // 报名模式文本
  entryRule?: string; // 报名规则文本
  badgeImage?: string; // 状态徽章图片
}
```

#### 排行榜数据结构

```typescript
interface RankItem {
  rank: number; // 排名
  player_id: string; // 玩家 ID (脱敏)
  total_bet_amount: string; // 总投注额
  award: string; // 奖励金额
  avatar?: string; // 头像 URL
  isMe?: boolean; // 是否为当前用户
}

interface RankResponse {
  list: {
    rank: RankItem[]; // 排行榜列表
    bet_amount: string; // 个人投注额
  };
  user: {
    rank: number; // 个人排名
    award: string; // 个人奖励
  };
}
```

## 🔄 开发工作流

### 1. 新增活动页面流程

1. **创建页面文件**

   ```bash
   # 在 promos-detail 目录下创建新文件
   touch src/views/promos/promos-detail/promo_X.vue
   ```

2. **页面模板结构**

   ```vue
   <template>
     <XPage :navBarStyle="{ background: '#color', color: '#fff' }">
       <template #right>
         <van-icon name="question-o" @click="showHelp" />
       </template>

       <div class="promo-container">
         <!-- 页面内容 -->
       </div>
     </XPage>
   </template>

   <script setup lang="ts">
   import { ref, onMounted } from "vue";
   import { useRouter } from "vue-router";

   defineOptions({ name: "PromoX" });

   const router = useRouter();

   // 页面逻辑
   </script>

   <style lang="scss" scoped>
   .promo-container {
     // 样式定义
   }
   </style>
   ```

3. **配置路由跳转**
   - 在后台配置 `activity_list` 对应的数字
   - 跳转逻辑会自动匹配到对应页面

### 2. 锦标赛状态页面开发

1. **状态判断逻辑**

   ```typescript
   // 在 tournament.vue 中的状态计算
   const getActivityStatus = (
     periodType: number,
     startTime: number,
     endTime: number
   ): 0 | 1 | 2 => {
     // 返回对应状态码
   };
   ```

2. **页面跳转配置**
   ```typescript
   // 根据状态跳转到对应页面
   switch (tournament.status) {
     case TournamentStatus[0]: // waiting
       router.push("/promos/tournament-waiting");
       break;
     case TournamentStatus[1]: // ongoing
       router.push("/promos/tournament-ongoing");
       break;
     default: // ended
       router.push("/promos/tournament-ended");
       break;
   }
   ```

## 🎯 最佳实践

### 1. 代码规范

#### 组件命名

- 使用 PascalCase: `PromoDetail`, `TournamentList`
- 文件名使用 kebab-case: `promo-detail.vue`, `tournament-list.vue`

#### 函数命名

- 使用 camelCase: `getActivityStatus`, `formatPrize`
- 事件处理函数使用 `handle` 前缀: `handleTournamentClick`

#### 常量定义

- 使用 UPPER_SNAKE_CASE: `TOURNAMENT_STATUS`, `ENTRY_MODES`
- 使用 `as const` 断言提供类型安全

### 2. 性能优化

#### 图片懒加载

```vue
<ZImage :lazyLoad="index > 5" // 前5张立即加载，后续懒加载 :src="imageUrl" />
```

#### API 请求优化

```typescript
// 使用 Promise.all 并行请求
const [bannersRes, tournamentsRes] = await Promise.all([getBanners({}), getTournamentList({})]);
```

#### 计算属性缓存

```typescript
// 使用 computed 缓存复杂计算
const tournaments = computed(() => {
  return props.tournamentList.map((tournament) => ({
    ...tournament,
    status: getActivityStatus(tournament.period_type, tournament.start_time, tournament.end_time),
  }));
});
```

### 3. 错误处理

#### API 错误处理

```typescript
const getBannersData = async () => {
  loading.value = true;
  try {
    const res = await getBanners({});
    banners.value = res?.banner ? sortArray(res.banner, "sort") : [];
  } catch (error) {
    console.error("获取活动数据失败:", error);
    banners.value = [];
    // 可选：显示错误提示
  } finally {
    loading.value = false;
  }
};
```

#### 组件错误边界

```vue
<template>
  <div v-if="error" class="error-state">
    <p>加载失败，请重试</p>
    <button @click="retry">重试</button>
  </div>
  <div v-else>
    <!-- 正常内容 -->
  </div>
</template>
```

## 🧪 测试指南

### 1. 单元测试

#### 工具函数测试

```typescript
// 测试状态计算函数
describe("getActivityStatus", () => {
  it("should return 0 for waiting status", () => {
    const result = getActivityStatus(2, futureTime, futureTime + 3600);
    expect(result).toBe(0);
  });
});
```

#### 组件测试

```typescript
// 测试组件渲染
describe("Tournament.vue", () => {
  it("should render tournament list", () => {
    const wrapper = mount(Tournament, {
      props: { tournamentList: mockData },
    });
    expect(wrapper.find(".activity-card")).toBeTruthy();
  });
});
```

### 2. 集成测试

#### API 集成测试

```typescript
// 测试 API 调用
describe("Promos API", () => {
  it("should fetch banners successfully", async () => {
    const result = await getBanners({});
    expect(result).toHaveProperty("banner");
  });
});
```

### 3. E2E 测试

#### 用户流程测试

```typescript
// 测试完整用户流程
describe("Promos User Flow", () => {
  it("should navigate from promos list to detail", () => {
    cy.visit("/promos");
    cy.get(".promos-item").first().click();
    cy.url().should("include", "/promos/promo_");
  });
});
```

## 📚 扩展开发

### 1. 新功能开发指南

#### 添加新的活动类型

1. 创建对应的 `promo_X.vue` 文件
2. 实现页面逻辑和样式
3. 在后台配置对应的 `activity_list` 值
4. 测试跳转逻辑

#### 添加新的锦标赛状态

1. 扩展 `TournamentStatus` 枚举
2. 更新 `getActivityStatus` 函数逻辑
3. 创建对应的状态页面
4. 配置路由跳转

### 2. 国际化支持

#### 文本国际化

```typescript
// 使用 i18n
const { t } = useI18n();

// 模板中使用
<h1>{{ t('promos.title') }}</h1>
```

#### 时间格式化

```typescript
// 根据语言环境格式化时间
const formatTime = (timestamp: number, locale: string = "en-US") => {
  return new Date(timestamp * 1000).toLocaleDateString(locale);
};
```

这份技术文档涵盖了 Promos 模块的完整架构、功能实现、API 接口、工具函数、最佳实践和扩展指南，为开发团队提供了全面的技术参考和开发指导。
