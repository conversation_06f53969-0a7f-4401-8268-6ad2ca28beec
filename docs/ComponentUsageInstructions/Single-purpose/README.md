# Single-purpose 组件文档

本目录包含项目中所有 Single-purpose 组件的文档。这些组件专为特定用例设计，提供专门的功能。

## 可用组件

1. [EnvelopeButton](./EnvelopeButton.md) - 红包奖励按钮，带动画效果
2. [TurntableButton](./TurntableButton.md) - 浮动转盘/抽奖按钮
3. [XNumberRoller](./XNumberRoller.md) - 数字滚动显示组件
4. [XTextRoller](./XTextRoller.md) - 奖品公告文本滚动组件
5. [Xpage](./Xpage.md) - 页面容器，带导航和滚动处理

## 组件分类

### 交互按钮

- **EnvelopeButton**: 红包交互功能
- **TurntableButton**: 转盘游戏入口

### 显示组件

- **XNumberRoller**: 数字动画过渡
- **XTextRoller**: 滚动文本公告

### 布局组件

- **Xpage**: 全页面容器，带导航

## 使用指南

所有 Single-purpose 组件位于 `src/components/Single-purpose/` 目录下，专为特定场景设计。它们包含内置动画、状态管理集成和响应式设计。

详细的使用说明、API 文档和示例，请参考各个组件的文档文件。

## 导入路径

```javascript
import ComponentName from "@/components/Single-purpose/ComponentName.vue";
```

## 通用特性

- **响应式设计**: 所有组件都针对移动设备优化
- **动画支持**: 内置 CSS 动画和过渡效果
- **状态管理**: 与 Pinia 状态管理集成（如适用）
- **触摸优化**: 针对移动设备触摸交互优化
