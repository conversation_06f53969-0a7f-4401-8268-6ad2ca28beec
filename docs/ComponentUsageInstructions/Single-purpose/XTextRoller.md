# XTextRoller 组件

## 概述

`XTextRoller` 是一个专门的滚动文本组件，用于显示奖品公告和获奖者信息。它提供流畅的垂直滚动动画，具有自动数据刷新功能，专为转盘/抽奖游戏显示优化。

## 文件位置

```
src/components/Single-purpose/XTextRoller.vue
```

## 功能特性

- **流畅滚动**: 带 CSS 过渡的垂直文本滚动
- **自动刷新**: 到达列表末尾时自动获取数据
- **可配置字段**: 可自定义的数据字段映射
- **响应式设计**: 针对移动端显示优化
- **性能优化**: 高效的 DOM 更新和内存管理
- **视觉格式化**: 内置的用户、金额和时间显示样式

## 使用方法

### 基础实现

```vue
<template>
  <XTextRoller :rollerList="winnersList" :getMore="fetchMoreWinners" />
</template>

<script setup>
import { ref } from "vue";
import XTextRoller from "@/components/Single-purpose/XTextRoller.vue";

const winnersList = ref([
  {
    user_id: "Player123",
    prize_amount: "1000",
    prize_time: "12:34",
  },
  // ... 更多项目
]);

const fetchMoreWinners = async () => {
  // 获取额外的获奖者数据
  const newData = await api.getWinners();
  winnersList.value.push(...newData);
};
</script>
```

### 自定义字段映射

```vue
<template>
  <XTextRoller :rollerList="customData" :option="customOptions" :getMore="loadMoreData" />
</template>

<script setup>
import XTextRoller from "@/components/Single-purpose/XTextRoller.vue";

const customData = ref([
  {
    username: "Alice",
    winAmount: "5000",
    timestamp: "14:25",
  },
]);

const customOptions = {
  user: "username",
  amount: "winAmount",
  time: "timestamp",
};

const loadMoreData = async () => {
  // 自定义数据加载逻辑
};
</script>
```

## 属性 (Props)

| 属性         | 类型       | 必需 | 默认值     | 描述                   |
| ------------ | ---------- | ---- | ---------- | ---------------------- |
| `rollerList` | `Array`    | 否   | `[]`       | 要显示的数据项数组     |
| `option`     | `Object`   | 否   | 见下方     | 字段映射配置           |
| `getMore`    | `Function` | 否   | `() => {}` | 获取更多数据的回调函数 |

### 默认选项对象

```javascript
{
  user: "user_id",      // 用户标识符的字段名
  amount: "prize_amount", // 奖金金额的字段名
  time: "prize_time"     // 时间显示的字段名
}
```

## 事件 (Events)

该组件不发出自定义事件。数据更新通过 `getMore` 回调函数处理。

## 行为特性

### 滚动机制

1. **初始设置**: 挂载后计算项目高度（2 秒延迟）
2. **自动滚动**: 每 1.5 秒移动到下一项
3. **过渡**: 1 秒 CSS 过渡，实现流畅移动
4. **数据刷新**: 到达最后 4 项时触发

### 数据管理

- **连续显示**: 同时显示 2 项（总高度 40px）
- **智能刷新**: 在到达末尾前自动获取更多数据
- **无缝体验**: 数据加载期间无中断

### 动画时间

- **滚动间隔**: 项目间 1.5 秒
- **过渡持续时间**: 1 秒流畅移动
- **刷新延迟**: 到达触发点后 500ms

## 技术实现

### 核心滚动逻辑

```javascript
const startScroll = () => {
  timer = setInterval(() => {
    isAnimating.value = true;
    currentIndex++;
    scrollTop.value = currentIndex * itemHeight.value;

    // 接近末尾时触发数据刷新
    if (currentIndex === props.rollerList.length - 4) {
      setTimeout(() => {
        clearInterval(timer);
        timer = null;
        isAnimating.value = false;
        getMoreData();
      }, 500);
    }
  }, 1500);
};
```

### 高度计算

```javascript
onMounted(() => {
  setTimeout(() => {
    const item = boxRef.value?.querySelector(".scroll-item");
    if (item) {
      itemHeight.value = item.offsetHeight;
      startScroll();
    }
  }, 2000);
});
```

### 数据刷新处理器

```javascript
const getMoreData = async () => {
  await props.getMore?.();
  // 数据更新后重新开始滚动
  if (currentIndex < props.rollerList.length - 4) {
    startScroll();
  }
};
```

## 样式设计

### 容器结构

```css
.scroll-board {
  height: 40px; /* 显示两行 */
  overflow: hidden;
  border-radius: 8px;
  padding: 2px 0;
  font-size: 10px;
  margin-top: 6px;
}
```

### 项目布局

```css
.scroll-item {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  line-height: 20px;
  color: #fff;
  padding: 0 8px;
  font-weight: bold;
  gap: 2px;
}
```

### 视觉元素

- **用户显示**: 标准白色文本
- **金额显示**: 绿色 (#00f570) 带 ₱ 符号
- **时间显示**: 标准白色文本
- **背景**: 透明，带圆角

## 性能考虑

### 优化功能

- **Will-change**: CSS 属性用于优化变换
- **高效定时器**: 正确的清理和管理
- **最少 DOM**: 仅渲染可见项目
- **智能刷新**: 防止不必要的数据获取

### 内存管理

```javascript
onBeforeUnmount(() => {
  clearInterval(timer);
  timer = null;
});
```

## 集成要求

### 数据结构

确保您的数据遵循预期格式：

```javascript
const winnerData = [
  {
    user_id: "string", // 用户标识符
    prize_amount: "string", // 奖金金额（将被格式化）
    prize_time: "string", // 时间显示
  },
  // ... 更多项目
];
```

### API 集成

实现 `getMore` 函数以获取额外数据：

```javascript
const fetchMoreWinners = async () => {
  try {
    const response = await api.getRecentWinners({
      offset: winnersList.value.length,
      limit: 20,
    });
    winnersList.value.push(...response.data);
  } catch (error) {
    console.error("获取获奖者失败:", error);
  }
};
```

## 浏览器兼容性

- **现代浏览器**: 完全支持 CSS 变换和过渡
- **移动设备**: 针对移动端显示优化
- **性能**: 在大多数设备上流畅动画

## 故障排除

### 常见问题

1. **滚动未开始**

   - 检查数据数组是否有足够项目（>4）
   - 验证组件挂载和高度计算
   - 确保 2 秒初始化延迟完成

2. **数据未刷新**

   - 验证 `getMore` 函数实现
   - 检查 API 响应和数据结构
   - 确保正确的 async/await 处理

3. **动画卡顿**

   - 检查 CSS 过渡支持
   - 验证 will-change 属性应用
   - 确保没有冲突的 CSS 规则

4. **显示不正确**
   - 验证 `option` 属性中的数据字段映射
   - 检查数据结构是否匹配预期格式
   - 确保金额的正确字符串格式化

## 使用建议

### 最佳实践

- 确保数据列表有足够的项目以支持连续滚动
- 在生产环境中优化 API 响应时间
- 测试不同数据量下的性能表现

### 注意事项

- 组件专为转盘游戏设计，适用于获奖者公告
- 需要稳定的数据源以确保连续滚动
- 建议在游戏或活动页面的侧边栏中使用
