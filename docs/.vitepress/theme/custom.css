/* VitePress 自定义主题样式 */

:root {
  --vp-c-brand-1: #3c82f6;
  --vp-c-brand-2: #2563eb;
  --vp-c-brand-3: #1d4ed8;
}

/* 确保页面可以正常滚动 */
html,
body {
  overflow: auto !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* 确保主容器可以滚动 */
.VPApp {
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* 确保内容区域可以滚动 */
.VPContent {
  overflow: visible !important;
  height: auto !important;
}

/* 确保文档容器可以滚动 */
.VPDoc {
  overflow: visible !important;
  height: auto !important;
}

/* 移除任何可能阻止滚动的样式 */
* {
  max-height: none !important;
}

/* 确保页面内容完整显示 */
.VPDoc .container {
  height: auto !important;
  min-height: auto !important;
}

/* 确保内容区域可以完整显示 */
.content {
  height: auto !important;
  overflow: visible !important;
}

/* 重置任何可能的固定高度 */
.VPLayout {
  height: auto !important;
  min-height: 100vh !important;
}
