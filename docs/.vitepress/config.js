// docs/.vitepress/config.js
export default {
  // 文档站点标题和描述
  title: "Vue3 H5 项目文档",
  description: "Vue3 移动端项目组件使用指南和技术文档",

  // 站点部署的基础路径（如果部署在子路径下，如 GitHub Pages 的 repo 名称）
  //   base: "/web-vue-h5/", // 非子路径部署可省略

  themeConfig: {
    // 导航栏配置
    nav: [
      { text: "首页", link: "/" },
      { text: "组件文档", link: "/ComponentUsageInstructions/Single-purpose/README" },
      { text: "技术文档", link: "/technical/" },
    ],

    // 侧边栏配置
    sidebarMenuLabel: "菜单",
    returnToTopLabel: "返回顶部",
    darkModeSwitchLabel: "深色模式",

    // 侧边栏配置（按目录分组）
    sidebar: {
      "/ComponentUsageInstructions/": [
        {
          text: "Single-purpose 组件",
          items: [
            { text: "组件概览", link: "/ComponentUsageInstructions/Single-purpose/README" },
            {
              text: "EnvelopeButton 红包按钮",
              link: "/ComponentUsageInstructions/Single-purpose/EnvelopeButton",
            },
            {
              text: "TurntableButton 转盘按钮",
              link: "/ComponentUsageInstructions/Single-purpose/TurntableButton",
            },
            {
              text: "XNumberRoller 数字滚动",
              link: "/ComponentUsageInstructions/Single-purpose/XNumberRoller",
            },
            {
              text: "XTextRoller 文本滚动",
              link: "/ComponentUsageInstructions/Single-purpose/XTextRoller",
            },
            { text: "Xpage 页面容器", link: "/ComponentUsageInstructions/Single-purpose/Xpage" },
          ],
        },
      ],
      "/technical/": [
        {
          text: "技术文档",
          items: [
            { text: "技术文档概览", link: "/technical/" },
            { text: "PROMOS 技术文档", link: "/PROMOS_TECHNICAL_DOCUMENTATION" },
            { text: "支付密码对话框修复", link: "/Payment-Password-Dialog-Fix" },
            { text: "VConsole 配置", link: "/VConsole-Configuration" },
          ],
        },
      ],
    },

    // 搜索配置
    search: {
      provider: "local",
    },

    // 页脚配置
    footer: {
      message: "Vue3 H5 项目文档",
      //   copyright: "Copyright © 2024",
    },

    // 编辑链接配置
    editLink: {
      /*  pattern: "https://github.com/your-username/web-vue-h5/edit/main/docs/:path",
      text: "在 GitHub 上编辑此页", */
    },

    // 最后更新时间
    lastUpdated: {
      text: "最后更新于",
      formatOptions: {
        dateStyle: "short",
        timeStyle: "medium",
      },
    },

    // 大纲配置
    outline: {
      level: [2, 3, 4],
      label: "页面导航",
    },

    // 文档页脚
    docFooter: {
      prev: "上一页",
      next: "下一页",
    },
  },

  // 头部配置
  head: [
    ["meta", { name: "viewport", content: "width=device-width, initial-scale=1.0" }],
    ["meta", { name: "theme-color", content: "#3c82f6" }],
  ],

  // Markdown 配置
  markdown: {
    lineNumbers: true,
  },
};
