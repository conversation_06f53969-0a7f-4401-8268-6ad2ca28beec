---
home: true
heroText: Vue3 H5 项目文档
tagline: 移动端组件库使用指南和技术文档
actions:
  - text: 查看组件
    link: /ComponentUsageInstructions/Single-purpose/README
    type: primary
  - text: 技术文档
    link: /PROMOS_TECHNICAL_DOCUMENTATION
    type: secondary
---

## 项目特点

- 基于 Vue 3 Composition API
- 专为移动端 H5 应用设计
- 丰富的专用组件库
- 完善的动画和交互效果

## 组件分类

### Single-purpose 专用组件

- **[EnvelopeButton](/ComponentUsageInstructions/Single-purpose/EnvelopeButton)** - 红包奖励按钮
- **[TurntableButton](/ComponentUsageInstructions/Single-purpose/TurntableButton)** - 转盘游戏按钮
- **[XNumberRoller](/ComponentUsageInstructions/Single-purpose/XNumberRoller)** - 数字滚动动画
- **[XTextRoller](/ComponentUsageInstructions/Single-purpose/XTextRoller)** - 文本滚动公告
- **[Xpage](/ComponentUsageInstructions/Single-purpose/Xpage)** - 页面容器组件

## 技术文档

- **[PROMOS 技术文档](/PROMOS_TECHNICAL_DOCUMENTATION)** - 项目技术架构说明
- **[支付密码对话框修复](/Payment-Password-Dialog-Fix)** - 支付功能修复记录
- **[VConsole 配置](/VConsole-Configuration)** - 调试工具配置指南

## 快速开始

1. 查看 [组件概览](/ComponentUsageInstructions/Single-purpose/README) 了解所有可用组件
2. 选择需要的组件查看详细使用文档
3. 参考代码示例进行集成
