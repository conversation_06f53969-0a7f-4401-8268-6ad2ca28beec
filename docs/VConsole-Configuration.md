# VConsole 配置说明

## 🎯 问题描述

在执行 `npm run start:pre` 命令时，VConsole 调试工具仍然被加载，即使在 `.env.pre` 文件中设置了 `VITE_ENABLE_VCONSOLE=false`。

## 🔍 问题原因

原来的 `VConsoleManager.ts` 中的 `shouldEnable()` 方法逻辑有问题：

```typescript
// 原来的逻辑（有问题）
private shouldEnable(): boolean {
  // 开发环境 - 这里会无条件返回 true
  if (import.meta.env.DEV) {
    return true;
  }
  
  // 环境变量控制 - 永远不会执行到这里
  if (import.meta.env.VITE_ENABLE_VCONSOLE === "true") {
    return true;
  }
  // ...
}
```

问题在于：
1. `import.meta.env.DEV` 在开发模式下总是 `true`
2. 环境变量检查被开发环境检查覆盖了
3. 即使设置了 `VITE_ENABLE_VCONSOLE=false`，VConsole 仍然会被启用

## ✅ 解决方案

### 1. 修改 VConsoleManager.ts 逻辑

优化 `shouldEnable()` 方法的判断顺序：

```typescript
private shouldEnable(): boolean {
  // 优先检查环境变量控制
  if (import.meta.env.VITE_ENABLE_VCONSOLE === "true") {
    return true;
  }
  
  // 如果环境变量明确设置为 false，则不启用（即使是开发环境）
  if (import.meta.env.VITE_ENABLE_VCONSOLE === "false") {
    return false;
  }

  // 开发环境且未明确设置环境变量时才启用
  if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_VCONSOLE === undefined) {
    return true;
  }

  // URL 参数控制：?vconsole=true
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get("vconsole") === "true") {
    return true;
  }

  // localStorage 控制（方便生产环境临时调试）
  if (localStorage.getItem("enable_vconsole") === "true") {
    return true;
  }

  return false;
}
```

### 2. 环境变量配置

确保各环境文件中的配置正确：

#### `.env.development`
```bash
# 开发环境默认启用（可选，不设置也会启用）
VITE_ENABLE_VCONSOLE=true
```

#### `.env.pre`
```bash
# 预发布环境不启用
VITE_ENABLE_VCONSOLE=false
```

#### `.env.production`
```bash
# 生产环境不启用
VITE_ENABLE_VCONSOLE=false
```

## 🎮 使用方式

### 1. 通过环境变量控制

```bash
# 启用 VConsole
npm run start:pre  # 如果 .env.pre 中设置 VITE_ENABLE_VCONSOLE=true

# 禁用 VConsole
npm run start:pre  # 如果 .env.pre 中设置 VITE_ENABLE_VCONSOLE=false
```

### 2. 通过 URL 参数临时启用

```bash
# 即使环境变量设置为 false，也可以通过 URL 参数临时启用
http://localhost:3000?vconsole=true
```

### 3. 通过 localStorage 临时启用

```javascript
// 在浏览器控制台中执行
localStorage.setItem('enable_vconsole', 'true');
// 然后刷新页面

// 禁用
localStorage.removeItem('enable_vconsole');
```

### 4. 通过代码动态控制

```javascript
// 动态启用
window.vConsoleManager.enableForDebug();

// 动态禁用
window.vConsoleManager.disable();

// 切换主题
window.toggleVConsoleTheme();

// 显示/隐藏
window.toggleVConsole();

// 清除日志
window.clearVConsole();
```

## 📋 优先级说明

VConsole 启用的判断优先级（从高到低）：

1. **环境变量 `VITE_ENABLE_VCONSOLE=true`** - 强制启用
2. **环境变量 `VITE_ENABLE_VCONSOLE=false`** - 强制禁用
3. **URL 参数 `?vconsole=true`** - 临时启用
4. **localStorage `enable_vconsole=true`** - 临时启用
5. **开发环境且未设置环境变量** - 默认启用
6. **其他情况** - 禁用

## 🔧 验证方法

### 1. 检查环境变量

在浏览器控制台中执行：

```javascript
console.log('VITE_ENABLE_VCONSOLE:', import.meta.env.VITE_ENABLE_VCONSOLE);
console.log('DEV:', import.meta.env.DEV);
console.log('MODE:', import.meta.env.MODE);
```

### 2. 检查 VConsole 状态

```javascript
console.log('VConsole Manager:', window.vConsoleManager);
console.log('VConsole Instance:', window.vConsole);
console.log('Is Ready:', window.vConsoleManager?.isReady());
```

## 🎉 测试结果

现在执行 `npm run start:pre` 命令时：

- ✅ VConsole 不会被加载（因为 `.env.pre` 中设置了 `VITE_ENABLE_VCONSOLE=false`）
- ✅ 控制台会显示 "VConsole 未启用"
- ✅ 可以通过 URL 参数 `?vconsole=true` 临时启用
- ✅ 可以通过 localStorage 临时启用

## 📝 注意事项

1. **环境变量优先级最高**：明确设置的环境变量会覆盖开发环境的默认行为
2. **临时启用方式**：URL 参数和 localStorage 可以在任何环境下临时启用 VConsole
3. **类型安全**：修复了 TypeScript 类型问题，确保代码的类型安全性
4. **向后兼容**：保持了所有原有的功能和 API

现在您可以放心地使用 `npm run start:pre` 命令，VConsole 将不会被加载！
