import { useWithdrawStore } from '@/stores/withdraw'
import type { WithdrawAccount } from '@/stores/withdraw'

export { CHANNEL_TYPES, CHANNEL_NAMES } from '@/stores/withdraw'
export type { WithdrawAccount }

export function useWithdrawAccounts() {
    const store = useWithdrawStore()

    return {
        accounts: store.accounts,
        currentNum: store.currentNum,
        totalNum: store.totalNum,
        isSelectionMode: store.isSelectionMode,
        selectedAccount: store.selectedAccount,
        isLoading: store.isLoading,
        setSelectionMode: store.setSelectionMode,
        getAccounts: store.getAccounts,
        addAccount: store.addAccount,
        updateAccount: store.updateAccount,
        selectAccount: store.selectAccount
    }
} 