// 复制功能
import { ref } from "vue";
import { showToast } from "vant";

export function useClipboard() {
  const isCopied = ref(false);

  const copy = async (text: string) => {
    try {
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text);
        showToast("Copied");
        console.log("Copied", text);
      } else {
        throw new Error("使用备用方法");
      }
      isCopied.value = true;
      const timer = setTimeout(() => {
        isCopied.value = false;
        return () => {
          clearTimeout(timer);
        };
      }, 1500);
      return true;
    } catch {
      // 降级方案
      const textarea = document.createElement("textarea");
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      const success = document.execCommand("copy");
      document.body.removeChild(textarea);
      showToast("Copied");
      console.log("Copied", success, textarea.value);
      return success;
    }
  };

  return { isCopied, copy };
}
