<script setup lang="ts">
import { getMessageList } from "@/api/message";
import { ref } from "vue";
import { useRoute } from "vue-router";
import { formatDateToEventString } from "@/utils/core/tools";
const route = useRoute();

const data = ref({});
const { id } = route.params;

const init = async () => {
  const list = await getMessageList({});
  const curMessage = list.find((item) => item.id == id);
  data.value = curMessage;
};
</script>

<template>
  <ZPage :request="init" class="page">
    <div class="wrap">
      <div class="title">{{ data.title }}</div>
      <div class="text">{{ data.text }}</div>
      <div class="date">{{ formatDateToEventString(data.updated_at) }}</div>
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.wrap {
  background-color: #fff;
  height: 100%;
  padding: 12px;

  .title {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 150%;
    /* 24px */
  }

  .text {
    margin-top: 6px;
    color: #506477;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
  }

  .date {
    color: #506477;
    font-size: 12px;
    margin-top: 6px;
  }
}
</style>
