<template>
  <ZPage :request="init">
    <template #right>
      <ZIcon type="icon-15qingkong-1" color="" @click="readAllMesage"></ZIcon>
      <!-- <ZIcon type="icon-15qingkong-1" color="" @click="clearAllMesage"></ZIcon> -->
    </template>
    <div class="message-page" v-if="list.length">
      <div class="message-group" v-for="(group, idx) in groupedList" :key="group.date">
        <div class="message-date">{{ group.date }}</div>
        <div
          class="message-item"
          v-for="item in group.items"
          :key="item.id"
          @click="jumpDetail(item)"
        >
          <div class="message-avatar">
            <Notice />
          </div>
          <div class="message-content">
            <div class="message-title-row">
              <span :class="['message-title', 'text-ellipsis']">
                <span v-if="!item.status" class="dot"></span>
                {{ item.title }}
              </span>
            </div>
            <div class="message-text text-ellipsis">{{ item.text }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="noData" v-else>
      <ZNoData text="No content available"></ZNoData>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { getMessageList, postReadMessage, postDelMessage } from "@/api/message";
import { useRouter } from "vue-router";
import { formatDateToEventString } from "@/utils/core/tools";
import { showToast } from "vant";
import { ref, computed, watch, nextTick } from "vue";
import { useGlobalStore } from "@/stores/global";
import { getGlobalDialog } from "@/enter/vant";
import Notice from "@/assets/images/home/<USER>";

const $dialog = getGlobalDialog();

const router = useRouter();
const globalStore = useGlobalStore();

const list = ref<any[]>([]);

// 计算是否有未读消息
const hasUnreadMessages = computed(() => {
  return list.value.some((item: any) => !item.status);
});

// 监听未读消息状态变化，同步到全局状态
watch(
  hasUnreadMessages,
  (newValue) => {
    globalStore.setUnreadIndexboxMsg(newValue);
  },
  { immediate: true }
);

const readAllMesage = async () => {
  try {
    await postReadMessage({ id: 0 });

    // 更新本地状态 - 标记所有消息为已读
    list.value.forEach((item: any) => {
      item.status = 1; // 假设 1 表示已读
    });

    // showToast("operate successfully");
    // 注意：hasUnreadMessages 的 watch 会自动更新全局状态
  } catch (error) {
    console.error("Failed to mark all messages as read:", error);
    // 如果失败，重新获取数据
    init();
  }
};
const clearAllMesage = async () => {
  $dialog({
    title: "Delete Inbox Message",
    message: "Are you sure you want to delete all?",
    confirmText: "Delete",
    onConfirm: async () => {
      await postDelMessage({ id: 0 });
      showToast("operate successfully");
      init();
    },
  });
};
const init = async () => {
  try {
    const res = await getMessageList({});
    // 提取实际的消息数据
    const messageData = res?.data || res || [];
    // 确保是数组类型再进行排序
    if (Array.isArray(messageData)) {
      // 按 send_time 降序排列（最新的在前）
      messageData.sort((a: any, b: any) => {
        return new Date(b.send_time).getTime() - new Date(a.send_time).getTime();
      });
      list.value = messageData;
      await nextTick();
    } else {
      list.value = [];
    }
  } catch (error) {
    console.error("Failed to fetch message list:", error);
    list.value = [];
  }
};
/**
 * 按日期分组的消息列表
 * 根据 send_time 字段按日期分组，并使用 formatDateToEventString 格式化日期
 */
const groupedList = computed(() => {
  const groups: Record<string, any[]> = {};
  const dateToTimestamp: Record<string, number> = {}; // 用于存储日期对应的时间戳，便于排序

  // 遍历消息列表，按日期分组
  list.value.forEach((item: any, index: number) => {
    if (!item.send_time) {
      console.warn("消息缺少 send_time 字段:", item);
      return;
    }

    // 使用 send_time 字段进行日期格式化
    const dateKey = formatDateToEventString(item.send_time);
    // 记录该日期的时间戳（用于排序）
    if (!dateToTimestamp[dateKey]) {
      dateToTimestamp[dateKey] = new Date(item.send_time).getTime();
    }
    // 如果该日期组不存在，创建新组
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    // 将消息添加到对应日期组
    groups[dateKey].push(item);
  });

  // 将分组结果转换为数组格式，并按日期排序（最新的在前）
  const result = Object.keys(groups)
    .sort((a, b) => {
      // 使用实际的时间戳进行排序，确保准确性
      return dateToTimestamp[b] - dateToTimestamp[a];
    })
    .map((date) => ({
      date,
      items: groups[date].sort((a: any, b: any) => {
        // 同一天内的消息按时间降序排列（最新的在前）
        const timeA = new Date(a.send_time).getTime();
        const timeB = new Date(b.send_time).getTime();
        return timeB - timeA;
      }),
    }));

  return result;
});
const jumpDetail = async (item: any) => {
  try {
    await postReadMessage({ id: item.id });

    router.push(`/message/${item.id}`);

    // 更新本地状态 - 标记该消息为已读
    const messageIndex = list.value.findIndex((msg: any) => msg.id === item.id);
    if (messageIndex !== -1) {
      list.value[messageIndex].status = 1; // 假设 1 表示已读
    }
    // 注意：hasUnreadMessages 的 watch 会自动更新全局状态
  } catch (error) {
    console.error("Failed to mark message as read:", error);
  }
};
</script>

<style scoped lang="scss">
.noData {
  display: flex;
  align-items: center;
  flex-direction: column;
  height: 100%;
  background-color: #f4f8fb;
}

.message-page {
  padding: 12px;
  background-color: #f4f8fb;
  padding-bottom: 30px;
  height: 100%;

  .message-group {
    margin-bottom: 24px;

    .message-date {
      color: #999;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 8px;
    }
  }

  .message-item {
    display: flex;
    padding: 16px;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;
    background-color: #fff;
    border-radius: 12px;
    margin-top: 8px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #fafafa;
    }

    .message-avatar {
      width: 44px;
      height: 44px;
      flex-shrink: 0;

      svg {
        width: 100%;
        height: 100%;
        fill: #409eff;
      }
    }

    .message-content {
      flex: 1;
      display: flex;
      flex-direction: column;

      width: calc(100% - 50px);

      .message-title-row {
        display: flex;
        align-items: center;
        margin-bottom: 4px;

        .message-title {
          color: #222;
          position: relative;
          display: flex;
          align-items: center;
          color: #222;

          /* 列表内主标题 */
          font-family: Inter;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          /* 约束宽度 */
          word-break: break-word;
          /* 处理超长无空格文本 */
          hyphens: auto;

          .dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #ff4d4f;
            border-radius: 50%;
            vertical-align: middle;
            margin-right: 4px;
          }
        }
      }

      .message-text {
        color: #666;
        font-family: Inter;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        /* 约束宽度 */
        word-break: break-word;
        /* 处理超长无空格文本 */
        hyphens: auto;
        /* 自动连字符（可选，增强排版） */
      }
    }
  }
}
</style>
