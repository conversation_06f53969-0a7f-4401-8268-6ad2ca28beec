<template>
  <ZPage :request="initData" backgroundColor="#F4F8FB" :narBarStyle="{ background: 'transparent' }">
    <div class="mainbox">
      <BetFilters
        v-model:activeBtn="activeBtn"
        v-model:filterProvider="filterProvider"
        :dateOptions="DATE_OPTIONS"
        @filter-change="handleFilterChange"
      />
      <BetTabs
        v-model:activeTab="activeTab"
        v-model:loading="loading"
        :betOrders="betOrders"
        :gamesList="gamesList"
        :tabTitles="TAB_TITLES"
        @load-more="onLoad"
        @item-click="jumpDetail"
        @tab-change="handleTabChange"
      />
    </div>
  </ZPage>
</template>

<script setup lang="ts">
defineOptions({ name: "BetOrders" });

import BetFilters from "./components/BetFilters.vue";
import BetTabs from "./components/BetTabs.vue";
import { useBetData } from "./composables/useBetData";
import { ref } from "vue";
import { useRouter } from "vue-router";

// Import shared types and constants
import type { FilterProvider, DateOption } from "./shared";
import { DATE_OPTIONS, TAB_TITLES } from "./shared";

const router = useRouter();

// Use the composable for data management
const {
  loading,
  gamesList,
  activeTab,
  betOrders,
  initData: initBetData,
  onLoad: loadMoreData,
} = useBetData();

// Local state for filters
const filterProvider = ref<FilterProvider[]>([]);
const activeBtn = ref<DateOption>({ name: "Last 3 days", value: 3 });

// Wrapper function for the composable's initData
const initData = async (data = {}) => {
  console.log("activeBtn", activeBtn.value);
  await initBetData(filterProvider.value, activeBtn.value, data);
};

// Wrapper function for load more
const onLoad = async (page: number, status: string) => {
  await loadMoreData(page, status, filterProvider.value, activeBtn.value);
};

// Add new handler functions for the components
const handleFilterChange = () => {
  initData();
};

const handleTabChange = (tab: string) => {
  console.log("Tab changed to:", tab);
};

const jumpDetail = (data: any) => {
  router.push({
    path: "/account/bet-detail",
    query: {
      data: JSON.stringify(data),
      gameInfo: JSON.stringify(gamesList.value[data.game_id]),
    },
  });
};
</script>

<style lang="scss" scoped>
.custom-content {
  padding: 0 !important;
}

.mainbox {
  position: relative;
}
</style>
