<script setup lang="ts">
import { useClipboard } from "@/hooks/useClipboard";
import { formatNumberToThousands } from "@/utils/core/tools";
import { useRoute } from "vue-router";
import { jumpGame } from "@/utils/JumpGame";
import { TAB_TITLES } from "./shared";

const route = useRoute();
const { copy } = useClipboard();

// Parse route data with error handling
const data = (() => {
  try {
    return JSON.parse((route.query.data as string) || "{}");
  } catch {
    return {};
  }
})();

const gameInfo = (() => {
  try {
    return JSON.parse((route.query.gameInfo as string) || "{}");
  } catch {
    return {};
  }
})();

// Navigation handler
const jumpTo = () => {
  jumpGame(gameInfo);
};
</script>
<template>
  <ZPage backgroundColor="#F4F8FB" :narBarStyle="{ background: 'transparent' }">
    <div class="bet-detail">
      <div class="detail-header">
        <img
          v-lazy="{
            src: gameInfo.images,
            loading: data.images,
          }"
          alt=""
        />
        <div class="name">{{ data.game_name }}</div>
        <span :class="`status ${TAB_TITLES[data.status]}`">{{ TAB_TITLES[data.status] }}</span>
      </div>
      <div class="detail" v-if="data.status !== 'promos'">
        <div class="line">
          <span class="label">
            <span class="iconfont icon-billNo"></span>
            Bet ID
          </span>
          <span class="content">
            <span class="game-code">{{ data.id }}</span>
            <span class="iconfont icon-fuzhi1" @click="copy(data.id)"></span>
          </span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-shangdian"></span>
            Provider
          </span>
          <span class="content">{{ data.provider_name }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-type"></span>
            Category
          </span>
          <span class="content">{{ data.game_type }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-gongmu"></span>
            Bet
          </span>
          <span class="content">{{ formatNumberToThousands(data.bet / 100) }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-yingbi"></span>
            Valid Bets
          </span>
          <span class="content">{{ formatNumberToThousands(data.effective_bet / 100) }}</span>
        </div>
        <div class="line" v-if="data.house_rake > 0">
          <span class="label">
            <span class="iconfont icon-divide"></span>
            House Rake
          </span>
          <span class="content">{{ data.house_rake }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-a-1yue"></span>
            Bet Time
          </span>
          <span class="content">{{ data.third_create_time }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-a-2yue"></span>
            Settlement Time
          </span>
          <span class="content">{{ data.third_updated_time || data.index_time || "--" }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-xuanzhong2"></span>
            Payout
          </span>
          <span class="content">{{ data.payouts > 0 ? data.payouts : "--" }}</span>
        </div>
        <div class="line" v-if="data.total_refund > 0">
          <span class="label">
            <span class="iconfont icon-refund"></span>
            Total Withholding Refund
          </span>
          <span class="content">{{ data.total_refund }}</span>
        </div>
      </div>
      <!-- Promos details-->
      <div class="detail" v-else>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-shangdian"></span>
            Bet ID
          </span>
          <span class="content">
            <span class="game-code">{{ data.id }}</span>
            <span class="iconfont icon-fuzhi1" @click="copy(data.id)"></span>
          </span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-a-2yue"></span>
            Settlement Time
          </span>
          <span class="content">{{ data.index_time || "--" }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-xuanzhong2"></span>
            WIN
          </span>
          <span class="content">{{ formatNumberToThousands(data.win_lose / 100) }}</span>
        </div>
      </div>
      <div class="footer-btn">
        <button @click="jumpTo">Play again</button>
      </div>
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.bet-detail {
  .iconfont {
    color: #c0c0c0;
    font-size: 24px;

    &.icon-xuanzhong2 {
      font-size: 20px;
      margin: 0 4px 0 2px;
    }
  }

  .detail-header {
    border-radius: 10px;
    background-color: #fff;
    margin: 10px 10px 14px;
    padding: 28px 14px 24px;
    text-align: center;

    img {
      width: 80px;
      height: 80px;
      border-radius: 10px;
      margin: 0 auto;
    }

    .name {
      font-size: 13px;
      font-style: normal;
      font-weight: 600;
      padding: 14px 0 18px;
      line-height: 16px;
    }

    .status {
      //   display: flex;
      display: inline-block;
      padding: 2px 18px;
      align-items: center;
      text-align: center;
      border-radius: 42px;
      background: #f9f9f9;
      padding: 4px 12px;
      color: #999999;
      font-weight: 600;

      /*  &.Settled {
        background: #dbe9ff;
        color: #277dff;
      }

      &.Unsettled {
        background: #fee1e3;
        color: #2f81ff;
      }

      &.Cancelled {
        background: #eeeeef;
        color: #2a7fff;
      }

      &.Promos {
        background: #ffe3d0;
        color: #ff7b1e;
      } */
    }

    .amount {
      margin-top: 14px;
      font-size: 26px;
      font-weight: 700;
    }
  }

  .detail {
    margin: 10px;
    border-radius: 10px;
    background-color: #fff;
    padding: 8px 12px;

    .line {
      line-height: 38px;
      display: flex;
      height: 38px;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;

      .label {
        color: #999999;
        font-size: 14px;
        font-weight: 400;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .content {
        color: #303030;
        font-size: 15px;
        font-weight: 500;

        .icon-fuzhi1 {
          color: #ac1140;
          font-size: 16px;
          margin-left: 6px;
        }
      }
    }
  }

  .footer-btn {
    text-align: center;
    width: 100%;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 10px 0 14px;
    font-size: 18px;

    > button {
      width: 340px;
      height: 48px;
      background-color: #ac1140;
      border-radius: 999px;
      color: #fff;
      text-align: center;
    }
  }
}
</style>
