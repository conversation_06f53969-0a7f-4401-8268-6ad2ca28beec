import { ref } from "vue";
import { betOrder } from "@/api/user";
import { getGameInfo } from "@/api/games";
import { groupData, formatNumberToThousands } from "@/utils/core/tools";
import PlaceholderBase64 from "@/assets/constants/nsLogoBase64";
import placeholderBannerBase64 from "@/assets/constants/homeBannerBase64";

// Import shared types and constants
import type { BetItem, BetOrder, FilterProvider, DateOption } from "../shared";
import { TAB_TITLES, DEFAULT_BET_ORDERS, DEFAULT_CURRENT_PAGE, API_CONFIG } from "../shared";

export function useBetData() {
  // State
  const loading = ref(false);
  const gamesList = ref<Record<string | number, any>>({});
  const gameIds = ref<(string | number)[]>([]);
  const activeTab = ref<string>("3");

  const betOrders = ref<Record<string, BetOrder>>({ ...DEFAULT_BET_ORDERS });
  const currentPage = ref({ ...DEFAULT_CURRENT_PAGE });

  // Utility functions
  const getRightText = (item: BetItem, tab: string) => {
    let text = formatNumberToThousands(item.win_lose / 100);
    if (tab === "Unsettled") {
      text = "Bet " + formatNumberToThousands(item.bet / 100);
    }
    if (tab === "Promos") {
      text = "+" + text;
    }
    return text;
  };

  const getRightStyle = (tab: string) => {
    const styleMap = {
      Settled: { color: "#FF4849", "font-weight": 600 },
      Unsettled: { color: "#909090", "font-weight": 600 },
      Cancel: { color: "#303030", "font-weight": 600 },
      Promos: { color: "#12BE6B", "font-weight": 600 },
    };
    return styleMap[tab as keyof typeof styleMap] || styleMap.Settled;
  };

  // Helper function to get tab key from status
  const getTabKeyFromStatus = (status: string | number): string => {
    const statusMap: Record<string | number, string> = {
      1: "Unsettled",
      2: "Cancel",
      3: "Settled",
      promos: "Promos",
    };
    return statusMap[status] || "Settled";
  };

  // Batch fetch game details to avoid duplicate requests
  const fetchGameDetails = async (gameIds: (string | number)[]) => {
    const uniqueIds = [...new Set(gameIds)];
    const pendingIds = uniqueIds.filter((id) => !Object.keys(gamesList.value).includes(String(id)));
    if (pendingIds.length === 0) return;

    try {
      const responses = await Promise.all(pendingIds.map((id) => getGameInfo({ id: Number(id) })));
      responses.forEach((res, index) => {
        const gameData = res?.data || res;
        gamesList.value[pendingIds[index]] =
          Array.isArray(gameData) && gameData.length > 0 && gameData.length < 2
            ? gameData[0]
            : { images: PlaceholderBase64 };
      });
    } catch (error) {
      console.error("Failed to fetch game details:", error);
      // Set fallback data for failed requests
      pendingIds.forEach((id) => {
        gamesList.value[id] = { images: PlaceholderBase64 };
      });
    }
  };

  // Fetch bet orders with improved error handling
  const fetchBetOrders = async (
    filterProvider: FilterProvider[],
    activeBtn: DateOption,
    data: Record<string, any> = {}
  ) => {
    const providerParam =
      filterProvider.length === 0 || filterProvider.some((p) => p.id === "all")
        ? ""
        : filterProvider.map((p) => p.id).join(",");

    const params = {
      provider: providerParam,
      page: 1,
      page_number: 15,
      status: 1,
      date: activeBtn.value,
      ...data,
    };

    const tabKey = getTabKeyFromStatus(params.status);
    currentPage.value[tabKey as keyof typeof currentPage.value] = params.page;

    try {
      const res = await betOrder(params);
      const responseData = res?.data || res;
      const newList = responseData.list || [];

      if (newList.length > 0) {
        // Extract game IDs for batch loading
        const newGameIds = newList.map((r: BetItem) => r.game_id);
        gameIds.value = [...new Set([...gameIds.value, ...newGameIds])];

        // Format data with fallback values
        const formattedList = newList.map((item: BetItem) => ({
          ...item,
          images: placeholderBannerBase64,
          game_name: item.game_name || `${item.provider_name} Promos`,
        }));

        const groupedData = (groupData(formattedList, "third_create_time") || {}) as Record<
          string,
          BetItem[]
        >;

        // Update bet orders data
        updateBetOrdersData(tabKey, responseData, groupedData, params.page === 1);
      } else {
        // No data case
        betOrders.value[tabKey] = { ...responseData, list: {} };
      }
    } catch (error) {
      console.error("Failed to fetch bet orders:", error);
      throw error;
    }
  };

  // Helper function to update bet orders data
  const updateBetOrdersData = (
    tabKey: string,
    responseData: any,
    groupedData: Record<string, BetItem[]>,
    isFirstPage: boolean
  ) => {
    if (isFirstPage) {
      betOrders.value[tabKey] = { ...responseData, list: groupedData };
      return;
    }

    // Handle pagination merge
    const currentList = betOrders.value[tabKey].list;
    const timesList = Object.keys(currentList);
    const lastTime = timesList[timesList.length - 1];
    const newTimeList = Object.keys(groupedData);
    const firstTime = newTimeList[0];

    if (lastTime === firstTime) {
      // Same time period, merge items
      const { [lastTime]: lastTimeItem, ...others1 } = currentList;
      const { [firstTime]: firstTimeItem, ...others2 } = groupedData;

      betOrders.value[tabKey] = {
        ...responseData,
        list: {
          ...others1,
          [lastTime]: [...lastTimeItem, ...firstTimeItem],
          ...others2,
        },
      };
    } else {
      // Different time periods, append data
      betOrders.value[tabKey] = {
        ...responseData,
        list: { ...currentList, ...groupedData },
      };
    }
  };

  // Initialize data for all tabs
  const initData = async (
    filterProvider: FilterProvider[],
    activeBtn: DateOption,
    data: Record<string, any> = {}
  ) => {
    try {
      loading.value = true;
      await Promise.all([
        fetchBetOrders(filterProvider, activeBtn, { status: 3, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: 1, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: 2, ...data }),
        fetchBetOrders(filterProvider, activeBtn, { status: "promos", ...data }),
      ]);
      await fetchGameDetails(gameIds.value);
    } catch (error) {
      console.error("Failed to initialize data:", error);
    } finally {
      loading.value = false;
    }
  };

  // Load more data for pagination
  const onLoad = async (
    page: number,
    status: string,
    filterProvider: FilterProvider[],
    activeBtn: DateOption
  ) => {
    const currentPageValue = currentPage.value[status as keyof typeof currentPage.value];
    const totalPage = betOrders.value[status]?.total_page || 0;

    if (currentPageValue === page || page > totalPage) {
      loading.value = false;
      return;
    }

    try {
      await fetchBetOrders(filterProvider, activeBtn, {
        page: page || 1,
        status: activeTab.value,
      });
      await fetchGameDetails(gameIds.value);
    } catch (error) {
      console.error("Failed to load more data:", error);
    } finally {
      loading.value = false;
    }
  };

  return {
    // State
    loading,
    gamesList,
    gameIds,
    activeTab,
    betOrders,
    currentPage,

    // Utility functions
    getRightText,
    getRightStyle,

    // API functions
    fetchGameDetails,
    fetchBetOrders,
    initData,
    onLoad,
  };
}
