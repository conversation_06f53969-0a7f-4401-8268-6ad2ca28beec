<script setup lang="ts">
import { useClipboard } from "@/hooks/useClipboard";
import { formatNumberToThousands, formatEngDate } from "@/utils/core/tools";

const route = useRoute();
const { isCopied, copy } = useClipboard();
const data = route.query;
console.log("data", data);
</script>
<template>
  <ZPage backgroundColor="#F4F8FB" :title="data.title" :narBarStyle="{ background: 'transparent' }">
    <div class="bet-detail">
      <div class="detail-header">
        <div class="header-img">
          <span :class="[
            `gift`,
            {
              Maya: data.payment_method
                ? data.payment_method.toLowerCase().includes('maya')
                : false,
              Gcash: data.payment_method
                ? data.payment_method.toLowerCase().includes('gcash')
                : false,
            },
          ]"></span>
        </div>

        <div class="amount" v-show="data.right_amount">
          {{ data.right_amount }}
        </div>
        <!-- <span class="status">{{ data.status_name }}</span> -->
      </div>
      <div class="detail">
        <div class="line" v-show="data.payment_method">
          <span class="label">
            <span class="iconfont icon-zhifufangshi"></span>
            Payment Method</span>
          <span class="content">{{ data.payment_method }}</span>
        </div>
        <div class="line" v-show="data.title === 'Reward'">
          <span class="label">
            <span class="iconfont icon-zhifufangshi"></span>
            Reward</span>
          <span class="content">{{ data.status_name }}</span>
        </div>
        <div class="line" v-show="data.account_no">
          <span class="label">
            <span class="iconfont icon-Union"></span>
            Account number</span>
          <span class="content">{{ data.account_no }}</span>
        </div>
        <div class="line">
          <span class="label">
            <span class="iconfont icon-a-3yue"></span>
            Creation Time</span>
          <span class="content">{{ formatEngDate(data.updated_at || data.created_at) }}</span>
        </div>
        <div class="line">
          <span class="label" v-if="data.game_code || data.pay_serial_no || data.order_no">
            <span class="iconfont icon-billNo"></span>
            Order ID</span>
          <span class="content">
            <span :class="data.game_code ? 'game-code' : 'order-no'" v-if="data.game_code">{{
              data.game_code || data.pay_serial_no || data.order_no
              }}</span>
            <span class="iconfont icon-fuzhi1"
              @click="copy(data.game_code || item.order_no || data.pay_serial_no)"></span>
          </span>
        </div>
        <div class="line remark" v-show="data.remark">
          <span class="label">
            <span class="iconfont icon-bookmark"></span>
            Note</span>
          <div class="content">{{ data.remark }}</div>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.bet-detail {
  .detail-header {
    background-color: #fff;
    margin: 10px 10px 14px;
    border-radius: 10px;
    padding: 16px 14px 24px;
    text-align: center;

    .header-img {
      margin: 0 auto;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f9f9f9;

      .gift {
        display: inline-block;
        overflow: hidden;
        width: 40px;
        height: 40px;
        background-image: url("@/assets/images/account/coin_img.png");
        background-repeat: no-repeat;
        background-size: 39.467vw 21.467vw;
        background-position: left -29vw top -0.8vw;

        &.Maya {
          background-position: left -28.5vw top -10.8vw;
        }

        &.Gcash {
          background-position: left -0.5vw top -0.8vw;
        }
      }
    }

    .amount {
      font-size: 20px;
      font-style: normal;
      font-weight: 800;
      padding: 18px;
      line-height: 16px;
    }

    .status {
      display: inline-block;
      padding: 4px 12px;
      align-items: center;
      text-align: center;
      border-radius: 42px;
      background: #f9f9f9;
      padding: 4px 12px;
      color: #999999;
    }
  }

  .detail {
    background-color: #fff;
    margin: 8px 12px;
    border-radius: 10px;
    padding: 8px;

    .iconfont {
      font-size: 16px;
      color: #c0c0c0;
      vertical-align: bottom;
    }

    .line {
      line-height: 38px;
      display: flex;
      height: 38px;
      justify-content: space-between;
      align-items: center;
      align-self: stretch;
      flex-wrap: nowrap;
      flex-grow: 1;

      &.remark {
        // display: block;
        flex-wrap: wrap;
        min-height: 76px;
        height: auto;

        .content {
          width: 100%;
          text-align: left;
          padding: 0 20px;
        }
      }

      .label {
        // width: 40%;
        color: #6a7a88;
        font-size: 14px;
        font-weight: 400;
        display: flex;
        justify-content: flex-start;
        flex-wrap: nowrap;
        gap: 4px;

        .iconfont {
          font-size: 24px;
        }
      }

      .content {
        // width: 60%;
        color: #303030;
        font-size: 15px;
        font-weight: 500;
        overflow: hidden;
        text-align: right;

        &:last-child {
          // width: 83%;
        }
      }

      .game-code,
      .order-no {
        white-space: nowrap;
        margin-right: 4px;
      }

      .game-code {
        font-size: 10px;
      }

      .icon-fuzhi1 {
        color: #ac1140;
        font-size: 14px;
      }
    }
  }
}
</style>
