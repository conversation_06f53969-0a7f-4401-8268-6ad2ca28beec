<template>
  <div class="items-block">
    <!-- 分组日期 -->
    <div class="items-title">{{ date }}</div>

    <!-- 交易项目列表 -->
    <div class="items-wrap">
      <div v-for="item in items" :key="item.id" class="items-content">
        <LineSetting
          valueStatus="normal"
          :valueStyle="getStatusStyle(item)"
          :text="item.title"
          :value="item.status_name"
          :showArrow="false"
          :rightStyle="{ fontWeight: 600 }"
          :rightText="item.right_amount"
          @click="$emit('item-click', item)"
        >
          <template #icon>
            <div class="icon-img">
              <span :class="getIconClass(item)"></span>
              <span v-if="item.quantity > 1" class="item-quantity">
                {{ item.quantity }}
              </span>
            </div>
          </template>
        </LineSetting>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LineSetting from "@/views/account/components/LineSetting.vue";
import type { TransactionGroupProps, TransactionItem } from "../config";
import { STATUS_COLORS } from "../config";

const props = withDefaults(defineProps<TransactionGroupProps>(), {
  showTopPadding: true,
});

defineEmits<{
  "item-click": [item: TransactionItem];
}>();

/**
 * 获取状态样式
 */
const getStatusStyle = (item: TransactionItem) => {
  const statusKey = item.recharge_status || item.status_name;
  const color = STATUS_COLORS[statusKey as keyof typeof STATUS_COLORS] || "#999";
  return { color };
};

/**
 * 获取图标类名
 */
const getIconClass = (item: TransactionItem) => {
  const baseClass = "gift";
  const paymentMethod = item.payment_method?.toLowerCase() || "";

  return [
    baseClass,
    {
      Maya: paymentMethod.includes("maya"),
      Gcash: paymentMethod.includes("gcash"),
    },
  ];
};
</script>

<style lang="scss" scoped>
.items-block {
  padding: 10px 10px 0;
}

.items-wrap {
  background: #fff;
  border-radius: 10px;
}

.items-content {
  padding: 0 12px;

  .icon-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f9f9f9;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .item-quantity {
      position: absolute;
      bottom: 0;
      right: -4px;
      padding: 0 6px;
      background: #999;
      color: #fff;
      font-size: 10px;
      border-radius: 12px;
    }
  }

  .gift {
    display: inline-block;
    overflow: hidden;
    width: 28px;
    height: 28px;
    background-image: url(/src/assets/images/account/coin_img.png);
    background-repeat: no-repeat;
    background-size: 100px 55px;
    background-position: left -73px top -2px;

    &.Maya {
      background-position: left -19vw top -7.6vw;
    }

    &.Gcash {
      background-position: left -0.5vw top -2px;
    }
  }
}

.items-title {
  color: #6a7a88;
  font-family: Inter;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 48px;
  border-bottom: 0.5px solid #eee;
  padding: 0 12px;
}
</style>
