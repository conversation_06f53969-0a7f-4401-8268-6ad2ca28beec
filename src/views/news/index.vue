<script setup lang="ts">
defineOptions({ name: "News" });
import { ref, computed, watch } from "vue";
import { getNews } from "@/api/news";
import { formatDateToEventString, getServerSideImageUrl } from "@/utils/core/tools";
import { getLocalStorage, removeLocalStorage, setLocalStorage } from "@/utils/core/Storage";
import router from "@/router";
import { showToast } from "vant";
import { useGlobalStore } from "@/stores/global";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";
import {
  handleNewsUnreadStatus,
  sortNewsArray,
  validateAndFixUrl,
  newsTypeIcon,
  newsTypeText,
  type NewsItem as NewsItemType
} from "./newsUtils";

// 全局状态管理
const globalStore = useGlobalStore();

// 新闻列表数据
const newsList = ref<NewsItemType[]>([]);

// 计算是否有未读新闻
const hasUnreadNews = computed(() => {
  return newsList.value.some(item => item.unread);
});

// 监听未读新闻状态变化，同步到全局状态
watch(hasUnreadNews, (newValue) => {
  globalStore.setUnreadNews(newValue);
}, { immediate: true });

const handleUnread = (list: NewsItemType[]) => {
  return handleNewsUnreadStatus(
    list,
    (id: number) => !!getLocalStorage(`NEWS_READ_ID_${id}`),
    (id: number) => removeLocalStorage(`NEWS_READ_ID_${id}`)
  );
};

const readDetail = (item: NewsItemType) => {
  if (item.content_type == 1) {
    // 验证并修复 URL 格式
    const validUrl = validateAndFixUrl(item.url || '');
    if (!validUrl) {
      showToast(`${item.url}\nIt is not a valid link.`);
      return;
    }
    const success = MobileWindowManager.navigateToUrl(validUrl);
    if (!success) {
      console.error("Failed to open news URL:", validUrl);
      showToast("Failed to open link, please try again");
    }
  } else if (item.content_type == 2) {
    router.push(`/news/${item.id}`);
  }

  // 标记为已读
  setLocalStorage(`NEWS_READ_ID_${item.id}`, true);

  // 更新新闻列表的未读状态
  newsList.value = handleUnread(newsList.value);

  // 注意：hasUnreadNews 的 watch 会自动更新全局状态
};

// 获取新闻列表
const getNewsList = async () => {
  try {
    const res = await getNews();
    const newsData = res?.data || res || [];
    newsList.value = handleUnread(sortNewsArray(newsData));
  } catch (error) {
    console.error("Failed to fetch news list:", error);
  }
};
</script>

<template>
  <ZPage :showNarBar="false" :request="getNewsList" backgroundColor="transparent">
    <div class="news-page" v-if="newsList.length > 0">
      <div class="news-list">
        <!-- 渲染新闻列表 -->
        <div v-for="item in newsList" :key="item.id" class="news-item" :class="item.type" @click="readDetail(item)">
          <!-- 未读标记 -->
          <div v-if="item.unread" class="unread-dot"></div>
          <!-- 内容区域 -->
          <div class="content">
            <!-- 标题 -->
            <div class="title-container">
              <h3 class="title">{{ item.title }}</h3>
            </div>
            <!-- 描述 -->
            <p class="desc">{{ item.abstract }}</p>
            <!-- 类型和日期 -->
            <div class="type-date">
              <img :src="newsTypeIcon[item.news_type]" alt="Type Icon" />
              <span>{{ newsTypeText[item.news_type] }}</span>
              <span class="date-time">{{ formatDateToEventString(item.created_at) }}</span>
            </div>
          </div>
          <!-- 图片 -->
          <div v-if="item.image" class="image-container">
            <ZImage :src="getServerSideImageUrl(item.image)" :alt="item.title" class="news-image" />
          </div>
        </div>
      </div>
      <TabBarGap></TabBarGap>
    </div>
    <div v-else class="no-data-container">
      <ZNoData text="No content available" />
    </div>
  </ZPage>
</template>

<style scoped lang="scss">
.news-page {
  padding-bottom: 20px;
  box-sizing: border-box;

  .news-list {
    padding: 0 16px;
    padding-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    /* 每个新闻项之间的间距 */

    .news-item {
      position: relative;
      background: #ffffff;
      border-radius: 12px;
      padding: 16px;
      display: flex;
      align-items: flex-start;
      gap: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }

      .unread-dot {
        position: absolute;
        top: 24px;
        left: 6px;
        width: 8px;
        height: 8px;
        background: #FF936F;
        border-radius: 50%;
        z-index: 2;
      }

      .content {
        flex: 1;
        min-width: 0;

        .title-container {
          margin-bottom: 8px;

          .title {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #222;
            /* tab文字和新闻标题 */
            font-family: Inter;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
          }
        }

        .desc {
          margin: 0 0 12px 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #999;
          font-family: Inter;
          font-size: 14px;
        }

        .type-date {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #222;
          font-family: Inter;
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: normal;

          img {
            width: 16px;
            height: 16px;
            object-fit: contain;
          }

          .date-time {
            margin-left: auto;
            font-weight: 500;
          }
        }
      }

      .image-container {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;

        .news-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 40px 20px;

  }
}
</style>
