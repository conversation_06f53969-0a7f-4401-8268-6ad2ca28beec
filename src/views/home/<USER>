<script setup lang="ts">
defineOptions({ name: "Home" });

import { ref, onMounted, onUnmounted, onActivated, onBeforeUnmount, watchEffect } from "vue";
import { useRouter, onBeforeRouteLeave } from "vue-router";
// 业务组件
import Carousel from "./components/Carousel.vue";
import VideoCarousel from "./components/VideoCarousel.vue";
import NotificationBar from "./components/NotificationBar.vue";
import ProviderLogos from "./components/ProviderLogos.vue";
import GamesList from "./components/GamesList.vue";
import WalletRewardBtn from "./components/WalletRewardBtn.vue";

// store
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { coinAnimationBus } from "@/utils/coinAnimationBus";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useRewardWalletStore } from "@/stores/rewardWallet";
// utils
import { serviceMgr, ServiceType } from "@/utils/ServiceMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { getMessageList } from "@/api/message";
import { bonusWalletManager } from "@/utils/managers/BonusWalletManager";
import { KycMgr, InGameType, KycState } from "@/utils/KycMgr";

// 弹窗组件
import Tip21OldTip from "@/components/ZPopDialog/Tip21OldTip.vue";
import VipTip from "@/components/ZPopDialog/VipTip.vue";
import ActivityBonusTip from "@/components/ZPopDialog/ActivityBonusTip.vue";
import LeaderBoardPopTip from "@/components/ZPopDialog/LeaderBoardPopTip.vue";
import LeaderBoardPopJILITip from "@/components/ZPopDialog/LeaderBoardPopJILITip.vue";
import PopupBannersTip from "@/components/ZPopDialog/PopupBannersTip.vue";
import RegisterBonusTip from "@/components/ZPopDialog/RegisterBonusTip.vue";
import KycTip from "@/components/ZPopDialog/KycTip.vue";
import DownloadGuideTip from "@/components/ZPopDialog/DownloadGuideTip.vue";
// 金币动画
import CoinAnimation from "@/components/CoinAnimation/index.vue";

const router = useRouter();

const autoPopMgrStore = useAutoPopMgrStore();
const depositStore = useDepositStore();
const rewardWalletStore = useRewardWalletStore();

const globalStore = useGlobalStore();
const gameStore = useGameStore();
const { homeClassifyGames } = storeToRefs(gameStore);
const { hasUnreadCustomerMsg, hasUnreadIndexboxMsg } = storeToRefs(globalStore);
const { bonusTotal, hasContent, bonusList } = storeToRefs(rewardWalletStore);

const diffWalletBtnType = computed(() => {
  return hasContent.value && bonusTotal.value > 0;
});
// 读取厂商列表数据——底部logo展示
const activeCategory = ref(0);
const mainContent = ref<HTMLElement | null>(null);
const isSidebarSticky = ref(false);
const sidebarTop = ref(0);

// 金币动画
const coinAnimationRef = ref(null);

// Balance 组件引用
const balanceRef = ref(null);

// 使用 watchEffect 监听 token 和 balanceRef 的变化,避免重复注册
let isBalanceRegistered = false;
watchEffect(() => {
  if (globalStore.token && balanceRef.value && !isBalanceRegistered) {
    try {
      coinAnimationBus.setHomeBalanceRef(balanceRef.value);
      isBalanceRegistered = true;
    } catch (error) {}
  }
  // 当用户登出时重置注册状态
  if (!globalStore.token && isBalanceRegistered) {
    isBalanceRegistered = false;
  }
});

/* 顶部导航高度 */
const TOP_HEIGHT = 100;

const handleMessage = () => {
  router.push("/message");
};
const handleCustomerService = () => {
  serviceMgr.instance.openChat(ServiceType.Custom);
};

const handleShowMore = (categoryId: number) => {
  router.push(`/game-categories?categoryId=${categoryId}`);
};

const getUnreadIndexboxMsg = async () => {
  if (!globalStore.token) return;
  const list = await getMessageList({});
  globalStore.hasUnreadIndexboxMsg = list?.some((item) => !item.status);
};

// like
const updateLike = (catIdx, game, gameIdx) => {
  const category = homeClassifyGames.value[catIdx];
  if (!category) return;
  const games = category.games;
  if (!games || !games[gameIdx]) return;
  // 直接修改 is_like
  games[gameIdx].is_like = games[gameIdx].is_like == "1" ? "0" : "1";
};

// 处理滚动事件
const handleScroll = () => {
  if (!mainContent.value) return;
  const scrollTop = mainContent.value.scrollTop;
  const sections = document.querySelectorAll(".game-category-section");

  // 更新左侧激活的分类
  sections.forEach((section, index) => {
    const sectionTop = (section as HTMLElement).offsetTop - TOP_HEIGHT; // 减去顶部导航高度
    const sectionHeight = (section as HTMLElement).offsetHeight;
    if (scrollTop >= sectionTop - 50 && scrollTop < sectionTop + sectionHeight - 50) {
      activeCategory.value = index;
    }
  });
  // 处理左侧边栏吸顶效果
  isSidebarSticky.value = scrollTop >= sidebarTop.value;
};

// 判断是否跳转奖金钱包
const checkRedirect = async () => {
  try {
    const shouldRedirect = await bonusWalletManager.shouldPerformAutoRedirect();
    if (shouldRedirect) {
      console.log("Home: 检测到需要自动跳转到bonus-wallet（有新奖金或首次访问）");
      await bonusWalletManager.performAutoRedirect();
      return true;
    } else {
      console.log("Home: 无需自动跳转到bonus-wallet（用户已访问且无新奖金）");
      return false;
    }
  } catch (error) {
    console.error("检查跳转失败:", error);
    return false;
  }
};

// 处理弹窗的金币动画
const handleStartCoinAnimation = (startElement) => {
  // 注册金币动画组件到事件总线
  if (coinAnimationRef.value) {
    coinAnimationBus.setAnimationRef(coinAnimationRef.value);
  }
  // 这个会通过事件总线获取
  setTimeout(() => {
    // 延迟执行，确保所有组件都已挂载
    const homeBalanceElement = document.querySelector(".balance-wrap .coin");
    if (startElement && homeBalanceElement) {
      coinAnimationBus.startAnimation({
        startElement: startElement,
        endElement: homeBalanceElement as HTMLElement,
        coinCount: 10,
      });
    } else {
      console.error("❌ 起始或目标元素未找到", { startElement, homeBalanceElement });
    }
  }, 200);
};

// 初始化
onMounted(() => {
  activeCategory.value = 0;
  if (mainContent.value) {
    mainContent.value.addEventListener("scroll", handleScroll);
    // 计算侧边栏初始位置
    sidebarTop.value = mainContent.value.offsetTop;
  }
});

// 清理
onUnmounted(() => {
  if (mainContent.value) {
    mainContent.value.removeEventListener("scroll", handleScroll);
  }
});

// kyc 前置验证，不放在弹窗队列里，原因有几个
// 1. 是因为怕用户手动修改浏览器路由地址，然后没有预先请求kyc 接口
// 2. kyc 认证页返回来，如果没有验证成功，还得弹窗的，
const showKycDialog = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (globalStore.token) {
      KycMgr.instance.clearData();
      autoPopMgrStore.getKycInfo().then(() => {
        if (autoPopMgrStore.isNeedShowPopKYC()) {
          autoPopMgrStore.showKycTip = true;
          resolve(true);
        } else {
          resolve(false);
        }
      });
    } else {
      resolve(false);
    }
  });
};

const autoPopMgr = async () => {
  // 如果已经处理过弹窗，直接返回
  if (autoPopMgrStore.hasPop) return;
  // 标记已处理，避免重复触发
  autoPopMgrStore.hasPop = true;

  // 请求需要登录的弹窗数据，Banner 数据已在 App.vue 中预加载
  const loginRequiredPromises = [
    autoPopMgrStore.getEnvelopeData(),
    autoPopMgrStore.getCainoYesterdaytRankData(),
    autoPopMgrStore.getJiLiRankData(),
    autoPopMgrStore.getAllActivityBonus(),
    autoPopMgrStore.getSpinInfo(),
  ];

  Promise.allSettled(loginRequiredPromises).finally(() => {
    // 多重检查登录状态，确保不会在登录后误触发未登录弹窗
    const isLoggedIn = !!globalStore.token;
    if (isLoggedIn) {
      // 已登录，确保21岁提示不会显示
      window["showTip21Old"] = true;
    }
    AutoPopMgr.autoPopupDialog();
  });
};

onActivated(async () => {
  getUnreadIndexboxMsg(); // 获取未读消息

  // 检查是否需要跳转奖金钱包，如果跳转了就不继续执行其他逻辑
  const hasRedirected = await checkRedirect();
  if (hasRedirected) {
    return;
  }

  // 先处理 KYC 弹窗，等待用户交互完成
  const hasKycDialog = await showKycDialog();
  if (hasKycDialog) {
    return;
  }
  // KYC 弹窗处理完成后，再执行其他弹窗逻辑
  await autoPopMgr();
});

// 关闭所有弹窗的通用方法
const closeAllPopups = () => {
  // 关闭充值弹窗
  if (depositStore.show) {
    depositStore.show = false;
  }

  // 重置自动弹窗管理器
  AutoPopMgr.resetAllPopups();

  // 关闭所有弹窗状态
  autoPopMgrStore.closeAllPopups();
};

// 路由离开前关闭弹窗
onBeforeRouteLeave((to, _from, next) => {
  closeAllPopups();
  next();
});

const handleRewardWallet = () => {
  router.push("/bonus-wallet");
};

// 下载引导组件引用
const downloadGuideRef = ref(null);
</script>

<template>
  <ZPage :showNarBar="false" :request="globalStore.getBalance">
    <div class="home-container">
      <section class="adsorption">
        <div class="user-info">
          <div class="wallet-container">
            <div v-show="!globalStore.token" class="nologin">
              <div @click="() => router.push('/login')" class="tologin">Register / Login</div>
            </div>
            <div class="login" v-show="globalStore.token">
              <Balance ref="balanceRef" :coinSize="38"></Balance>
              <div class="todeposit" @click="depositStore.openDialog">Deposit</div>
            </div>
          </div>
          <div class="right-icons">
            <div @click="handleRewardWallet" class="message-icon reward-wallet-icon">
              <WalletRewardBtn
                :state="diffWalletBtnType ? 'claim' : 'standby'"
                :animation-config="{
                  width: diffWalletBtnType ? 48 : 24,
                  height: diffWalletBtnType ? 48 : 24,
                  fps: 12,
                  loop: true,
                  autoplay: true,
                }"
              />
              <div class="notice-bar" v-if="diffWalletBtnType">
                {{ bonusTotal }} Bonus Available,Claim Now!
              </div>
              <van-popover color="#FF794C" background="#FFF8F6" />
            </div>
            <div class="message-icon">
              <ZIcon @click="handleMessage" type="icon-xiaoxi" color="#999"></ZIcon>
              <span v-if="hasUnreadIndexboxMsg" class="badge"></span>
            </div>
            <div class="message-icon">
              <ZIcon @click="handleCustomerService" type="icon-kefu1" color="#999"></ZIcon>
              <span v-if="hasUnreadCustomerMsg" class="badge"></span>
            </div>
          </div>
        </div>
        <div class="message">
          <NotificationBar />
        </div>
      </section>
      <section class="home-scroll" ref="mainContent" @scroll="handleScroll">
        <section class="banner-video">
          <div class="banner">
            <Carousel bannerType="home" />
          </div>
          <div class="video">
            <VideoCarousel />
          </div>
        </section>
        <GamesList
          :active-category="activeCategory"
          :is-sidebar-sticky="isSidebarSticky"
          :main-content="mainContent"
          :top-height="TOP_HEIGHT"
          @show-more="handleShowMore"
          @update-like="updateLike"
        />
        <section class="about-section">
          <ProviderLogos />
          <div class="about-us-content">
            <div class="about-btn" @click="router.push('/protocal/terms-of-use')">Terms of use</div>
            <div class="about-btn" @click="router.push('/protocal/privacy-policy')">
              Privacy Policy
            </div>
          </div>
        </section>
        <TabBarGap></TabBarGap>
      </section>
    </div>
    <!-- 只在首页才弹窗 -->
    <Tip21OldTip></Tip21OldTip>
    <VipTip></VipTip>
    <LeaderBoardPopTip></LeaderBoardPopTip>
    <LeaderBoardPopJILITip></LeaderBoardPopJILITip>
    <PopupBannersTip></PopupBannersTip>
    <ActivityBonusTip @start-coin-animation="handleStartCoinAnimation"></ActivityBonusTip>
    <RegisterBonusTip @start-coin-animation="handleStartCoinAnimation"></RegisterBonusTip>
    <KycTip></KycTip>
    <!-- 下载引导提示 - 只在首页显示 -->
    <DownloadGuideTip ref="downloadGuideRef"></DownloadGuideTip>

    <!-- 金币动画组件 - 只在首页显示 -->
    <CoinAnimation ref="coinAnimationRef" :coinCount="10" />
  </ZPage>
</template>

<style scoped lang="scss">
.wallet-container {
  .nologin {
    display: flex;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 999px;
    background: var(--Nustar, #ac1140);

    .tologin {
      color: #fff;
      font-family: Inter;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }

  .login {
    font-family: D-DIN;
    display: flex;
    font-weight: 700;
    color: #000;
    align-items: center;
    background-color: #f9eef2;
    border-radius: 16px;
    gap: 20px;
    height: 32px;

    .todeposit {
      border-radius: 999px;
      background: var(--Nustar, #ac1140);
      padding: 6px 10px;
      border-radius: 16px;
      color: #fff;
      font-size: 14px;
      font-family: Inter;
    }
  }
}

.home-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background: linear-gradient(180deg, #fff 0.04%, #f4f8fb 40.22%);

  .adsorption {
    background: #fff;
    color: var(--text-color-white);
    box-sizing: border-box;
    height: 94px;
    margin: 12px 12px 0 12px;

    .user-info {
      display: flex;
      align-items: center;
      gap: 24px;
      color: var(--text-color-white);
      justify-content: space-between;

      .right-icons {
        text-align: right;
        display: flex;
        gap: 12px;

        .reward-wallet-icon {
          position: relative;
          width: 24px;
          height: 24px;

          .notice-bar {
            position: absolute;
            bottom: -40px;
            right: -60px;
            height: 34px;
            line-height: 34px;
            white-space: nowrap;
            padding: 0 8px;
            font-size: 12px;
            text-align: center;
            z-index: 2;
            background: #fff8f6;
            color: #ff794c;
            border-radius: 16px;

            &::before {
              content: "";
              position: absolute;
              top: -8px;
              right: 32%;
              width: 0;
              height: 0;
              border-left: 8px solid transparent;
              border-right: 8px solid transparent;
              border-bottom: 8px solid #fff8f6;
            }
          }
        }
      }

      img {
        width: 24px;
        height: 24px;
      }

      .message-icon {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .badge {
          position: absolute;
          top: 2px;
          right: 0px;
          width: 8px;
          height: 8px;
          background: var(--red-color);
          border-radius: 50%;
          border: solid 0.5px #fff;
        }
      }
    }

    .message {
      margin-top: 16px;
      background-color: #fff;
    }
  }

  .home-scroll {
    flex: 1;
    overflow-y: auto;
    position: relative;
    scroll-behavior: smooth;
    // background: #F4F8FB;
    background: linear-gradient(180deg, #fff 0.04%, #f4f8fb 40.22%);

    .banner-video {
      margin: 0 12px 12px 12px;

      .video {
        margin-top: 12px;
      }

      .banner {
        margin: 0 auto;
      }
    }

    .about-section {
      box-sizing: border-box;
      background: #fff;
      width: calc(100% - 24px);
      margin: 20px 12px 20px 12px;
      border-radius: 8px;
      padding-bottom: 14px;

      .about-us-content {
        display: flex;
        justify-content: center;
        gap: 30px;
        height: 30px;
        margin-top: 10px;

        > div {
          width: 98px;
          height: 30px;
          flex-shrink: 0;
          background-color: #f3f3f3;
          border-radius: 20px;
          line-height: 30px;
          text-align: center;
          font-family: Inter;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
  }
}

/* 开发环境测试按钮样式 */
.test-download-btn {
  position: fixed;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  background: #ac1140;
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  cursor: pointer;
  z-index: 10000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

  &:hover {
    background: #d91a5b;
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}
</style>
