<!-- 提供商Logo展示组件 -->
<template>
  <div class="about-us">
    <div class="logo-container">
      <img :src="nustar" class="nustar"></img>
      <ProviderLogo class="provider-logo"></ProviderLogo>
    </div>
    <van-row gutter="12">
      <van-col span="6">
        <div class="img-logo">
          <GcashSvg />
        </div>
      </van-col>
      <van-col span="6">
        <div class="img-logo">
          <MayaSvg />
        </div>
      </van-col>
      <van-col span="6" v-for="img in vaildThirdCompany" :key="img.id">
        <template v-if="img.url && !img.imageError">
          <van-image
            :src="img.url"
            @load="img.imageError = false"
            @error="img.imageError = true"
            :loading="img.short_name || img.provider"
          />
        </template>
        <div class="img-logo" v-if="img.imageError">
          {{ img.short_name || img.provider }}
        </div>
      </van-col>
    </van-row>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import nustar from "@/assets/frame/nustart/20.png";
import GcashSvg from "@/assets/icons/home-provider/gcash.svg";
import MayaSvg from "@/assets/icons/home-provider/maya.svg";
import ProviderLogo from "@/components/ProviderLogo.vue";

const gameStore = useGameStore();
const { vaildThirdCompany } = storeToRefs(gameStore);
</script>

<style scoped lang="scss">
.about-us {
  padding:0 12px 12px;
  width: 100%;
  box-sizing: border-box;
  .logo-container {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 10px;

    .nustar {
      width: 80px;
      height: 80px;
      flex-shrink: 0; // 固定宽度，不被压缩
    }

    .provider-logo {
      flex: 1; // 自适应剩余空间
      min-width: 0; // 允许收缩
      scale: 0.8;
    }
  }

  .van-col{
    margin-bottom: 6px;
  }

  .img-logo {
    height: 40px;
    line-height: 40px;
    width: 100%;
    text-align: center;
      background-color: #f9f9f9;
  }

  &:deep(.van-row) {
    .van-image {
      height: 40px;
      width: 100%;
      background-color: #f9f9f9;
      text-align: center;
    }
  }
}
</style>
