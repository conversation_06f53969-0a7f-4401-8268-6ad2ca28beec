<template>
  <ZPopOverlay :show="show">
    <div class="bonus-guide-dialog" @click.stop>
      <!-- 背景遮罩 -->
      <div class="dialog-overlay">
        <!-- 卡片实例 -->
        <div class="guide-card">
          <img src="@/assets/images/reward-wallet/example-card.png" alt="" />
        </div>
        <!-- 引导气泡 -->
        <div class="guide-bubble">
          <img src="@/assets/images/reward-wallet/guide-arrow.png" alt="" />
          <img src="@/assets/images/reward-wallet/guide-text.png" alt="" />
        </div>

        <!-- 确认按钮 -->
        <div class="confirm-section" @click="handleClose">
          <img src="@/assets/images/reward-wallet/guide-starlight.png" alt="" />
          <img src="@/assets/images/reward-wallet/guide-know.png" alt="" />
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script setup lang="ts">
defineOptions({
  name: "BonusGuideDialog",
});

interface Props {
  show: boolean;
}

interface Emits {
  (e: "update:show", value: boolean): void;
  (e: "confirm"): void;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
});

const emit = defineEmits<Emits>();

// 关闭弹窗
const handleClose = () => {
  emit("update:show", false);
  emit("confirm");
};
</script>

<style scoped lang="scss">
.dialog-overlay {
  height: 100vh;
  width: 100vw;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.guide-card {
  padding: 10px;
  img {
    margin: 0 auto;
    width: auto;
    height: 146px;
  }
}

// 引导气泡
.guide-bubble {
  width: 100vw;
  img {
    margin: 0 auto;
    width: 33px;
    height: 48px;
  }
  img:last-child {
    width: 330px;
    height: 125px;
  }
}

// 确认按钮
.confirm-section {
  position: relative;
  margin-top: 30px;
  width: 100vw;
  text-align: center;
  img {
    margin: 0 auto;
    width: 128px;
    height: 46px;
  }
  img:first-child {
    width: 175px;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
  }
}
</style>
