<template>
  <div class="progress-item">
    <div class="progress-label">
      <span>{{ label }}</span>
      <span class="progress-text"> {{ current > target ? target : current }} / {{ target }} </span>
    </div>
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: `${getPercentage}%` }"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  label: string;
  current: number;
  target: number;
  isFull?: boolean; // 用于充值进度的特殊标记
}>();

const getPercentage = computed(() => {
  if (props.isFull) return 100;
  if (props.target <= 0) return 100;
  if (props.current > props.target) return 100;
  return Math.min(100, Math.round((props.current / props.target) * 100));
});
</script>

<style scoped lang="scss">
.progress-item {
  margin-bottom: 16px;

  .progress-label {
    font-weight: 400;
    font-size: 12px;
    color: #222;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .progress-bar {
    height: 3px;
    background: #f4f8fb;
    border-radius: 4px;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: #ff8b32;
      border-radius: 4px;
      transition: width 0.3s ease;
    }
  }

  .progress-text {
    font-size: 12px;
    color: #999;
  }
}
</style>
