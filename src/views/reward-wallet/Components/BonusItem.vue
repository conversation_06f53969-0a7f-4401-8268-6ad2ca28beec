<template>
  <div class="bonus-item" :class="{ 'bonus-topIndex': isHighlighted }">
    <!-- 奖金头部 -->
    <div class="bonus-header" :class="getStatusClass">
      <div class="bonus-name">{{ bonus.task_name }}</div>
      <div v-if="bonus.task_status === TaskStatus.LOCKED" class="countdown">
        <ZIcon type="icon-timing" :size="14" color="#FF5100" />
        {{ bonus.countdown || "00:00:00" }}
      </div>
    </div>

    <!-- 奖金内容 -->
    <div class="bonus-content">
      <div class="bonus-amount">
        <IconCoin :size="26" />
        <span class="amount">{{ formatNumberToThousands(bonus.bonus) }}</span>
      </div>

      <!-- 操作按钮 -->
      <div class="bonus-action">
        <img
          v-if="bonus.task_status === TaskStatus.COMPLETED"
          class="claim-btn"
          ref="launchRef"
          @click="(event) => $emit('claim', bonus, event)"
          src="@/assets/images/reward-wallet/Claim-btn.png"
          alt="领取"
        />

        <div
          v-else-if="[TaskStatus.LOCKED, TaskStatus.EXPIRED].includes(bonus.task_status)"
          class="operation-btn"
          :class="isUnlockable ? 'unlock-btn' : 'expired-btn'"
          @click="(event) => $emit('unlock', bonus, event)"
        >
          <ZIcon type="icon-password" :size="14" :color="isUnlockable ? '#ff5100' : '#999'" />
          Unlock Now
        </div>

        <div
          v-else-if="bonus.task_status === TaskStatus.ONGOING"
          class="operation-btn ongoing-btn"
          @click="$emit('ongoing')"
        >
          Ongoing
          <ZIcon type="icon-point-to" :size="12" color="#FF8B32" />
        </div>

        <div v-else class="claimed-btn"></div>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="hasProgress" class="progress-section">
      <ProgressItem
        label="Bet(₱)"
        :current="bonus.bet_num"
        :target="bonus.bet_target_value || 1"
        :bonus="bonus"
      />
      <ProgressItem
        v-if="bonus.recharge_target_value > 0"
        label="Deposit(₱)"
        :current="bonus.recharge_done ? 1 : 0"
        :target="bonus.recharge_target_value"
        :is-full="bonus.recharge_done"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { formatNumberToThousands } from "@/utils/core/tools";
import IconCoin from "@/components/icons/IconCoin.vue";
import ProgressItem from "./ProgressItem.vue";

// 类型定义
enum TaskStatus {
  LOCKED = 1, // 未解锁
  ONGOING = 2, // 进行中
  COMPLETED = 3, // 可领取
  EXPIRED = 4, // 过期
  CLAIMED = 5, // 已领取
}
const props = defineProps<{
  bonus: any; // 实际项目中应使用WalletTaskItem类型
  isHighlighted: boolean;
  hasOngoingBonuses: boolean;
}>();

// 创建引用
const launchRef = ref<HTMLElement | null>(null);

const isUnlockable = computed(
  () => props.bonus.task_status === TaskStatus.LOCKED && !props.hasOngoingBonuses
);

const hasProgress = computed(
  () =>
    (props.bonus.bet_target_value > 0 || props.bonus.recharge_target_value > 0) &&
    props.bonus.task_status !== TaskStatus.COMPLETED
);

const getStatusClass = computed(() => {
  const statusMap = {
    [TaskStatus.LOCKED]: "bonus-pending",
    [TaskStatus.ONGOING]: "bonus-ongoing",
    [TaskStatus.COMPLETED]: "bonus-available",
    [TaskStatus.EXPIRED]: "bonus-expired",
    [TaskStatus.CLAIMED]: "bonus-claimed",
  };
  return statusMap[props.bonus.task_status] || "";
});

// 暴露 launchRef 和 bonus 给父组件
defineExpose({
  launchRef,
  bonus: props.bonus,
});
</script>

<style scoped lang="scss">
// 奖金项
.bonus-item {
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease;
  background: #fff;

  &.bonus-topIndex:first-child {
    position: relative;
    z-index: 9999;
  }
}
// 奖金头部
.bonus-header {
  line-height: 40px;
  height: 40px;
  padding: 0 12px;
  position: relative;

  // 各状态顶部样式调整
  &.bonus-pending,
  &.bonus-claimed,
  &.bonus-expired {
    background: rgba(255, 139, 51, 0.1);
    color: #222;
  }
  &.bonus-available,
  &.bonus-ongoing {
    background: linear-gradient(270deg, #ff9849 0%, #ff7811 100%);
    color: #fff;
  }

  .bonus-name {
    font-weight: 600;
    font-size: 16px;
    letter-spacing: 0;
  }

  .countdown {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-family: "D-DIN";
    font-size: 13px;
    color: #ff5100;
    font-weight: 700;
    background: rgba(255, 31, 4, 0.08);
    // padding: 0 10px;
    line-height: 24px;
    border-radius: 0 16px 0 16px;
    width: 86px;
  }
}

// 奖金内容
.bonus-content {
  padding: 10px 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .bonus-amount {
    display: flex;
    align-items: center;
    gap: 12px;

    .amount {
      font-family: "D-DIN";
      font-weight: 700;
      font-size: 24px;
      line-height: 100%;
      letter-spacing: 0;
      color: #222222;
    }
  }
}

// 领取样式
.claim-btn {
  width: 124px;
  height: 36px;
}

.operation-btn {
  border: none;
  font-weight: 700;
  font-size: 14px;
  letter-spacing: 0px;
  width: 124px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  border-radius: 999px;
}
.unlock-btn {
  background: rgba(255, 139, 50, 0.1);
  color: #ff5100;
}
.expired-btn {
  color: #999;
  background: #f4f7fd;
}

.ongoing-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 20px;
  color: #ff8b32;
  width: 77px;
}

// 进度条部分
.progress-section {
  padding: 0 16px 16px;
  background: transparent;

  .progress-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .progress-label {
      font-weight: 400;
      font-size: 12px;
      line-height: 100%;
      letter-spacing: 0;
      color: #222222;
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .progress-bar {
      height: 3px;
      background: #f4f8fb;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 0;

      .progress-fill {
        height: 100%;
        background: #ff8b32;
        border-radius: 4px;
        transition: width 0.3s ease;
      }
    }

    .progress-text {
      font-size: 12px;
      color: #999;
    }
  }
}
</style>
