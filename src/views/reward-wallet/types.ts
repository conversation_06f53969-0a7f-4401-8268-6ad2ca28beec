/**
 * 奖金钱包相关类型定义
 */

// 奖金状态枚举
export enum BonusStatus {
  PENDING = 'pending',      // 待解锁
  AVAILABLE = 'available',  // 可领取
  CLAIMED = 'claimed',      // 已领取
  EXPIRED = 'expired',      // 已过期
  ONGOING = 'ongoing'       // 进行中
}

// 奖金类型枚举
export enum BonusType {
  REGISTRATION = 'registration',    // 注册奖金
  DEPOSIT = 'deposit',             // 充值奖金
  CASHBACK = 'cashback',           // 返水奖金
  TOURNAMENT = 'tournament',        // 锦标赛奖金
  VIP = 'vip',                     // VIP奖金
  DAILY = 'daily',                 // 每日奖金
  WEEKLY = 'weekly',               // 每周奖金
  SPECIAL = 'special'              // 特殊活动奖金
}

// 解锁条件类型
export interface UnlockCondition {
  type: 'bet' | 'deposit' | 'time';  // 条件类型：投注、充值、时间
  current: number;                    // 当前进度
  required: number;                   // 需要达到的目标
  unit?: string;                      // 单位（如 ₱、次数等）
}

// 奖金项接口
export interface BonusItem {
  id: string | number;                // 奖金ID
  name: string;                       // 奖金名称
  amount: number;                     // 奖金金额
  status: BonusStatus;                // 奖金状态
  type: BonusType;                    // 奖金类型
  description?: string;               // 奖金描述
  expireTime?: number;                // 过期时间戳
  unlockConditions?: UnlockCondition[]; // 解锁条件
  claimedTime?: number;               // 领取时间戳
  icon?: string;                      // 图标URL
  countdown?: {                       // 倒计时
    hours: string;
    minutes: string;
    seconds: string;
  };
}

// 奖金钱包状态接口
export interface BonusWalletState {
  totalBalance: number;               // 总余额
  availableCount: number;             // 可领取数量
  bonusList: BonusItem[];            // 奖金列表
  loading: boolean;                   // 加载状态
  showGuideDialog: boolean;           // 是否显示引导弹窗
  selectedBonus?: BonusItem;          // 当前选中的奖金（用于弹窗显示）
}

// API 请求参数接口
export interface GetBonusListParams {
  page?: number;
  pageSize?: number;
  status?: BonusStatus;
  type?: BonusType;
}

// API 响应接口
export interface BonusListResponse {
  list: BonusItem[];
  total: number;
  totalBalance: number;
  availableCount: number;
}

// 领取奖金请求参数
export interface ClaimBonusParams {
  bonusId: string | number;
}

// 领取奖金响应
export interface ClaimBonusResponse {
  success: boolean;
  message: string;
  newBalance?: number;
}

// 奖金进度信息
export interface BonusProgress {
  bonusId: string | number;
  progress: number;        // 进度百分比 0-100
  current: number;         // 当前值
  target: number;          // 目标值
  unit: string;           // 单位
}
