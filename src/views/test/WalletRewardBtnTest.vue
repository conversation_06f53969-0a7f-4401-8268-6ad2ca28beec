<template>
  <div class="wallet-btn-test">
    <h2>WalletRewardBtn 图片序列动画测试</h2>
    <p>支持 claim 和 standby 两种状态，每种状态使用不同目录下的 37 张图片生成循环动画</p>

    <div class="test-section">
      <h3>动画状态测试</h3>
      <div class="state-grid">
        <div class="state-item">
          <h4>Standby 状态 (24x24px)</h4>
          <WalletRewardBtn state="standby" @click="handleStateClick('standby')" />
        </div>
        <div class="state-item">
          <h4>Claim 状态 (48x48px)</h4>
          <WalletRewardBtn state="claim" @click="handleStateClick('claim')" :defaultSize="48" />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>默认尺寸对比</h3>
      <div class="size-comparison">
        <div class="comparison-item">
          <span>Standby (24x24px)</span>
          <WalletRewardBtn state="standby" @click="handleStateClick('standby')" />
        </div>
        <div class="comparison-item">
          <span>Claim (48x48px)</span>
          <WalletRewardBtn state="claim" :defaultSize="48" @click="handleStateClick('claim')" />
        </div>
        <div class="comparison-item">
          <span>自定义尺寸 Claim / standby (32x32px)</span>
          <WalletRewardBtn
            state="claim"
            :defaultSize="32"
            @click="handleStateClick('claim-custom')"
          />
          <WalletRewardBtn
            state="standby"
            :defaultSize="32"
            @click="handleStateClick('standby-custom')"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>状态切换测试</h3>
      <div class="demo-item">
        <span>当前状态: {{ currentState }}</span>
        <WalletRewardBtn :state="currentState" @click="handleBasicClick" />
        <div class="state-buttons">
          <button @click="switchToStandby" class="state-btn">切换到 Standby</button>
          <button @click="switchToClaim" class="state-btn">切换到 Claim</button>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同尺寸测试</h3>
      <div class="size-grid">
        <div class="size-item">
          <h4>16x16px</h4>
          <WalletRewardBtn
            :animation-config="{ width: 16, height: 16, fps: 12, loop: true }"
            @click="handleSizeClick('16x16')"
          />
        </div>
        <div class="size-item">
          <h4>24x24px (默认)</h4>
          <WalletRewardBtn @click="handleSizeClick('24x24')" />
        </div>
        <div class="size-item">
          <h4>32x32px</h4>
          <WalletRewardBtn
            :animation-config="{ width: 32, height: 32, fps: 12, loop: true }"
            @click="handleSizeClick('32x32')"
          />
        </div>
        <div class="size-item">
          <h4>48x48px</h4>
          <WalletRewardBtn
            :animation-config="{ width: 48, height: 48, fps: 12, loop: true }"
            @click="handleSizeClick('48x48')"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>不同帧率测试</h3>
      <div class="fps-grid">
        <div class="fps-item">
          <h4>6 FPS (慢速)</h4>
          <WalletRewardBtn
            :animation-config="{ width: 32, height: 32, fps: 6, loop: true }"
            @click="handleFpsClick('6fps')"
          />
        </div>
        <div class="fps-item">
          <h4>12 FPS (默认)</h4>
          <WalletRewardBtn
            :animation-config="{ width: 32, height: 32, fps: 12, loop: true }"
            @click="handleFpsClick('12fps')"
          />
        </div>
        <div class="fps-item">
          <h4>24 FPS (快速)</h4>
          <WalletRewardBtn
            :animation-config="{ width: 32, height: 32, fps: 24, loop: true }"
            @click="handleFpsClick('24fps')"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>自动播放测试</h3>
      <div class="demo-item">
        <span>自动播放动画：</span>
        <WalletRewardBtn
          :animation-config="{ width: 32, height: 32, fps: 12, loop: true, autoplay: true }"
          @click="handleAutoplayClick"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>单次播放测试</h3>
      <div class="demo-item">
        <span>播放一次后停止：</span>
        <WalletRewardBtn
          :animation-config="{ width: 32, height: 32, fps: 12, loop: false }"
          @click="handleSinglePlayClick"
        />
      </div>
    </div>

    <div class="test-section">
      <h3>禁用动画测试</h3>
      <div class="demo-item">
        <span>静态图标：</span>
        <WalletRewardBtn :enable-animation="false" @click="handleDisabledClick" />
      </div>
    </div>

    <div class="test-section">
      <h3>控制方法测试</h3>
      <div class="control-demo">
        <div class="control-item">
          <WalletRewardBtn
            ref="controlBtn"
            :animation-config="{ width: 48, height: 48, fps: 12, loop: true }"
          />
        </div>
        <div class="control-buttons">
          <button @click="playControlAnimation" class="control-btn">播放</button>
          <button @click="pauseControlAnimation" class="control-btn">暂停</button>
          <button @click="stopControlAnimation" class="control-btn">停止</button>
        </div>
      </div>
    </div>

    <!-- 事件日志 -->
    <div class="log-section">
      <h3>事件日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import WalletRewardBtn from "@/views/home/<USER>/WalletRewardBtn.vue";

// 响应式数据
const logs = ref([]);
const controlBtn = ref(null);
const currentState = ref("standby");

// 添加日志
const addLog = (event, data = "") => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, "0")}:${now
    .getMinutes()
    .toString()
    .padStart(2, "0")}:${now.getSeconds().toString().padStart(2, "0")}`;

  logs.value.unshift({
    time,
    event,
    data,
  });

  // 限制日志数量
  if (logs.value.length > 10) {
    logs.value.pop();
  }
};

// 事件处理函数
const handleBasicClick = () => {
  addLog("基础动画点击");
};

const handleSizeClick = (size) => {
  addLog("尺寸测试点击", size);
};

const handleFpsClick = (fps) => {
  addLog("帧率测试点击", fps);
};

const handleAutoplayClick = () => {
  addLog("自动播放点击");
};

const handleSinglePlayClick = () => {
  addLog("单次播放点击");
};

const handleDisabledClick = () => {
  addLog("禁用动画点击");
};

const handleStateClick = (state) => {
  addLog(`${state} 状态动画点击`);
};

// 状态切换方法
const switchToStandby = () => {
  currentState.value = "standby";
  addLog("切换到 Standby 状态");
};

const switchToClaim = () => {
  currentState.value = "claim";
  addLog("切换到 Claim 状态");
};

// 控制方法
const playControlAnimation = () => {
  if (controlBtn.value) {
    controlBtn.value.playAnimation();
    addLog("手动播放动画");
  }
};

const pauseControlAnimation = () => {
  if (controlBtn.value) {
    controlBtn.value.pauseAnimation();
    addLog("手动暂停动画");
  }
};

const stopControlAnimation = () => {
  if (controlBtn.value) {
    controlBtn.value.stopAnimation();
    addLog("手动停止动画");
  }
};
</script>

<style scoped>
.wallet-btn-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 10px;
}

p {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.demo-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 6px;
}

.size-grid,
.fps-grid,
.state-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.size-item,
.fps-item,
.state-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  text-align: center;
}

.state-item p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.size-comparison {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.comparison-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.comparison-item span {
  font-size: 14px;
  color: #555;
  font-weight: 500;
}

.state-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.state-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: #28a745;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.state-btn:hover {
  background: #218838;
}

.size-item h4,
.fps-item h4 {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.control-demo {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.control-btn:hover {
  background: #0056b3;
}

.log-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-section h3 {
  color: #333;
  margin-bottom: 16px;
  border-bottom: 2px solid #28a745;
  padding-bottom: 8px;
}

.log-content {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 12px;
  background: #f9f9f9;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 60px;
}

.log-event {
  color: #007bff;
  min-width: 100px;
}

.log-data {
  color: #28a745;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wallet-btn-test {
    padding: 12px;
  }

  .test-section {
    padding: 16px;
  }

  .size-grid,
  .fps-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .control-demo {
    flex-direction: column;
    text-align: center;
  }
}
</style>
