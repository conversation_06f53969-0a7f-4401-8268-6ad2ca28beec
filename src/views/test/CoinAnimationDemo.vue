<template>
  <div class="test-page">
    <div class="target">
      <Balance ref="balanceRef" :coinSize="40" />
    </div>
    <!-- 点击按钮 -->
    <ZButton class="custom-btn2" @click="hanldeLaunch" ref="launchRef" size="small">
      点击发射金币
    </ZButton>
    <!-- 引入金币动画组件 -->
    <CoinAnimation ref="coinAnimationRef" :start-ref="launchRef" :end-ref="balanceRef?.coinRef"
      @animation-end="handleAnimationEnd" @animation-start="handleAnimationStart" />

  </div>
</template>
<script setup>
import { ref } from 'vue';
import CoinAnimation from '@/components/CoinAnimation/index.vue';

// 模板引用
const coinAnimationRef = ref(null); // 金币组件引用
const balanceRef = ref(null); // 钱包金币 组件引用
const launchRef = ref(null); // 发射位置引用


// 启动动画
const hanldeLaunch = async () => {
  if (coinAnimationRef.value) {
    await coinAnimationRef.value.startAnimation();
  }
};

const handleAnimationStart = () => {
  balanceRef.value?.startAnimation();
};

// 动画结束回调
const handleAnimationEnd = () => {
  balanceRef.value?.stopAnimation();
}
</script>

<style scoped>
.custom-btn {
  position: fixed;
  bottom: 10px;
  left: 50%;
  width: 120px;
  /* 固定宽度，防止内容变化导致位置偏移 */
  min-width: 120px;
  /* 最小宽度 */
}

.custom-btn2 {
  position: fixed;
  bottom: 100px;
  left: 50%;
  width: 140px;
  /* 固定宽度，防止内容变化导致位置偏移 */
  min-width: 140px;
}


.test-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #000;
  overflow: hidden;
}


/* 左上角目标区域 */
.target {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 50px;
}
</style>
