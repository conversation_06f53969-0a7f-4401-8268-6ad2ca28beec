<template>
  <div class="tournament-active-page">
    <XPage :navTitle="tournamentData.title" :initShowNav="true"
      :navBarStyle="{ backgroundColor: '#4F01E1', color: '#fff', fontSize: '18px' }" @back.stop="onBack">
      <template #right>
        <van-icon size="24" @click="showHelp" name="question-o" />
      </template>
      <ZPopOverlay :show="loading">
        <ZLoading />
      </ZPopOverlay>
      <div class="tournament-waiting" :style="{
        'overflow-y': showRankList ? 'hidden' : 'auto',
        'max-height': showRankList ? '100vh' : 'auto',
      }">
        <!-- 活动详情页 -->
        <div v-if="!showRankList">
          <!-- 总奖金展示 -->
          <div class="total-prize-section">
            <h2 class="prize-label">Total Prize</h2>
            <div class="prize-amount" :data-text="`₱${formatNumber(tournamentData.total_price)}`">
              {{ "₱" + formatNumber(tournamentData.total_price) }}
            </div>
          </div>

          <!-- 奖台展示 -->
          <div class="podium-section">
            <div class="podium-container">
              <div v-for="(item, idx) in [1, 0, 2]" :key="item"
                :class="`podium-item ${['second', 'first', 'third'][idx]}`">
                <div class="podium-amount"
                  :data-text="`₱${formatNumber(tournamentData.top_rankings[item]?.bonus || 0)}`">
                  ₱{{ formatNumber(tournamentData.top_rankings[item]?.bonus || 0) }}
                </div>
              </div>
            </div>
          </div>

          <!-- 等待状态 -->
          <!-- v-if="countdown.hours || countdown.minutes || countdown.seconds" -->
          <div class="waiting-section" :class="{ 'in-middle': progressPercentage >= 100 }">
            <p class="waiting-text">Waiting for the start of the game</p>

            <!-- 倒计时 -->
            <div class="countdown">
              <div class="time-block">
                <span class="time-value">
                  {{ countdown.hours || "00" }}
                </span>
                <span class="time-label">H</span>
              </div>
              <span class="separator">:</span>
              <div class="time-block">
                <span class="time-value">
                  {{ countdown.minutes || "00" }}
                </span>
                <span class="time-label">M</span>
              </div>
              <span class="separator">:</span>
              <div class="time-block">
                <span class="time-value">
                  {{ countdown.seconds || "00" }}
                </span>
                <span class="time-label">S</span>
              </div>
            </div>
            <!-- 历史排名按钮 -->
            <div class="previous-ranking-btn" v-show="historyRankList.length > 0">
              <img @click="showPreviousRanking" src="@/assets/images/tournament/pre-rank-btn.png" alt="" />
            </div>
          </div>

          <!-- 锦标赛门槛 -->
          <div class="threshold-section" v-if="progressPercentage < 100">
            <div class="threshold-header">
              <img src="@/assets/images/tournament/turn-right.png" alt="" />
              <span class="threshold-title">Tournament Threshold</span>
              <img src="@/assets/images/tournament/turn-left.png" alt="" />
            </div>

            <div class="threshold-progress" v-if="tournamentData.registration_rule > 0">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
              </div>
              <div class="progress-text">
                {{ progressText }} {{ tournamentData.my_registration_value }}/{{
                  tournamentData.registration_requirement
                }}
              </div>
            </div>
            <!-- <div class="status-div" style="opacity: 0.5">Keep Going</div> -->
          </div>

          <!-- 继续游戏按钮 -->
          <div class="keep-going-btn" v-show="showKeepGoingBtn || isEnter" @click="keepGoing">
            <img src="@/assets/images/tournament/yellow-btn-bg.png" alt="" />
            <span data-text="Keep Going!">Keep Going!</span>
          </div>
        </div>
        <!-- 排行榜列表 -->
        <rank-list :historyRankList="historyRankList" :currentRankList="rankingList" :detailData="tournamentData"
          v-if="showRankList"></rank-list>
      </div>
    </XPage>
    <popup-help v-if="isHelp" v-model:visible="isHelp" :img-list="tournamentData?.detail_image_urls" />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称
defineOptions({ name: "TournamentActivePage" });

import { ref, computed, onMounted, onUnmounted, onBeforeMount } from "vue";
import { useRouter } from "vue-router";
import {
  getQueryByUrl,
  getQuery,
  isEmpty,
  parseTime,
  formatTime,
  formatNumberToThousands,
  getServerSideImageUrl,
  getUserAvatar,
} from "@/utils/core/tools";
import {
  playerBettingSummary,
  playerGameDistribution,
  tournamentDetails,
  getLeaderboardList,
} from "@/api/promos";
import RankList from "./rank-list.vue";
import PopupHelp from "./Components/PopupHelp.vue";
import { TopRank, DetailInfo, RankItem } from "../configs";
import { processTournamentData } from "../utils/promoUtils";
import { useDepositStore } from "@/stores/deposit";
import { useGameStore } from "@/stores/game";

const router = useRouter();
const loading = ref(true);
const depositStore = useDepositStore();
const gameStore = useGameStore();

const CHAMPIONSHIP_STATUS = {
  WAITING: 0,
  ONGOING: 1,
  ENDED: 2,
};
const POGRESS_TEXTS = ["", "Deposit ", "Bet "];
const BOTTOM_BTNTEXTS = ["Keep Going!", "Registration", "Cancel Registration"];
// 外部传入数据
const query = getQueryByUrl(location.search);
// 当前展示的锦标赛详情
const curId = ref<string>(query.id || "");

// 锦标赛 活动页面数据
const tournamentData = ref<detailInfo>({
  id: "",
  status: 0,
  matches_rule: 0,
  title: "",
  title_image: "",
  detail_image_urls: [],
  history_detail_image_urls: [],
  period_type: 0,
  start_time: 0,
  end_time: 0,
  registration_rule: 0,
  registration_requirement: "0",
  my_registration_value: "0",
  game_ids: [],
  vendors: [],
  brands: [],
  game_types: [],
  total_price: 0,
  my_rank: {
    rank: 0,
    avatar: "",
    nickname: "",
    score: "0",
    bonus: "0",
    user_id: "",
  },
  my_historyRank: {
    rank: 0,
    avatar: "",
    nickname: "",
    score: "0",
    bonus: "0",
    user_id: "",
  },
  top_rankings: [],
});
const progressText = computed(() => {
  return POGRESS_TEXTS[tournamentData.value.registration_rule];
});
const showRankList = ref(false); //判断当前是否展示排行榜
watch(tournamentData, () => {
  showRankList.value = tournamentData.value.status !== CHAMPIONSHIP_STATUS.WAITING;
});
const isHelp = ref(false);

// 倒计时数据
const countdown = ref({
  hours: "",
  minutes: "",
  seconds: "",
});
// 排行榜列表
const rankingList = ref([]); // 当前
const historyRankList = ref([]); // 之前

// 计算进度百分比，判断NaN
const progressPercentage = computed(() => {
  if (Number(tournamentData.value.registration_requirement) <= 0) return 100;
  const myValue = Number(tournamentData.value.my_registration_value);
  const requirement = Number(tournamentData.value.registration_requirement);
  if (isNaN(myValue) || isNaN(requirement) || requirement === 0) return 0;
  return Math.min((myValue / requirement) * 100, 100);
});

// 是否达到参赛门槛
const isEnter = computed(() => {
  // 门槛
  const threshold = Number(tournamentData.value.registration_requirement);
  // 当前投注金额
  const curBet = Number(tournamentData.value.my_registration_value);
  return curBet >= threshold;
});

// 格式化数字显示
const formatNumber = (num: number): string => {
  return formatNumberToThousands(num, false);
};

// 倒计时定时器
let countdownTimer: NodeJS.Timeout | null = null;

// /** 活动时间范围 */
const activityTimes = computed(() => {
  const { start_time, end_time, period_type } = tournamentData.value;
  if (isEmpty(start_time) || isEmpty(end_time)) return "";
  const format = period_type !== 2 ? "hh:mm" : "YYYY.MM.DD";
  const startTime = parseTime(start_time * 1000, format);
  const endTime = parseTime(end_time * 1000, format);
  return `${startTime}-${endTime}`;
});

/** 获取活动详情 */
const getTournamentData = async (payload = {}) => {
  if (!curId.value) return;
  try {
    const params = {
      activity_id: curId.value,
      is_history: false,
      ...payload,
    };
    const res = await tournamentDetails(params);
    if (isEmpty(res)) return;
    if (payload?.is_history) {
      tournamentData.value.my_historyRank = res.my_rank || {};
      tournamentData.value.history_detail_image_urls = res.detail_image_urls || {};
      return;
    }
    tournamentData.value = {
      ...res,
      ...processTournamentData(res),
      detail_image_urls: res.detail_image_urls.map((t) => getServerSideImageUrl(t)) || [],
    };
    console.log("获取活动详情", tournamentData.value);
  } catch (error) {
    console.log(error);
  }
};

/**
 * 排行历史榜列表获取
 */
const getRankList = async (payload?: any) => {
  const params = {
    activity_id: curId.value,
    is_history: false,
    page: 1,
    page_size: 100,
    ...payload,
  };
  const data = await getLeaderboardList(params);
  if (isEmpty(data.list)) {
    if (params.is_history) historyRankList.value = [];
    else rankingList.value = [];
    return;
  }
  const list = data.list;
  if (params.is_history) {
    historyRankList.value = list;
  } else rankingList.value = list;
};

// 获取计算倒计时数据
const setTime = () => {
  if (isEmpty(tournamentData.value.start_time)) {
    if (countdownTimer) clearInterval(countdownTimer);
    return;
  }
  const milliSecond = tournamentData.value.end_time * 1000 - tournamentData.value.start_time * 1000;
  // 当倒计时为负数说明活动结束
  if (milliSecond < 0) {
    if (countdownTimer) clearInterval(countdownTimer);
    return;
  }
  const [hour, minute, second] = formatTime(milliSecond, true).split(":");
  countdown.value = {
    hours: hour,
    minutes: minute,
    seconds: second,
  };
  startCountdown();
};

// 初始化数据
const initData = async () => {
  loading.value = true;
  try {
    await getTournamentData();
    setTime();
    getRankList();
    getRankList({ is_history: true });
    getTournamentData({ is_history: true });
  } catch (error) {
    console.log("初始化数据", error);
  } finally {
    loading.value = false;
  }
};
// 显示底部按钮
const showKeepGoingBtn = computed(() => {
  const {
    registration_rule: rule,
    my_registration_value,
    registration_requirement,
  } = tournamentData.value;
  console.log("showKeepGoingBtn", tournamentData.value);
  const value = Number(my_registration_value);
  const requirement = Number(registration_requirement);
  return rule > 0 && value < requirement;
});

// 启动倒计时
const startCountdown = () => {
  countdownTimer = setInterval(() => {
    let totalSeconds =
      parseInt(countdown.value.hours) * 3600 +
      parseInt(countdown.value.minutes) * 60 +
      parseInt(countdown.value.seconds);

    if (totalSeconds > 0) {
      totalSeconds--;

      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;

      countdown.value = {
        hours: hours.toString().padStart(2, "0"),
        minutes: minutes.toString().padStart(2, "0"),
        seconds: seconds.toString().padStart(2, "0"),
      };
    } else {
      // 倒计时结束
      if (countdownTimer) {
        clearInterval(countdownTimer);
      }
      // 锦标赛开始
      tournamentData.value.status = CHAMPIONSHIP_STATUS.ONGOING;
      tournamentData.value.statusName = "ongoing";
    }
  }, 1000);
};

// 显示帮助
const showHelp = () => {
  isHelp.value = true;
};
// 显示历史排名
const showPreviousRanking = async () => {
  showRankList.value = true;
};
// 返回或者排行榜页面消失
const onBack = (event: Event) => {
  event.preventDefault();
  // 当已结束的活动时仅有排行榜页面，直接返回，如果不是已结束的活动则显示活动详情页
  if (showRankList.value && tournamentData.value.status === CHAMPIONSHIP_STATUS.WAITING)
    showRankList.value = false;
  else router.back();
};

const handleBetNow = () => {
  gameStore.handleBetNowNavigation({
    vendors: tournamentData.value.vendors.map(String),
    game_types: tournamentData.value.game_types.map(String),
  });
};

// 继续 bet or deposit
const keepGoing = () => {
  if (tournamentData.value.registration_rule === 1) {
    depositStore.openDialog()
  } else if (tournamentData.value.registration_rule === 2) {
    // depositStore.clickBetNow();
    handleBetNow();
  }
};

onBeforeMount(() => {
  initData();
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style scoped lang="scss">
.tournament-active-page {
  height: 100%;
  width: 100%;
}

.tournament-waiting {
  background: url("@/assets/images/tournament/tournament-bg.png") no-repeat;
  background-size: 100% 100%;
  background-position: top -50px left 0;
  min-height: 100vh;
  color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  // overflow-y: auto;
  padding: 56px 0 20px;
  position: relative;
  z-index: 5;
  background-color: #0d17d4;
}

.total-prize-section {
  text-align: center;

  .prize-label {
    background: linear-gradient(180deg, #fffef4 20%, #fff588 80%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-size: 20px;
    font-weight: 700;
    margin: 0 0 8px 0;
  }

  .prize-amount {
    font-family: "Inter";
    background: linear-gradient(180deg, #fffef4 25%, #fff588 70%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    font-size: 40px;
    font-weight: 900;

    // 使用伪元素描边
    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 6px #1300ff;
      text-stroke: 6px #1300ff;
      z-index: -1;
    }
  }
}

.podium-section {
  position: relative;
  margin-bottom: 20px;
  height: 194px;

  .podium-container {
    background: url("@/assets/images/tournament/rank-top-bg.png") no-repeat;
    background-size: 336px 194px;
    background-position: top 0 left 50%;
    /*  display: flex;
    justify-content: center;
    align-items: flex-end; */
    height: 174px;
    padding: 0 16px;
    position: relative;

    .podium-item {
      display: inline-block;
      position: absolute;
      width: 28%;
      font-weight: 900;
      text-align: left;

      &.first {
        width: 42%;
        font-size: 24px;
        left: 29%;
        bottom: 44px;

        .podium-amount {
          color: #ff3d67;
          position: relative;
          z-index: 1;

          &::before {
            content: attr(data-text);
            position: absolute;
            -webkit-text-stroke: 6px #fffbf0;
            text-stroke: 6px #fffbf0;
            z-index: -1;
          }
        }
      }

      &.second {
        left: 23px;
        bottom: 16px;
        font-size: 18px;

        .podium-amount {
          color: #1e74ff;
          position: relative;
          z-index: 1;

          &::before {
            content: attr(data-text);
            position: absolute;
            -webkit-text-stroke: 6px #fffbf0;
            text-stroke: 6px #fffbf0;
            z-index: -1;
          }
        }
      }

      &.third {
        font-size: 18px;
        right: 23px;
        bottom: 16px;

        .podium-amount {
          color: #9e0fff;
          position: relative;
          z-index: 1;

          &::before {
            content: attr(data-text);
            position: absolute;
            -webkit-text-stroke: 6px #fffbf0;
            text-stroke: 6px #fffbf0;
            z-index: -1;
          }
        }
      }
    }

    .podium-amount {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
    }
  }
}

.waiting-section {
  text-align: center;
  margin-bottom: 40px;

  &.in-middle {
    margin-top: 60px;

    .waiting-text {
      font-weight: 900;
      font-size: 18px;
    }
  }

  .waiting-text {
    font-family: "Inter";
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
    text-align: center;
    color: #fff588;
  }

  .countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-bottom: 10px;

    .time-block {
      padding: 10px 2px;
      min-width: 60px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      .time-value {
        background: url("@/assets/images/tournament/time-wrap.png") no-repeat;
        background-size: 46px 46px;
        background-position: left -5px top -4px;
        position: relative;
        font-family: "D-DIN";
        display: block;
        color: #ea00ff;
        font-size: 20px;
        width: 36px;
        height: 36px;
        font-weight: 700;
        text-align: center;
        line-height: 40px;
      }

      .time-label {
        display: block;
        color: #93a4ff;
        font-size: 12px;
        font-weight: 800;
        margin-top: 2px;
      }
    }

    .separator {
      color: #fff;
      font-family: "D-DIN";
      font-weight: 700;
      margin-top: -20px;
      font-size: 16px;
      text-align: center;
    }
  }
}

.previous-ranking-btn {
  width: 100%;
  text-align: center;

  img {
    width: 154px;
    margin: 0 auto;
  }
}

.threshold-section {
  margin-bottom: 40px;

  .threshold-header {
    font-family: "Inter";
    font-weight: 900;
    font-style: Black;
    font-size: 18px;
    line-height: 100%;
    text-align: center;

    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 16px;

    img {
      width: 21px;
    }

    .threshold-title {
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .threshold-progress {
    margin: 0 auto;
    position: relative;
    background: linear-gradient(160.16deg, #6200d9 13.26%, #8417ff 61.35%),
      linear-gradient(180deg, #0200d8 0%, #4341fd 100%);
    width: 317px;
    border-radius: 40px;
    height: 30px;
    padding: 2px;

    .progress-bar {
      width: 311px;
      height: 25px;
      background: #f3ffff;
      border-radius: 40px;
      overflow: hidden;
      margin-bottom: 8px;
      box-shadow: 0px -1px 3px 0px #d9f7f7 inset;
      box-shadow: 0px 1px 3px 0px #d9f7f7 outset;
      padding: 1px;
      box-sizing: content-box;

      .progress-fill {
        height: 100%;
        background: #00faff;
        border-radius: 40px;
        box-shadow: 0px 1px 2px 0px #ffffff inset;
        box-shadow: 0px -1px 2px 0px #0000001a inset;
        border-right: 0;
      }
    }

    .progress-text {
      width: 100%;
      position: absolute;
      top: 0px;
      font-family: "Inter";
      font-weight: 700;
      text-align: center;
      color: #fff;
      font-size: 14px;
      background: #1f24ff;
      -webkit-background-clip: text;
      -webkit-text-fill-color: #fff;
      -webkit-text-stroke: 3px transparent;
      line-height: 30px;
    }
  }

  .status-div {
    width: 147px;
    height: 36px;
    border-radius: 40px;
    line-height: 38px;
    margin: 10px auto 0;
    text-align: center;
    color: #031ad6;
    font-family: "Inter";
    font-weight: 700;
    font-size: 14px;
    text-align: center;
    background: #68e3ff;
  }
}

.keep-going-btn {
  width: 100%;
  position: relative;

  img {
    width: auto;
    height: 73px;
    margin: 0 auto;
  }

  span {
    font-family: "Inter";
    font-weight: 900;
    font-size: 19px;
    display: inline-block;
    width: 100%;
    text-align: center;
    color: #fff;
    position: absolute;
    z-index: 1;
    top: 9px;

    &::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 4px #a94f00;
      text-stroke: 4px #a94f00;
      z-index: -1;
    }
  }
}
</style>
