<template>
  <van-overlay
    :show="visible"
    class-name="graphic-popup-overlay"
    z-index="20"
    teleport=".xpage-container"
  >
    <div class="modal-content" @click.stop>
      <!-- 头部导航 -->
      <TabHeader
        :tabs="TAB_TEXTS"
        :active-tab="activeTab"
        @update:active-tab="activeTab = $event"
      />

      <!-- 内容区域 -->
      <div class="modal-body">
        <!-- 游戏分布标签页 -->
        <BettingTab
          v-if="activeTab === TAB_TEXTS[0]"
          ref="bettingTabRef"
          :series-data="list"
          :table-data="pieLegends"
          :active-filter="activeFilter"
          @update:active-filter="activeFilter = $event"
        />

        <!-- 投注概览标签页 -->
        <DistributionTab
          :indicator-list="indicatorList"
          :series-list="radarList"
          :radar-data="radarData"
          v-else-if="activeTab === TAB_TEXTS[1]"
          ref="distributionTabRef"
        />
      </div>

      <!-- 关闭按钮 -->
      <button class="close-btn" @click="close">
        <ZIcon type="icon-jia" :size="16" />
      </button>
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
defineOptions({ name: "GraphicPopup" });

import { ref, computed, onMounted, watch } from "vue";
import TabHeader from "./TabHeader.vue";
import BettingTab from "./BettingTab.vue";
import DistributionTab from "./DistributionTab.vue";
import { playerBettingSummary, playerGameDistribution } from "@/api/promos";
import { isEmpty, amountFormatThousands } from "@/utils/core/tools";
import type { RadarItem, RadarSeriesItem, RadarData, PieListItem, SeriesItem } from "../../configs";

interface Game {
  name: string;
  bet: string;
  color: string;
  colorCode: string;
}

interface user {
  avatar: string;
  bonus: number;
  nickname: string;
  rank: number;
  score: string;
  user_id: string;
}

interface Props {
  visible: boolean;
  userInfo: user;
  activityId: string;
  isHistory: boolean;
}

// 饼图调色盘顺序
const colors = ["#7086FD", "#6FD195", "#FFAE4C", "#07DBFA", "#F28AFC", "#988AFC"];

/** 雷达图刻度线配置项 */
const indicatorList = ref([
  { name: "Bet Amount", max: 10, color: "#3E70FF" },
  { name: "Win Rate", max: 10, color: "#00DAE2" },
  { name: "Game Numbers", max: 10, color: "#FF9D00" },
  { name: "Net Win", max: 10, color: "#FF4A3D" },
  { name: "Bet Times", max: 10, color: "#9A2FFF" },
]);

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  userInfo: {},
  activityId: "",
  isHistory: false,
});

const emits = defineEmits<{ close: [] }>();

const TAB_TEXTS = ["distribution", "betting"];
// 响应式状态
const activeTab = ref(TAB_TEXTS[0]);
//{ 0:'Valid Bet', 1:'Net Win', 2:'Win Rate'}
const activeFilter = ref("0");
const gameTypeStr = ["BetAmount", "NetWin", "WinRate"];

// 雷达图展示数据
const radarList = ref<Array<RadarSeriesItem>>([{ name: "", value: [] }]);

/** 雷达图数据 */
const radarData = ref();

// 环形饼图
const pieLegends = ref<Array<PieListItem>>();
const list = ref<Array<SeriesItem>>([]);

// 子组件引用
const bettingTabRef = ref();
const distributionTabRef = ref();

// 事件处理
const close = () => {
  emits("close");
};

/**
 * 玩家游戏分布
 */
const getGameDistribution = async () => {
  console.log("getGameDistribution", props.userInfo.user_id);
  try {
    const params = {
      activity_id: props.activityId,
      player_id: props.userInfo.user_id,
      is_history: props.isHistory,
      data_type: gameTypeStr[activeFilter.value],
    };
    const data = await playerGameDistribution(params);
    /*  const data = {
      pie_chart_data: [
        { game_name: "游戏A", percent: "40.0" }, //游戏名称 占比
        { game_name: "游戏B", percent: "30.0" },
        { game_name: "Others", percent: "30.0" },
      ],
      list_data: [
        { game_name: "游戏A", value: "4000", game_id: 1 }, //游戏名称，值，游戏id
        { game_name: "游戏B", value: "3000", game_id: 2 },
        { game_name: "Others", value: "3000" },
      ],
    }; */
    if (isEmpty(data.pie_chart_data)) {
      list.value = [];
      pieLegends.value = [];
      return;
    }
    list.value = data.pie_chart_data
      .filter((item) => item.percent > 0)
      .map((item) => {
        return { name: item.game_name, value: item.percent };
      });
    pieLegends.value = data.list_data.map((item) => {
      item.value =
        gameTypeStr[activeFilter.value] === "WinRate"
          ? Math.floor(+item.value) + "%"
          : "₱" + amountFormatThousands(item.value);
      return item;
    });
    console.log("pieLegends", pieLegends.value);
  } catch (error) {
    console.log("playerGameDistribution error", error);
  }
};
/**
 * 生成雷达图数据
 * @param {Object} data 后端获取的值
 * @param {Array} keys 刻度线配置项
 */
function getRadarList(
  data: Record<string, number>,
  keys: Array<RadarItem>
): Array<RadarSeriesItem> {
  let value: Array<number> = [];
  keys.forEach((item) => {
    const key = item.name.replace(" ", "_").toLowerCase();
    console.log("key:", key, data[key], value);
    value.push(data[key]);
  });

  return [{ name: "", value }];
}

/**
 * 玩家投注汇总
 */
const getBettingSummary = async () => {
  try {
    const params = {
      activity_id: props.activityId,
      player_id: props.userInfo.user_id,
      is_history: props.isHistory,
    };
    const data = await playerBettingSummary(params);
    /*  const data = {
      radar_data: {
        // 雷达数据
        bet_amount: 8.5, //投注额分数
        bet_times: 7.0, //投注次数分数
        net_win: 9.0, //净赢分数
        win_rate: 6.5, //胜率分数
        game_numbers: 8.0, //游戏数量分数
      },
      list_data: {
        //列表数据
        bet_amount: 10000, //下注金额
        bet_times: 50, //投注次数
        net_win: 2000, //净赢
        win_rate: "60.0%", //胜率
        game_numbers: 5, //游戏数量
      },
    }; */
    radarList.value = isEmpty(data.radar_data)
      ? []
      : getRadarList(data.radar_data, indicatorList.value);
    // console.log("playerBettingSummary", radarList.value);
    if (isEmpty(data.list_data)) return;
    radarData.value = JSON.parse(JSON.stringify(data.list_data));
    // 下注金额
    radarData.value.bet_amount = "₱" + amountFormatThousands(radarData.value.bet_amount);
    radarData.value.net_win = "₱" + amountFormatThousands(radarData.value.net_win);
  } catch (error) {}
};

// 监听activeFilter变化，重新获取数据
watch(activeFilter, () => {
  getGameDistribution();
});

onMounted(() => {
  console.log("onMounted userInfo", props);
  getBettingSummary();
  getGameDistribution();
});
</script>

<style scoped lang="scss">
// 不生效的类
// .graphic-popup-overlay {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background: rgba(0, 0, 0, 0.5);
//   backdrop-filter: blur(4px);
//   z-index: 6;
// }

.modal-content {
  width: 92%;
  max-height: 70vh;
  margin: 14vh auto 0;
  // overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  background: linear-gradient(179.88deg, #e5efff 10%, #ffffff 32.7%);
}

.modal-body {
  padding: 0 0 16px;
  border: 0;
  // margin-top: -2px;
  background: linear-gradient(179.88deg, #e5efff 0%, #ffffff 32.7%);
  border-radius: 0 0 20px 20px;
}

.close-btn {
  position: absolute;
  bottom: -50px;
  right: 50%;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 32px;
  height: 32px;
  background: linear-gradient(240deg, rgb(25, 74, 252, 0.3) 0%, rgb(119, 0, 255, 0.3) 100%);
  border: none;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(-50%) rotate(45deg) scale(1.1);
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.4);
  }
}
</style>
