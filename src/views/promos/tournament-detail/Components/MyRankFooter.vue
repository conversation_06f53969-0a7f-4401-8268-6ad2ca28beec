<template>
  <div class="my-rank-footer">
    <!-- My Rank Info -->
    <van-row class="me-info">
      <van-col span="3" class="rank">{{ myRank.rank }}</van-col>
      <van-col span="6" class="user-id">
        <img :src="getUserAvatar(myRank?.avatar)" class="mini-avatar" alt="User Avatar" />
        {{ maskString(myRank.user_id, 2, 3, 1) }}
      </van-col>
      <van-col span="7" class="bet">
        <IconCoin :size="16" />
        {{ formatNumberToThousands(myRank.score, false) }}
      </van-col>
      <van-col span="6" class="bonus">
        <IconCoin :size="16" />
        {{ formatNumberToThousands(myRank.bonus, false) }}
      </van-col>
      <van-col span="2">
        <img
          @click="() => handleRankClick(myRank)"
          class="arrow-right"
          src="@/assets/images/tournament/arrow-right.png"
          alt=""
        />
      </van-col>
    </van-row>

    <!-- Bet Now Button -->
    <div class="bet-now-button">
      <img @click="handleBetNow" src="@/assets/images/tournament/bet-now.png" alt="Bet Now" />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: "MyRankFooter" });

import { maskString, getUserAvatar, formatNumberToThousands } from "@/utils/core/tools";
import type { RankItem } from "../../configs";

interface Props {
  myRank: RankItem;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  rankClick: [item: RankItem];
  betNow: [];
}>();

// 事件处理
const handleRankClick = (item: RankItem) => {
  emit("rankClick", item);
};

const handleBetNow = () => {
  emit("betNow");
};
</script>

<style lang="scss" scoped>
.my-rank-footer {
  height: 112px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #cdefff;

  .rank {
    color: #1e49fc;
    font-weight: 700;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .me-info {
    text-align: center;
    padding: 12px;
    color: #222222;
    font-weight: 600;
    background: linear-gradient(180deg, #f5fcff 0%, #cdefff 98.61%);

    > div {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .bet,
    .bonus {
      font-weight: 700;
      font-size: 15px;
      line-height: 100%;
      letter-spacing: 0px;
    }

    .bonus {
      color: #ff9800;
    }
  }

  .user-id {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 15px;
    line-height: 100%;
    letter-spacing: 0%;

    .mini-avatar {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .bet,
  .user-id,
  .bonus {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }

  .arrow-right {
    width: 14px;
    cursor: pointer;
  }

  .bet-now-button {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 0;

    img {
      width: 211px;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }
}
</style>
