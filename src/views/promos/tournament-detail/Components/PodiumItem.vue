<template>
  <div :class="`podium-item ${rankPosition}`" @click="() => handleClick(rankData)">
    <div class="avatar">
      <img :src="getUserAvatar(rankData?.avatar)" alt="User Avatar" />
      <img class="rank-mark" :src="rankMarkImage" alt="" />
      <img
        class="rank-mark-isMe"
        v-if="isCurrentUser"
        src="@/assets/images/tournament/isMe.png"
        alt=""
      />
    </div>
    <div class="user-id">{{ displayUserId }}</div>
    <div class="prize">
      <IconCoin :size="coinSize.prize" />
      <span :data-text="displayBonus">{{ displayBonus }}</span>
    </div>
    <div class="bet">
      <IconCoin :size="coinSize.bet" />
      <span :data-text="displayScore">{{ displayScore }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: "PodiumItem" });

import { computed } from "vue";
import { maskString, getUserAvatar } from "@/utils/core/tools";
import type { RankItem } from "../../configs";
import first from "@/assets/images/tournament/top1.png";
import second from "@/assets/images/tournament/top2.png";
import third from "@/assets/images/tournament/top3.png";

interface Props {
  rankData?: RankItem;
  rankPosition: "first" | "second" | "third";
  coinSize: { prize: number; bet: number };
  isCurrentUser: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  rankData: undefined,
  isCurrentUser: false,
});

const emit = defineEmits<{
  click: [item?: RankItem];
}>();

// 计算属性
const rankMarkImage = computed(() => {
  const rankImages = {
    first,
    second,
    third,
  };
  return rankImages[props.rankPosition];
});

const displayUserId = computed(() => maskString(props.rankData?.user_id || "", 2, 3) || "--");

const displayBonus = computed(() => props.rankData?.bonus || "--");

const displayScore = computed(() => props.rankData?.score || "--");

// 事件处理
const handleClick = (item?: RankItem) => {
  emit("click", item);
};
</script>

<style lang="scss" scoped>
.podium-item {
  text-align: center;
  position: relative;
  flex: 1;
  color: #333;
  max-height: 198px;
  padding-top: 52px;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: 0px;

  .rank-mark {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 20%;
    bottom: -45%;
    z-index: 1;
  }

  .rank-mark-isMe {
    width: 32px;
    height: 16px;
    position: absolute;
    top: 0;
    left: 10%;
  }

  &.first {
    order: 2;
    padding-top: 70px;
    background: url("@/assets/images/tournament/rank-first.png") no-repeat;
    background-size: 100% 100%;

    .rank-mark-isMe {
      left: 20%;
    }

    .prize,
    .bet {
      font-size: 16px;
      color: #f18500;
    }

    .bet {
      font-size: 22px;
    }

    .avatar {
      width: 59px;
      height: 59px;
      top: -1px;
      left: 25%;
    }

    .rank-mark {
      width: 35px;
      height: auto;
      left: 18%;
      bottom: -50%;
    }
  }

  &.second {
    background: url("@/assets/images/tournament/rank-second.png") no-repeat;
    background-size: 100% 100%;
    order: 1;

    .prize,
    .bet {
      font-size: 18px;
      line-height: 100%;
      letter-spacing: 0px;
      color: #007eed;
    }

    .bet {
      font-size: 14px;
    }
  }

  &.third {
    background: url("@/assets/images/tournament/rank-third.png") no-repeat;
    background-size: 100% 100%;
    order: 3;

    .prize,
    .bet {
      font-size: 18px;
      color: #ae45ff;
    }

    .bet {
      font-size: 14px;
    }
  }

  .avatar {
    top: 8px;
    left: 30%;
    position: absolute;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin: 0 auto 28px;
    margin-top: -20%;
    border: 2px solid #0d17d4;
    padding: 1px;

    img:first-child {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .user-id {
    margin-bottom: 14px;
    font-family: "D-DIN";
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
    color: #222222;
  }

  .prize,
  .bet {
    font-family: "D-DIN";
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-bottom: 8px;

    span {
      position: relative;
      z-index: 1;
    }

    // 使用伪元素描边
    span::before {
      content: attr(data-text);
      position: absolute;
      -webkit-text-stroke: 2px #fff;
      z-index: -1;
    }
  }

  .bet {
    margin-bottom: 18px;
  }

  .prize {
    color: #ffd700;
  }
}
</style>
