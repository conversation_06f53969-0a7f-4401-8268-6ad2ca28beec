<template>
  <div class="tab-content distribution-tab">
    <div class="radar-chart-container">
      <div ref="chartRef" class="chart-canvas"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, watchEffect, nextTick } from "vue";
import * as echarts from "echarts";
import type { RadarItem, RadarSeriesItem, RadarData } from "../../configs";

// 对应文本颜色
const colors = ["#3E70FF", "#6FD195", "#FFAE4C", "#FF4A3D", "#9A2FFF"];
const nameGaps = [10, 15, 30, 30, 15];
// 雷达图各文本颜色样式配置
const richObj = colors
  .map((t) => ({ color: t, fontWeight: 700, fontSize: 14 }))
  .reduce((acc, current, index) => {
    acc["style" + index] = current;
    return acc;
  }, {});

const props = withDefaults(
  defineProps<{
    indicatorList: Array<RadarItem>;
    seriesList: Array<RadarSeriesItem> | [];
    radarData: Array<RadarData> | [];
  }>(),
  {
    seriesList: () => [{ name: "", value: [] }],
    radarData: () => [],
  }
);
const chartRef = ref<HTMLDivElement | null>(null);
const chartInstance = ref<echarts.ECharts | null>(null);

// 初始化图表
const initChart = () => {
  if (chartRef.value && !chartInstance.value) {
    chartInstance.value = echarts.init(chartRef.value);
    const option = setOptions(props.indicatorList, props.seriesList);
    chartInstance.value.setOption(option);
  }
};

function setOptions(
  indicatorList: Array<radarItem>,
  seriesList: Array<radarSeriesItem>
): echarts.EChartsOption {
  return {
    title: { show: false },
    // 图例
    legend: { show: false },
    // 调整图表位置和大小，确保文本完整显示
    grid: {
      left: "15%",
      right: "15%",
      top: "15%",
      bottom: "15%",
      containLabel: true,
    },
    // tooltip: {
    //   trigger: 'item'
    // },
    series: {
      type: "radar",
      lineStyle: {
        color: "#5470C6", // 蓝色边框线
        width: 2,
      },
      symbol: "none", // 不显示数据点
      areaStyle: {
        color: {
          type: "radial",
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            {
              offset: 0,
              color: "rgba(84, 112, 198, 0.4)", // 中心更透明
            },
            {
              offset: 1,
              color: "rgba(84, 112, 198, 0.1)", // 边缘更透明
            },
          ],
        },
      },
      data: seriesList,
    },
    // 雷达图的标准
    radar: {
      indicator: indicatorList.map((item, index) => {
        const key = item.name.replace(" ", "_").toLowerCase();
        return {
          name: `${item.name}\n${props.radarData[key] || ""}`,
          max: item.max,
          nameGap: nameGaps[index],
        };
      }),
      center: ["50%", "50%"], // 居中显示
      radius: "50%", // 调整半径以留出更多空间给文本
      startAngle: 90, // 从顶部开始
      // 名称样式
      axisName: {
        fontSize: 14, // 增大字体以确保可读性
        fontWeight: 500,
        color: "#666", // 统一使用深色文本
        // 使用回调函数，第一个参数是指示器名称，第二个参数是指示器配置项
        formatter: function (name: string, indicator: any) {
          console.log("formatter", name, indicator);
          const lines = name.split("\n");
          const title = lines[0];
          const value = lines[1] || "";
          const colorIndex = indicatorList.findIndex((item: any) => item.name === title);
          return `{style${colorIndex}|${title}}\n{value|${value}}`;
        },
        // 分别配置各方位文本样式
        rich: {
          ...richObj,
          value: {
            textAlign: "center",
            fontSize: 14,
            fontWeight: 500,
            color: "#666666CC",
            lineHeight: 32,
          },
        },
      },
      // 刻度线
      axisLine: {
        lineStyle: {
          color: "rgba(25, 102, 254, 0.25)",
        },
      },
      // 分割线
      splitLine: {
        lineStyle: {
          color: "rgba(25, 102, 254, 0.25)",
        },
      },
      // 分割区域
      splitArea: {
        show: false,
      },
      // 刻度标签
      axisLabel: {
        show: false,
      },
    },
  };
}
onMounted(() => {
  initChart();
  console.log("onMounted initChart");
});

watchEffect(() => {
  nextTick(() => {
    if (chartInstance.value)
      chartInstance.value.setOption(setOptions(props.indicatorList, props.seriesList));
  });
});
</script>

<style scoped lang="scss">
.distribution-tab {
  display: flex;
  flex-direction: column;
}

.radar-chart-container {
  position: relative;
  width: 100%;
  height: 380px;
  // padding: 20px; // 增加内边距以确保文本完整显示
}

.chart-canvas {
  width: 100%;
  height: 100%;
}
</style>
