/**
 * 排行榜数据管理 Composable
 * 统一处理排行榜数据的获取、状态管理和错误处理
 */

import { ref, computed } from "vue";
import { useGlobalStore } from "@/stores/global";
import { maskString } from "@/utils/core/tools";
import { createApiState, executeBatchApi, executeApi, type ApiState } from "../utils/promoUtils";

export interface RankingItem {
  player_id: string;
  award: string | number;
  total_bet_amount: string | number;
  rank: number;
  avatar?: string | number;
}

export interface RankingData {
  list: {
    rank: RankingItem[];
    bet_amount?: string | number;
    user?: {
      rank?: number;
      award?: string | number;
    };
  };
  user?: {
    rank?: number;
    award?: string | number;
  };
}

export interface RankingState {
  [key: string]: RankingData;
}

/**
 * 排行榜数据管理 Hook
 * @param apiFunction API 调用函数
 * @param options 配置选项
 * @returns 排行榜相关的状态和方法
 */
export function useRankingData(
  apiFunction: (params: any) => Promise<any>,
  options: {
    autoRefresh?: boolean;
    refreshInterval?: number;
    cacheTime?: number;
  } = {}
) {
  const { autoRefresh = false, refreshInterval = 30000, cacheTime = 5 * 60 * 1000 } = options;
  const globalStore = useGlobalStore();

  // 使用 API 状态管理
  const apiState: ApiState<RankingState> = createApiState({
    1: { list: { rank: [] }, user: {} },
    2: { list: { rank: [] }, user: {} },
  });

  const activeTab = ref("1");
  let refreshTimer: NodeJS.Timeout | null = null;

  // 计算属性
  const currentRankingList = computed(() => {
    return apiState.data.value?.[activeTab.value]?.list?.rank || [];
  });

  const currentUserRank = computed(() => {
    return (
      apiState.data.value?.[activeTab.value]?.list?.user?.rank ||
      apiState.data.value?.[activeTab.value]?.user?.rank ||
      "--"
    );
  });

  const currentUserBetAmount = computed(() => {
    return apiState.data.value?.[activeTab.value]?.list?.bet_amount || "--";
  });

  const currentUserAward = computed(() => {
    return (
      apiState.data.value?.[activeTab.value]?.list?.user?.award ||
      apiState.data.value?.[activeTab.value]?.user?.award ||
      "--"
    );
  });

  // 获取排行榜数据
  const fetchRankingData = async (type: string = "1") => {
    return await executeApi(apiFunction, { type }, apiState, {
      transform: (response) => {
        const currentData = apiState.data.value || {
          1: { list: { rank: [] }, user: {} },
          2: { list: { rank: [] }, user: {} },
        };
        return {
          ...currentData,
          [type]: response,
        };
      },
    });
  };

  // 初始化数据（获取今日和昨日数据）
  const initializeData = async () => {
    const apiCalls = [
      { apiFunction, params: { type: "1" }, key: "today" },
      { apiFunction, params: { type: "2" }, key: "yesterday" },
    ];

    return await executeBatchApi(apiCalls, apiState, {
      transform: (results) => ({
        1: results[0],
        2: results[1],
      }),
    });
  };

  // 检查用户是否在当前排行榜中
  const isCurrentUser = (playerId: string): boolean => {
    return globalStore.userInfo.user_id === playerId;
  };

  // 获取脱敏的用户ID
  const getMaskedPlayerId = (playerId: string): string => {
    return maskString(playerId, 2, 3);
  };

  // 获取前三名数据
  const getTopThreeData = computed(() => {
    const currentList = currentRankingList.value;
    return [1, 0, 2].map((index) => currentList[index] || null);
  });

  // 切换标签页
  const switchTab = (tab: string) => {
    activeTab.value = tab;
  };

  // 自动刷新功能
  const startAutoRefresh = () => {
    if (autoRefresh && !refreshTimer) {
      refreshTimer = setInterval(() => {
        initializeData();
      }, refreshInterval);
    }
  };

  const stopAutoRefresh = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  };

  return {
    // 状态
    loading: apiState.loading,
    error: apiState.error,
    activeTab,
    rankingData: apiState.data,

    // 计算属性
    currentRankingList,
    currentUserRank,
    currentUserBetAmount,
    currentUserAward,
    getTopThreeData,

    // 方法
    fetchRankingData,
    initializeData,
    isCurrentUser,
    getMaskedPlayerId,
    switchTab,
    startAutoRefresh,
    stopAutoRefresh,
  };
}
