<template>
  <XPage :navBarStyle="{ backgroundColor: '#a95c1b', color: '#fff' }">
    <div class="promo9-tip-bg">
      <div class="promo9-tip-content">
        <div class="mechanics-header">
          <h1 class="main-title">GENERAL MECHANICS</h1>
        </div>

        <div class="rule-item">
          <span class="rule-number">1.</span>
          <p class="rule-text">
            NUSTAR members must meet the specific deposit and bet requirements associated with the
            designated bonus level to receive the bonus.
          </p>
        </div>
        <div class="table-container">
          <van-row class="table-head">
            <van-col span="8">Deposit (₱)</van-col>
            <van-col span="8">Bet (₱)</van-col>
            <van-col span="8">Bonus (₱)</van-col>
          </van-row>
          <van-row v-for="row in betList" :key="row.bet">
            <van-col span="8">{{ row.deposit }}</van-col>
            <van-col span="8">{{ row.bet }}</van-col>
            <van-col span="8">{{ row.bonus }}</van-col>
          </van-row>
        </div>

        <div class="rule-item">
          <span class="rule-number">2.</span>
          <p class="rule-text">
            The total Deposit and bet amount is calculated weekly, from Monday 00:00:00 to Sunday
            23:59:59.
          </p>
        </div>

        <div class="rule-item">
          <span class="rule-number">3.</span>
          <p class="rule-text">
            Bonuses will be automatically credited to the accounts of eligible NUSTAR members every
            Monday at 2:00 PM.
          </p>
        </div>
      </div>
      <!-- <img src="@/assets/images/promos/promo9_tip.jpg" alt="" /> -->
      <ZFootPng />
    </div>
  </XPage>
</template>

<script setup>
const betList = [
  { deposit: "1,000", bet: "1,000", bonus: "5" },
  { deposit: "5,000", bet: "10,000", bonus: "10" },
  { deposit: "10,000", bet: "50,000", bonus: "50" },
  { deposit: "50,000", bet: "250,000", bonus: "300" },
  { deposit: "100,000", bet: "500,000", bonus: "1,000" },
  { deposit: "500,000", bet: "2,500,000", bonus: "5,000" },
  { deposit: "1,000,000", bet: "5,000,000", bonus: "10,000" },
  { deposit: "2,000,000", bet: "20,000,000", bonus: "50,000" },
];
</script>

<style lang="scss" scoped>
.promo9-tip-bg {
  min-height: 100vh;
  background: #994500;
  color: #fff;
  padding-bottom: 84px;
  img {
    width: 100%;
    height: auto;
  }
}

.promo9-tip-content {
  font-family: "Inter", "D-DIN";
  font-size: 12px;
  padding: 24px 20px;
  font-weight: 400;

  .mechanics-header {
    margin-bottom: 24px;

    .main-title {
      font-size: 18px;
      font-weight: bold;
      color: #ffd700;
      text-align: left;
      margin: 20px 0;
    }
  }

  .rule-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    color: #fff;
    font-size: 14px;

    .rule-number {
      margin-right: 12px;
      margin-top: 2px;
    }

    .rule-text {
      line-height: 1.5;
      margin: 0;
      flex: 1;
    }
  }

  .table-container {
    margin: 10px 0;
    font-size: 12px;
    text-align: center;
    line-height: 28px;
    background: #502105;
    border: none;
    color: #fff;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #fff;
    &:deep(.van-row) {
      border-bottom: 1px solid #fff;
      &:last-child {
        border: none;
      }
    }
    .table-head {
      color: #d6b266;
      font-weight: 400;
      line-height: 1.1;
      font-size: 14px;

      &:deep(.van-col) {
        padding: 8px 0px;
      }
    }

    &:deep(.van-col) {
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-child,
      &:nth-child(2) {
        border-right: 1px solid #fff;
      }
    }
  }
}
</style>
