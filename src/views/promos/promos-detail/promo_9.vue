<template>
  <ZPage :request="initData" backgroundColor="#a95c1b" :narBarStyle="{ backgroundColor: '#a95c1b', color: '#fff' }">
    <template #right>
      <van-icon size="24" @click="() => router.push('/promos/promo9_tip')" name="question-o" />
    </template>
    <div class="payday">
      <!-- Tabs -->
      <div class="payday-tabs">
        <div name="ended" @click="activeTab = 'ended'" :class="[`payday-tab ended`, { active: activeTab === 'ended' }]">
          <div class="payday-tab-date">{{ lastWeek }}</div>
          <div class="payday-tab-status">ENDED</div>
        </div>
        <div name="ongoing" @click="activeTab = 'ongoing'"
          :class="[`payday-tab ongoing`, { active: activeTab === 'ongoing' }]">
          <div class="payday-tab-date">{{ thisWeek }}</div>
          <div class="payday-tab-status">ONGOING</div>
        </div>
      </div>
      <!-- Table -->
      <div class="payday-table">
        <div class="payday-table-header">
          <span>LEVEL</span>
          <span>DEPOSIT</span>
          <span>BET</span>
          <span>BONUS</span>
        </div>
        <div class="payday-table-content">
          <ZNoData v-if="!weekConfigInfo.weekly_config.length > 0"></ZNoData>
          <div v-else class="payday-table-row" v-for="(item, idx) in weekConfigInfo.weekly_config" :key="idx">
            <span>{{ idx + 1 }}</span>
            <span class="payday-cell-dark">
              <div class="bg-line" v-if="idx < weekConfigInfo.weekly_config.length - 1"></div>
              ₱{{ item.deposit.toLocaleString() }}
            </span>
            <span class="payday-cell-dark">
              <div class="bg-line" v-if="idx < weekConfigInfo.weekly_config.length - 1"></div>
              ₱{{ item.bet.toLocaleString() }}
            </span>
            <span class="payday-cell-bonus">₱{{ item.bonus.toLocaleString() }}</span>
          </div>
        </div>

        <div class="payday-tip">
          Data updates every 10 minutes. Bonuses will be distributed on Monday at 2pm.
        </div>
      </div>

      <!-- My Performance -->
      <div class="payday-performance">
        <div class="payday-performance-title">MY PERFORMANCE</div>
        <div class="payday-performance-table">
          <div>
            <div class="payday-performance-label">DEPOSIT</div>
            <div class="payday-performance-value">₱{{ weekInfos[activeTab].deposit }}</div>
          </div>
          <div>
            <div class="payday-performance-label">BET</div>
            <div class="payday-performance-value">₱{{ weekInfos[activeTab].bet }}</div>
          </div>
          <div>
            <div class="payday-performance-label">BONUS</div>
            <div class="payday-performance-value">₱{{ weekInfos[activeTab].bonus }}</div>
          </div>
        </div>
        <div class="payday-performance-btns">
          <button class="payday-btn deposit" @click="depositStore.openDialog">DEPOSIT</button>
          <button class="payday-btn bet" @click="() => router.push('/home')">BET</button>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<script setup>
import { useDepositStore } from "@/stores/deposit";
import { useRouter } from "vue-router";
import { weeklyInfo, weeklyConfig } from "@/api/promos";
import { getWeekPeriods } from "@/utils/core/tools";
const router = useRouter();
const activeTab = ref("ongoing");
const depositStore = useDepositStore();

const WEEKINFO_INIT_DATA = {
  ongoing: { bet: "0.00", bonus: "0.00", deposit: "0.00" },
  ended: { bet: "0.00", bonus: "0.00", deposit: "0.00" },
};

const weekInfos = ref(WEEKINFO_INIT_DATA);
const weekConfigInfo = ref({
  weekly_config: [],
});

const initData = async () => {
  const res = await Promise.all([weeklyInfo({ turn: 1 }), weeklyInfo({ turn: 2 }), weeklyConfig()]);
  if (res[0].data)
    weekInfos.value = {
      ongoing: res[0].data || WEEKINFO_INIT_DATA.ongoing,
      ended: res[1].data || WEEKINFO_INIT_DATA.ended,
    };
  weekConfigInfo.value = res[2].data || { weekly_config: [] };
};
const { lastWeek, thisWeek } = getWeekPeriods();
</script>

<style lang="scss" scoped>
.payday {
  background: linear-gradient(180deg, #a95c1b 0%, #a24e00 80%);
  color: #fff;
  padding: 20px 0 120px;
}

.payday-tabs {
  font-size: 14px;
  position: relative;
  height: 66px;

  .payday-tab {
    height: 100%;
    display: inline-block;
    padding: 12px 0 0;
    text-align: center;
    border-radius: 20px 20px 0 0;
    color: #fff;
    background: linear-gradient(180deg, #7a3b00, #783b04);
    min-width: 50%;
    width: 50%;
    position: absolute;
    right: 0;
    top: 0;

    &.ended {
      left: 0;
    }

    .payday-tab-date {
      font-weight: 500;
    }

    .payday-tab-status {
      font-size: 12px;
      margin-top: 2px;
      letter-spacing: 1px;
    }
  }

  .payday-tab.active {
    width: 60%;
    padding-right: 40px;
    background-image: url("@/assets/images/promos/promo9_tab.png");
    background-size: 339px 221px;
    background-position: top -1px left -24px;
    /* 新增：左右翻转背景图 */
    transform: scaleX(-1);
    /* 让内容不翻转 */
    direction: ltr;
    color: #fff;
    font-weight: bold;
    z-index: 2;

    .payday-tab-status,
    .payday-tab-date {
      transform: scaleX(-1);
      color: #fff;
    }

    &.ended {
      left: 0;
      top: 0;
      padding-right: 40px;
      transform: scaleX(1);

      .payday-tab-status,
      .payday-tab-date {
        transform: scaleX(1);
      }
    }
  }
}

.payday-table {
  padding: 0 0 34px 0;
  overflow: hidden;
  background: #653c1a;
  border-radius: 0 0 20px 20px;

  .payday-table-header {
    display: flex;
    justify-content: space-between;
    background: none;
    color: #ffd388;
    font-weight: bold;
    font-size: 14px;
    padding: 12px 14px 6px;

    span {
      flex: 1;
      text-align: center;
    }
  }

  .payday-table-content {
    height: calc(100vh - 420px);
    overflow-y: auto;
  }

  .payday-table-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #4d2600;
    border-radius: 18px;
    margin: 4px 12px;
    font-size: 14px;
    padding: 4px 0;
    font-weight: 500;
    color: #fff;

    span {
      flex: 1;
      display: inline-block;
      text-align: center;
    }

    .payday-cell-dark {
      background: #2f1a06;
      border-radius: 18px;
      margin: 0 8px;
      font-weight: bold;
      color: #fff;
      padding: 6px 0;
      display: inline-block;
      min-width: 90px;
      position: relative;
      z-index: 2;

      .bg-line {
        position: absolute;
        width: 6px;
        height: 45px;
        background: #2f1a06;
        left: 50%;
        top: 0;
        z-index: -1;
      }
    }

    .payday-cell-bonus {
      color: #a79d8b;
      font-weight: bold;
    }
  }
}

.payday-tip {
  color: #fff;
  width: 80%;
  background: #653c1a;
  margin: 10px auto;
  font-size: 12px;
  text-align: center;
}

.payday-performance {
  background: linear-gradient(180deg, #b25501 0%, #773800 100%);
  border-radius: 18px 18px 0 0;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0 0 18px 0;
  z-index: 10;
  box-shadow: 0 -2px 12px #00000022;
}

.payday-performance-title {
  background-image: url("../../../assets/images/promos/promo9_btn.svg");
  background-size: cover;
  background-repeat: no-repeat;
  // background: linear-gradient(180deg, #da2801 10%, #fa8614 100%);
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  border-radius: 0 0 18px 18px;
  padding: 10px 0;
  text-align: center;
  margin: 0 auto 8px;
  width: 60%;
  // box-shadow: 0 2px 8px #ff7f0130;
  letter-spacing: 1px;
  position: relative;
  top: -1px;
}

.payday-performance-table {
  display: flex;
  font-size: 14px;
  background: linear-gradient(90deg, rgba(58, 28, 3, 0) 10%, #3a1c03 50%, rgba(58, 28, 3, 0) 90%);
  justify-content: space-around;
  align-items: center;
  margin: 0 20px 20px;
  padding: 10px 0;

  >div {
    text-align: center;
    flex: 1;
  }
}

.payday-performance-label {
  color: #d3b296;
  margin-bottom: 2px;
}

.payday-performance-value {
  color: #fff;
  font-weight: bold;
}

.payday-performance-btns {
  display: flex;
  justify-content: space-around;
  margin: 8px 30px 0;
  gap: 16px;
}

.payday-btn {
  flex: 1;
  font-size: 14px;
  font-weight: bold;
  border: none;
  border-radius: 30px;
  padding: 10px 0;
  color: #000;
  margin: 0 8px;
}

.payday-btn.deposit {
  background: #ffbf2a;
}

.payday-btn.bet {
  background: #ff7602;
}
</style>
