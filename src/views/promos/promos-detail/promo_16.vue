<template>
  <XPage :navBarStyle="{ backgroundColor: '#FFAB13', color: '#333' }">
    <template #left-icon>
      <ZIcon type="icon-fanhui" color="#fff" />
    </template>

    <div class="promo-container">
      <div class="promo16-content">
        <h1 class="title">GENERAL MECHANICS</h1>
        <ol class="rule-list">
          <li class="rule-item">This promotion is open to both new and existing NUSTAR members!</li>
          <li class="rule-item">
            Meet the betting requirements for each game Super Bang Bang, Sugar Bang Bang 2, and
            Treasure raiders to unlock up to 216 free spins — and win up to ₱1,000,000 in bonuses!
            <div class="game-buttons">
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo16-1.png"
                  alt="Sugar Bang Bang"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo16-2.png"
                  alt="Sugar Bang Bang 2"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo16-3.png"
                  alt="Treasure Raiders"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
            </div>
            <p class="spin-info">
              The value of each free spin varies by game: ₱1.20 per spin for Sugar Bang Bang and
              Sugar Bang Bang 2, and ₱0.50 per spin for Treasure Raiders.
            </p>
          </li>
        </ol>
        <h2 class="sub-title">Sugar Bang Bang & Sugar Bang Bang 2</h2>
        <div class="table-container">
          <van-row class="table-head">
            <van-col span="12">BET AMOUNT</van-col>
            <van-col span="12">FREE SPIN</van-col>
          </van-row>
          <van-row
            v-for="(row, index) in list"
            :key="row.bet"
            :style="{ background: (index + 1) % 2 > 0 ? '#FFD58B' : '#FEC279' }"
          >
            <van-col span="12">{{ row.bet }}</van-col>
            <van-col span="12">{{ row.spin }}</van-col>
          </van-row>
        </div>
        <h2 class="sub-title">Treasure raiders</h2>
        <div class="table-container">
          <van-row class="table-head">
            <van-col span="12">BET AMOUNT</van-col>
            <van-col span="12">FREE SPIN</van-col>
          </van-row>
          <van-row
            v-for="(row, index) in list1"
            :key="row.bet"
            :style="{ background: (index + 1) % 2 > 0 ? '#FFD58B' : '#FEC279' }"
          >
            <van-col span="12">{{ row.bet }}</van-col>
            <van-col span="12">{{ row.spin }}</van-col>
          </van-row>
        </div>
        <ol class="rule-list" start="3">
          <li class="rule-item">
            Bet amounts for each game are calculated separately. Reach the required bet amount in
            any game to instantly claim your free spins!
          </li>
          <li class="rule-item">
            To ensure a fair gaming experience, you need to exit and re-enter the game after
            reaching the required betting amount for the system to automatically issue your Free
            Spins.
          </li>
          <li class="rule-item">
            This promo follows a natural weekly cycle, running from Monday at 00:00 to Sunday at
            23:00. All accumulated bets and spins will be reset every Monday.
          </li>
          <li class="rule-item">No wagering requirements are needed to withdraw your winnings.</li>
        </ol>
      </div>
      <ZFootPng background="linear-gradient(90deg,#fece9d,#ff8001)" />
    </div>
  </XPage>
</template>

<script setup>
const list = [
  { bet: "2,000", spin: "3" },
  { bet: "5,000", spin: "5" },
  { bet: "8,000", spin: "8" },
  { bet: "15,000", spin: "12" },
  { bet: "35,000", spin: "20" },
];
const list1 = [
  { bet: "2,000", spin: "7" },
  { bet: "5,000", spin: "12" },
  { bet: "8,000", spin: "18" },
  { bet: "15,000", spin: "28" },
  { bet: "35,000", spin: "55" },
];
</script>

<style lang="scss" scoped>
.icon-fanhui {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgb(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.promo16-content {
  padding: 50px 20px 0;
}
.promo-container {
  background: linear-gradient(180deg, #ffa90d, #ffa925, #ffac2a);
  margin: 0 auto;
  color: #333;
  font-family: "Inter";
}
.title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}
.rule-list {
  list-style: decimal;
  margin-left: 20px;
  margin-bottom: 15px;
  counter-reset: my-counter;
  li {
    counter-increment: my-counter;
  }
}
.rule-item {
  margin-bottom: 15px;
  line-height: 1.6;
}
.game-buttons {
  display: flex;
  gap: 20px;
  margin: 10px;
}
.game-item {
  text-align: center;
}
.game-img {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}
.bet-now {
  width: 100%;
  font-size: 10px;
  padding: 4px 8px;
  background: linear-gradient(180deg, #ff5e00 0%, #c80900 80%);
  box-shadow: -1px 1px 6px 1px rgba(0, 0, 0, 0.2);
  color: #fff;
  border-radius: 4px;
}
.spin-info {
  margin: 36px 0 20px;
}
.sub-title {
  width: 100%;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 5px;
}

.table-container {
  margin: 10px 14px 30px;
  font-size: 16px;
  font-weight: 800;
  text-align: center;
  line-height: 34px;
  background: #ffd58b;
  border: none;
  color: #333;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 1px 1px 8px 1px rgba(0, 0, 0, 0.1), -1px -1px 8px 1px rgba(0, 0, 0, 0.1);
  font-family: "D-DIN";
  .table-head {
    font-family: "Inter";
    color: #fff;
    font-size: 14px;
    background: #ec801e;
    font-weight: 500;
    line-height: 1.6;
    &:deep(.van-col) {
      padding: 8px 0px;
    }
  }

  &:deep(.van-col) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 20px;
}
.license-img,
.game-resp-img {
  height: 30px;
  width: auto;
}
.social-links {
  display: flex;
  gap: 10px;
}
.social-icon {
  color: #000;
  text-decoration: none;
}
.site-link,
.email-link,
.nustar {
  color: #000;
  text-decoration: none;
  margin: 0 5px;
}
</style>
