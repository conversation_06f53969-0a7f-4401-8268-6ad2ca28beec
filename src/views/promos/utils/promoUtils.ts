/**
 * 促销活动专用工具函数
 * 包含格式化、API 辅助、锦标赛相关的所有工具函数
 */

import { ref, type Ref } from "vue";
import {
  formatTime,
  parseTime,
  formatNumberToThousands,
  amountFormatThousands,
  isNumeric,
} from "@/utils/core/tools";
import type {
  Tournament,
  ProcessedTournament,
  TournamentStatusCode,
  TournamentStatusType,
} from "../configs";
import { TournamentStatus, ENTRY_MODES, ENTRY_RULES, PERIOD_TYPES } from "../configs";

// ==================== API 辅助工具 ====================

export interface ApiState<T = any> {
  data: Ref<T | null>;
  loading: Ref<boolean>;
  error: Ref<string | null>;
}

/**
 * 创建 API 状态管理
 * @param initialData 初始数据
 * @returns API 状态对象
 */
export function createApiState<T>(initialData: T | null = null): ApiState<T> {
  return {
    data: ref(initialData),
    loading: ref(false),
    error: ref(null),
  };
}

/**
 * 执行 API 调用的通用函数
 * @param apiFunction API 函数
 * @param params 参数
 * @param state API 状态对象
 * @param options 选项
 * @returns Promise<T>
 */
export async function executeApi<T>(
  apiFunction: (params: any) => Promise<T>,
  params: any = {},
  state: ApiState<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    transform?: (data: any) => T;
    silent?: boolean; // 是否静默执行（不显示 loading）
  } = {}
): Promise<T | null> {
  const { onSuccess, onError, transform, silent = false } = options;

  try {
    if (!silent) {
      state.loading.value = true;
    }
    state.error.value = null;

    const response = await apiFunction(params);
    const transformedData = transform ? transform(response) : response;

    state.data.value = transformedData;

    if (onSuccess) {
      onSuccess(transformedData);
    }

    return transformedData;
  } catch (err: any) {
    const errorMessage = err.message || "API request failed";
    state.error.value = errorMessage;

    if (onError) {
      onError(err);
    } else {
      console.error("API Error:", err);
    }

    return null;
  } finally {
    if (!silent) {
      state.loading.value = false;
    }
  }
}

/**
 * 批量执行 API 调用
 * @param apiCalls API 调用配置数组
 * @param state 共享状态对象
 * @returns Promise<any[]>
 */
export async function executeBatchApi<T>(
  apiCalls: Array<{
    apiFunction: (params: any) => Promise<any>;
    params?: any;
    key?: string;
  }>,
  state: ApiState<T>,
  options: {
    onSuccess?: (results: any[]) => void;
    onError?: (error: Error) => void;
    transform?: (results: any[]) => T;
  } = {}
): Promise<any[] | null> {
  const { onSuccess, onError, transform } = options;

  try {
    state.loading.value = true;
    state.error.value = null;

    const promises = apiCalls.map(({ apiFunction, params = {} }) => apiFunction(params));

    const results = await Promise.all(promises);

    const transformedData = transform ? transform(results) : results;
    state.data.value = transformedData;

    if (onSuccess) {
      onSuccess(results);
    }

    return results;
  } catch (err: any) {
    const errorMessage = err.message || "Batch API request failed";
    state.error.value = errorMessage;

    if (onError) {
      onError(err);
    } else {
      console.error("Batch API Error:", err);
    }

    return null;
  } finally {
    state.loading.value = false;
  }
}

/**
 * 重试机制的 API 调用
 * @param apiFunction API 函数
 * @param params 参数
 * @param maxRetries 最大重试次数
 * @param retryDelay 重试延迟（毫秒）
 * @returns Promise<T>
 */
export async function executeApiWithRetry<T>(
  apiFunction: (params: any) => Promise<T>,
  params: any = {},
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiFunction(params);
    } catch (error: any) {
      lastError = error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      // 等待后重试
      await new Promise((resolve) => setTimeout(resolve, retryDelay * (attempt + 1)));
    }
  }

  throw lastError!;
}

/**
 * 防抖 API 调用
 * @param apiFunction API 函数
 * @param delay 防抖延迟
 * @returns 防抖后的函数
 */
export function debounceApi<T>(apiFunction: (params: any) => Promise<T>, delay: number = 300) {
  let timeoutId: NodeJS.Timeout;

  return (params: any): Promise<T> => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId);

      timeoutId = setTimeout(async () => {
        try {
          const result = await apiFunction(params);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, delay);
    });
  };
}

/**
 * 缓存 API 结果
 * @param apiFunction API 函数
 * @param cacheTime 缓存时间（毫秒）
 * @returns 带缓存的函数
 */
export function cacheApi<T>(
  apiFunction: (params: any) => Promise<T>,
  cacheTime: number = 5 * 60 * 1000 // 默认5分钟
) {
  const cache = new Map<string, { data: T; timestamp: number }>();

  return async (params: any): Promise<T> => {
    const cacheKey = JSON.stringify(params);
    const cached = cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cacheTime) {
      return cached.data;
    }

    const result = await apiFunction(params);
    cache.set(cacheKey, { data: result, timestamp: Date.now() });

    return result;
  };
}

// ==================== 格式化工具 ====================

/**
 * 格式化奖励金额显示
 * @param award 奖励金额
 * @param fallback 无效值时的回退显示
 * @returns 格式化后的奖励字符串
 */
export const formatAward = (award: string | number | undefined | null, fallback = "--"): string => {
  if (!award) return fallback;

  const cleanValue = typeof award === "string" ? award.replace("-", "") : award;
  const numValue = typeof cleanValue === "string" ? parseFloat(cleanValue) : cleanValue;

  if (isNaN(numValue) || numValue < 0) {
    return fallback;
  }

  return Number(numValue).toLocaleString();
};

/**
 * 格式化投注金额显示
 * @param betAmount 投注金额
 * @param fallback 无效值时的回退显示
 * @returns 格式化后的投注金额字符串
 */
export const formatBetAmount = (
  betAmount: string | number | undefined | null,
  fallback = "--"
): string => {
  if (betAmount === null || betAmount === undefined) return fallback;

  // 处理字符串情况（移除所有负号）
  const cleanValue = typeof betAmount === "string" ? betAmount.replace(/-/g, "") : betAmount;

  // 转换为数字
  const numValue = typeof cleanValue === "string" ? parseFloat(cleanValue) : cleanValue;

  // 检查是否为有效数字
  if (isNaN(numValue)) {
    return typeof betAmount === "string" ? betAmount : fallback;
  }

  // 格式化数字（带两位小数）
  return numValue.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

/**
 * 安全获取排行榜数据
 * @param rankData 排行榜数据
 * @param index 索引
 * @param field 字段名
 * @param fallback 回退值
 * @returns 安全获取的值
 */
export const safeGetRankData = (
  rankData: any,
  index: number,
  field: string,
  fallback: any = null
): any => {
  try {
    return rankData?.list?.rank?.[index]?.[field] ?? fallback;
  } catch {
    return fallback;
  }
};

// ==================== 锦标赛工具 ====================

const dateType = "yyyy/MM/dd";

/**
 * 根据周期类型格式化时间范围
 */
export const formatTimeRange = (periodType: number, startTime: number, endTime: number): string => {
  switch (periodType) {
    case PERIOD_TYPES.HOURLY:
      return `${formatTime(startTime, false)}-${formatTime(endTime, false)}`;

    case PERIOD_TYPES.DAILY:
      return "00:00-24:00";

    case PERIOD_TYPES.CUSTOM:
      let resStr = "";
      const startDate = new Date(1000 * startTime);
      resStr += parseTime(startDate, dateType).slice(2);
      const endDate = new Date(1000 * endTime);
      resStr += "-" + parseTime(endDate, dateType).slice(2);
      return resStr;

    default:
      return "";
  }
};

/**
 * 将时间转换为 HHMMSS 格式的数字
 */
export const formatTimeToNumber = (date: Date): number => {
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return Number(hours + minutes + seconds);
};

/**
 * 获取锦标赛当前状态
 * @param periodType 周期类型 0=每天自定义，1=每日，2=不重复）
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 0-未开始 1-进行中 2-已结束
 */
export const getTournamentStatus = (
  periodType: number,
  startTime: number,
  endTime: number,
  rangeTimes?: string
): TournamentStatusCode => {
  // 0 每天自定义
  if (periodType === PERIOD_TYPES.HOURLY) {
    const startTimeFormat = formatTimeToNumber(new Date(1000 * startTime));
    const endTimeFormat = formatTimeToNumber(new Date(1000 * endTime));
    const currentTimeFormat = formatTimeToNumber(new Date());

    const minTime = Math.min(startTimeFormat, endTimeFormat);
    const maxTime = Math.max(startTimeFormat, endTimeFormat);

    if (currentTimeFormat < minTime) return 0;
    if (currentTimeFormat > maxTime) return 2;
    return 1;
  }

  // 按天周期 - 始终进行中 每日
  if (periodType === PERIOD_TYPES.DAILY) {
    return 1;
  }

  // 自定义时间段 不重复
  if (periodType === PERIOD_TYPES.CUSTOM) {
    let timeArr: any[] = [];
    if (rangeTimes) {
      timeArr = rangeTimes.split("-");
    }
    const now = Date.now() / 1000;
    if (startTime && now < startTime) return 0;
    else {
      const start_time = new Date(timeArr[0]).getTime() / 1000;
      if (now < start_time) return 0;
    }
    if (endTime && now > endTime) {
      return 2;
    } else if (timeArr.length > 1) {
      const end_time = new Date(timeArr[1]).getTime() / 1000;
      if (now > end_time) return 2;
    }
    return 1;
  }

  // 默认返回未开始状态
  return 0;
};

/**
 * 格式化奖金显示
 */
export const formatPrize = (amount: number): string => {
  return `₱${amount.toLocaleString()}`;
};

/**
 * 格式化参与人数显示
 */
export const formatParticipantCount = (count: number): string | number => {
  return count > 999 ? "999+" : count;
};

/**
 * 处理锦标赛数据，添加计算属性
 */
export const processTournamentData = (tournament: Tournament): ProcessedTournament => {
  const {
    period_type,
    start_time,
    end_time,
    registration_mode,
    registration_rule,
    match_user_count,
  } = tournament;

  const rangeTimes = formatTimeRange(period_type, start_time, end_time);
  const statusCode = getTournamentStatus(period_type, start_time, end_time);
  const statusName = TournamentStatus[statusCode] as TournamentStatusType;

  return {
    ...tournament,
    rangeTimes,
    status: statusCode,
    statusName,
    entryMode: ENTRY_MODES[registration_mode as 0 | 1],
    entryRule: ENTRY_RULES[registration_rule as 0 | 1 | 2],
    people: formatParticipantCount(match_user_count),
    badgeImage: tournament.badgeImage, // 这个会在组件中设置
  };
};

/**
 * 批量处理锦标赛列表数据
 */
export const processTournamentList = (tournaments: Tournament[]): ProcessedTournament[] => {
  return tournaments.map(processTournamentData);
};
