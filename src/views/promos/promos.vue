<script setup lang="ts">
defineOptions({ name: "Promos" });

// 根据 activity_list 区分活动页面内容
// 根据 jump_type 判断跳转什么页面，0不跳转，1内跳(内置打开网页)、2外跳(另外打开网页)，4跳转promos详情页，7跳转后台配置活动详情页  1、2跳转的地址都是url
// 详情页内根据pictures_jump判断跳转逻辑 1为跳转all games列表
// 通用配置活动页面内容 picture_details_title  picture_details 按钮文本button_text
import { ref } from "vue";
import { jumpPromosItem } from "@/utils/JumpPromo";
import { getServerSideImageUrl } from "@/utils/core/tools";

const props = defineProps({
  banners: {
    type: Array,
    default: [],
  },
});
</script>
<template>
  <div class="promos">
    <!-- 有活动数据时显示列表 -->
    <template v-if="banners.length > 0">
      <div v-for="(item, index) in banners" :key="item.id" class="promos-item">
        <ZImage
          :lazyLoad="index > 5"
          @click="() => jumpPromosItem(item)"
          :src="getServerSideImageUrl(item.home_page_banner)"
          fit="fill"
          type="promos"
        />
      </div>
    </template>

    <!-- 无活动数据时显示空状态 -->
    <div v-else class="no-data-container">
      <ZNoData text="No content available" />
    </div>

    <TabBarGap></TabBarGap>
  </div>
</template>

<style scoped lang="scss">
.promos {
  padding: 12px;
  min-height: calc(100vh - 70px);

  .promos-item {
    width: 100%;
    height: 94px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    border-radius: 20px;
    overflow: hidden;

    img {
      width: 100%;
      height: 94px;
      object-fit: fill;
    }
  }

  .no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: 40px 20px;
  }
}
</style>
