<template>
  <div class="tournament-card" @click="$emit('click', tournament)">
    <img class="badge" :src="tournament.badgeImage" :alt="tournament.status" />

    <div class="card-content">
      <img :src="tournament.banner_image" :alt="tournament.status" class="banner-image" />

      <div class="prize">
        <img :src="coinIcon" alt="Coin" class="coin-icon" />
        <span>{{ formatPrize(tournament.total_price) }}</span>
      </div>

      <van-row class="info">
        <van-col span="11" class="info-item">
          <ZIcon color="#c0c0c0" :size="16" type="icon-timeCycle" />
          <span>{{ tournament.rangeTimes }}</span>
        </van-col>

        <van-col span="13" class="info-item">
          <ZIcon color="#c0c0c0" :size="16" type="icon-group" />
          <span>{{ tournament.match_user_count }}</span>
        </van-col>

        <van-col span="11" class="info-item">
          <ZIcon color="#c0c0c0" :size="16" type="icon-tag1" />
          <span>{{ tournament.entryMode }}</span>
        </van-col>

        <van-col span="13" class="info-item">
          <ZIcon color="#c0c0c0" :size="16" type="icon-flag-fill" />
          <span>{{ tournament.entryRule }}</span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { ProcessedTournament } from "../configs";
import { formatPrize } from "../utils/promoUtils";
import { tournamentAssets } from "../configs";
import { getServerSideImageUrl } from "@/utils/core/tools";

interface Props {
  tournament: ProcessedTournament;
}

const props = defineProps<Props>();

const emits = defineEmits<{
  click: [tournament: ProcessedTournament];
}>();

// 计算属性
const coinIcon = computed(() => tournamentAssets.icons.coin);
</script>

<style scoped lang="scss">
.tournament-card {
  position: relative;
  border-radius: 20px;
  background: #fff;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card-content {
  .banner-image {
    border-radius: 20px;
    height: 160px;
    width: 100%;
    object-fit: fill;
    display: block;
  }
}

.badge {
  position: absolute;
  width: 86px;
  top: -8px;
  left: -12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 2;
}

.prize {
  display: flex;
  font-family: "D-DIN";
  justify-content: flex-start;
  padding: 12px 12px 0;
  color: #222222;
  font-size: 18px;
  font-weight: 700;
  gap: 4px;
  align-items: center;

  .coin-icon {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
  }
}

.info {
  padding: 8px 12px 12px;
  color: #666666;
  line-height: 22px;
  font-size: 14px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 4px;

    > span {
      margin-left: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
