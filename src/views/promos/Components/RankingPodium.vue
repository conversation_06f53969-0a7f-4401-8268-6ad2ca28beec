<template>
  <div class="ranking-podium">
    <div
      v-for="(index, idx) in [1, 0, 2]"
      :key="index"
      :class="`podium-item ${['second', 'first', 'third'][idx]}`"
    >
      <div class="podium-user">
        {{ getMaskedPlayerId(topThreeData[idx]) }}
      </div>
      <div class="podium-amount">
        <IconCoin />
        {{ formatAward(getAward(topThreeData[idx])) }}
      </div>
      <div class="podium-label">{{ betLabel }}</div>
      <div class="podium-bet">
        <IconCoin />
        {{ formatBetAmount(getBetAmount(topThreeData[idx]), { precision: 2 }) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType } from "vue";
import { maskString } from "@/utils/core/tools";
import { formatAward, formatBetAmount } from "../utils/promoUtils";
import type { RankingItem } from "../composables/useRankingData";

// Define component name for better debugging
defineOptions({
  name: "RankingPodium",
});

interface Props {
  topThreeData: (RankingItem | null)[];
  betLabel?: string;
}

const props = withDefaults(defineProps<Props>(), {
  betLabel: "Bet",
});

// 获取脱敏用户ID
const getMaskedPlayerId = (item: RankingItem | null): string => {
  if (!item?.player_id) return "--";
  return maskString(item.player_id, 2, 3);
};

// 获取奖励金额
const getAward = (item: RankingItem | null): string | number => {
  return item?.award || "--";
};

// 获取投注金额
const getBetAmount = (item: RankingItem | null): string | number => {
  return item?.total_bet_amount || "--";
};
</script>

<style lang="scss" scoped>
.ranking-podium {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  font-family: "D-DIN";
}

.podium-item {
  background: url("@/assets/images/promos/ranking_bg.png") no-repeat;
  background-size: 308px 365px; // min(75.733vw, 363.518px) min(91.733vw, 440.318px);
  width: 29%;
  text-align: center;
  padding: 52px 0 10px 0;
  color: #ff9a3c;
  position: relative;

  &.first {
    color: #ffa902;
    width: 33vw;
    height: 54vw;
    z-index: 2;
    background: transparent;
    padding-top: 70px;

    &::before {
      content: "";
      position: absolute;
      left: 50%;
      top: 50.5%;
      width: 210px;
      height: 34vw;
      background: url("@/assets/images/promos/ranking_bg.png") no-repeat;
      background-size: 308px 365px; // min(75.733vw, 363.518px) min(91.733vw, 440.318px);
      // background-size: 76vw 92vw;
      background-position: left -30px bottom -225px;
      transform: translate(-50%, -50%) rotate(-90deg);
      z-index: -1;
    }
  }

  &.second {
    color: #73b5f6;
    height: 176px;
    z-index: 1;
    background-position: right 0px bottom -40px;
  }

  &.third {
    color: #ff9c59;
    height: 176px;
    z-index: 1;
    background-position: right -114px bottom -40px;
  }
}

.podium-amount {
  font-size: 22px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.podium-label {
  font-size: 12px;
  color: #999;
  margin: 4px 0;
  font-family: "Inter";
}

.podium-user {
  font-size: 14px;
  color: #666;
  display: flex;
  font-weight: 600;
  align-items: center;
  justify-content: center;
  margin: 10px 0 4px;
}
.podium-bet {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #333;
  font-weight: 600;
}
</style>
