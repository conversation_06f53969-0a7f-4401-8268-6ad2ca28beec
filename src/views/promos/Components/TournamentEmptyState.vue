<template>
  <div class="empty-state">
    <div class="empty-image">
      <img :src="noDataImage" alt="No Data" />
    </div>
    <div class="empty-text">{{ message }}</div>
    <slot name="action"></slot>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { tournamentAssets } from "../configs";

interface Props {
  message?: string;
}

const props = withDefaults(defineProps<Props>(), {
  message: "No tournaments available",
});

const noDataImage = computed(() => tournamentAssets.icons.noData);
</script>

<style scoped lang="scss">
.empty-state {
  padding-top: 30%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 16px;

  .empty-image {
    img {
      width: 120px;
      height: 120px;
      opacity: 0.6;
    }
  }

  .empty-text {
    color: #999;
    font-size: 16px;
    text-align: center;
  }
}
</style>
