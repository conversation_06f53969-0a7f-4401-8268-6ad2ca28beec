<!--
  登录页面头部导航组件
  负责显示关闭按钮和返回按钮
-->
<template>
  <div class="login-header">
    <!-- 关闭按钮 -->
    <ZIcon v-if="showClose" @click="handleClose" type="icon-close" color="" class="header-button close-button"></ZIcon>
    <!-- 返回按钮 -->
    <ZIcon v-if="showBack" @click="handleBack" type="icon-fanhui" color="" class=" header-button back-button"></ZIcon>
  </div>
</template>

<script setup lang="ts">
/**
 * 登录页面头部导航组件
 */
interface Props {
  /** 是否显示关闭按钮 */
  showClose?: boolean;
  /** 是否显示返回按钮 */
  showBack?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showClose: false,
  showBack: false,
});

const emit = defineEmits<{
  /** 点击关闭按钮 */
  close: [];
  /** 点击返回按钮 */
  back: [];
}>();

/**
 * 处理关闭按钮点击
 */
const handleClose = () => {
  emit('close');
};

/**
 * 处理返回按钮点击
 */
const handleBack = () => {
  emit('back');
};
</script>

<style scoped lang="scss">
.login-header {
  position: relative;
  width: 100%;

  .header-button {
    position: absolute;
    top: 10px;
    color: #909090;
    cursor: pointer;
    z-index: 10;
  }

  .close-button {
    right: 20px;
  }

  .back-button {
    left: 20px;
  }
}
</style>
