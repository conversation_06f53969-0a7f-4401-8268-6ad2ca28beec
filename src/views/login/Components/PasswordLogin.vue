<template>
  <!-- 密码登录 -->
  <div class="main">
    <PhoneInput v-model="phone" @validityChange="onPhoneValidityChange" />
    <div class="form-item">
      <div class="input-wrap">
        <input class="input" v-model="password" :type="passwordType" maxlength="20" minlength="8"
          placeholder="Enter Your Password" @input="pwdChange" />
        <div class="action-group">
          <ZIcon v-show="password" type="icon-close" size="16" color="#999" @click="clearPassword" />
          <ZIcon @click="togglePasswordVisibility" size="16"
            :class="`icon-${passwordType === 'password' ? 'yanjing' : 'yanjing1'}`" color="#999" />
        </div>
      </div>
      <div class="tip-wrap">
        <div class="password-error">
          {{ pwdError ? 'The login password is 8~20 characters and must contain letters and numbers.' : ' ' }}
        </div>
        <span @click="showForgot" class="forget">forgot ?</span>
      </div>
    </div>

    <ZButton :click="handleLogin" :disabled="!isFormValid" class="btn">
      Log in
    </ZButton>

    <div @click="checkLoginTypeClick" class="form-redline">
      Login With Verification Code
      <ZIcon type="icon-qianjin" color=""></ZIcon>
    </div>

    <!-- 重置密码弹窗 -->
    <ZVerifyDialog v-model:showDialog="showResetPwdDialog" :passPhone="phone"
      :verifyType="PN_VERIFY_TYPE.ForgetPassword" :succCallBack="resetPwdSuccessCallback" />
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, computed } from "vue";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { phoneExists } from "@/api/user";
import { showToast } from "vant";
import PhoneInput from "./PhoneInput.vue";
import { loginManager } from "@/utils/managers/LoginManager";

interface Props {
  phoneValue?: string;
  onLogin?: (payload: { password: string; phone: string }) => Promise<void>;
  onSwitchMode?: (phone: string) => void;
}

const props = withDefaults(defineProps<Props>(), {
  phoneValue: "",
  onLogin: undefined,
  onSwitchMode: undefined,
});

// 响应式数据
const showResetPwdDialog = ref(false);
const phone = ref(props.phoneValue);
const password = ref("");
const passwordType = ref<"password" | "text">("password");
const pwdError = ref(false);
const phoneIsValid = ref(false);

// 计算属性
const isFormValid = computed(() => {
  return phoneIsValid.value && password.value && !pwdError.value;
});

// 密码验证正则：包含数字与字母，长度在8-20之间
const PASSWORD_REGEX = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]{8,20}$/;

// 监听器
watch(
  () => props.phoneValue,
  (newVal) => {
    phone.value = newVal;
  }
);

// 事件处理方法
const checkLoginTypeClick = () => {
  props.onSwitchMode?.(phone.value);
};

const onPhoneValidityChange = (isValid: boolean) => {
  phoneIsValid.value = isValid;
};

const togglePasswordVisibility = () => {
  passwordType.value = passwordType.value === "password" ? "text" : "password";
};

const clearPassword = () => {
  password.value = ''
  pwdError.value = false
}
const pwdChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const val = target.value;
  pwdError.value = !PASSWORD_REGEX.test(val);
};
const showForgot = async () => {
  if (!phoneIsValid.value) {
    showToast("Please enter a valid 10-digit mobile phone number.");
    return;
  }

  try {
    const res = await phoneExists({ phone: phone.value });
    // 根据实际 API 响应结构调整
    const responseData = res.data || res;
    if (responseData.exists === 0) {
      showToast("Please register an account first");
      return;
    } else if (responseData.exists === 2) {
      loginManager.showLock(responseData)
      return;
    }
    showResetPwdDialog.value = true;
  } catch (error) {
    console.error("Check phone exists error:", error);
    showToast("Network error, please try again");
  }
};

const resetPwdSuccessCallback = (loginPWD: string) => {
  showToast("Login password reset successfully");
  showResetPwdDialog.value = false;
  password.value = loginPWD;
};

const handleLogin = async () => {
  if (!isFormValid.value) {
    showToast("Please enter valid username and password");
    return;
  }

  try {
    props.onLogin?.({
      password: password.value,
      phone: phone.value
    });
  } catch (error) {
    console.error("Login error:", error);
    // 错误处理由父组件处理
  }
};
</script>
<style lang="scss" scoped>
.form-redline {
  text-align: center;
  margin-top: 16px;
  color: #666;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.btn {
  margin-top: 16px;
  height: 48px;
}

/* 表单项 */
.form-item {
  margin-top: 20px;
  position: relative;

  .tip-wrap {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;

    .password-error {
      flex: 1;
      color: var(--Nustar, #AC1140);
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .forget {
      color: #666;
      text-align: right;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }

  /* 输入框样式 */
  .input-wrap {
    width: 100%;
    height: 30px;
    border-bottom: 1px solid #ddd;
    font-size: 16px;
    font-weight: 600;
    outline: none;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .action-group {
      display: flex;
      gap: 16px;
    }

    .input {
      width: 100%;
      margin-left: 6px;
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      font-weight: 600;
      color: #222;
      font-family: D-DIN;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      &::placeholder {
        color: #C0C0C0;
        /* 输入框内默认文字 */
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
}
</style>
