<template>
  <div class="casino-images">
    <div class="casino-item" @click="navigateToCasino(1)">
      <ZImage :src="Baccarat" alt="Baccarat" />
    </div>
    <div class="group">
      <div class="casino-item" @click="navigateToCasino(2)">
        <ZImage :src="Roulette" alt="Roulette" />
      </div>
      <div class="casino-item" @click="navigateToCasino(3)">
        <ZImage :src="Blackjack" alt="Blackjack" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import router from "@/router";
import Baccarat from "@/assets/images/game/Baccarat.png";
import Blackjack from "@/assets/images/game/Blackjack.png";
import Roulette from "@/assets/images/game/Roulette.png";

// 导航到赌场分类页面
const navigateToCasino = (id: number): void => {
  router.push(`/casino-cate?id=${id}`);
};
</script>

<style lang="scss" scoped>
.casino-images {
  .casino-item {
    border-radius: 12px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }

  .group {
    display: flex;
    gap: 12px;
    margin-top: 12px;

    .casino-item {
      flex: 1;
    }
  }
}
</style>
