<template>
  <ZPage>
    <div class="categories-container">
      <!-- 导航栏区域，添加背景效果容器 -->
      <div class="nav-bar-effect">
        <!-- 自定义导航栏组件，传递确认事件、可见性绑定及搜索事件 -->
        <CustomNavbar
          ref="navbarRef"
          class="nav-bar"
          :confirm="handleConfirmFilters"
          v-model:visible="dialogVisible"
          @search="handleSearch"
        />
      </div>
      <!-- 主要内容区域，分类展示 -->
      <div class="categories">
        <!-- 分类标签页，有分类数据时显示 -->
        <div class="categories-tabs">
          <van-tabs
            v-model:active="currentIndex"
            @change="handleTabChange"
            swipeable
            shrink
            line-height="0"
            background="transparent"
          >
            <van-tab
              v-for="category in filteredCategories"
              :key="category.id"
              :title="category.name"
            >
              <!-- 游戏列表容器，处理滚动事件和ref设置 -->
              <div class="games-container" @scroll="handleScroll($event, category.id)">
                <!-- 特殊分类（casino）的图片展示区域 -->
                <div v-show="isSpecialCategory(category.name)" class="special-category-images">
                  <Casino />
                </div>
                <!-- 游戏数据 -->
                <van-row
                  class="games-grid"
                  gutter="12"
                  v-if="category.pagedGames && category.pagedGames.length > 0"
                >
                  <van-col
                    v-for="game in category.pagedGames"
                    :key="game.id"
                    :span="getGameSpan(game, category)"
                  >
                    <!-- 游戏项组件，非换行标记时渲染，传递游戏数据和更新喜欢状态事件 -->
                    <GameItem
                      v-show="!isLineBreak(game)"
                      :game="game"
                      @updateLike="handleUpdateLike"
                    />
                  </van-col>
                </van-row>
                <!-- 无游戏数据状态展示 - 只有在不加载且确实无数据时显示 -->
                <div v-if="shouldShowNoData(category)" class="no-data">
                  <template v-if="hasFilters">
                    <div>
                      <ZNoData text="Your filters has returned no results"></ZNoData>
                      <div
                        v-if="hasFilters"
                        @click="handleClearFilters"
                        size="small"
                        class="clear-filters-btn"
                      >
                        Clear Filters
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <ZNoData text="No Record"></ZNoData>
                  </template>
                </div>
              </div>
            </van-tab>
          </van-tabs>
        </div>
      </div>
    </div>
  </ZPage>
</template>
<script setup lang="ts">
defineOptions({ name: "Categories" });
import { useRoute, useRouter } from "vue-router";
import { ref, computed, onMounted, onActivated } from "vue";
import { storeToRefs } from "pinia";

import CustomNavbar from "./Components/CustomNavbar.vue";
import Casino from "./Components/Casino.vue";
import GameItem from "@/components/GameItem.vue";
import { useGameCategoriesStore } from "@/stores/gameCategories";
import { useGameFiltersStore } from "@/stores/gameFilters";
import { useGameStore } from "@/stores/game";
import type { Game, GameCategory } from "./types";

const route = useRoute();
const router = useRouter();
const gameStore = useGameStore();
const { gameTypes } = storeToRefs(gameStore);

const gameFiltersStore = useGameFiltersStore();
const gameCategoriesStore = useGameCategoriesStore();
const {
  filteredCategories,
  hasFilters,
  isDataLoading,
  isLoadingMore,
  currentIndex,
  dialogVisible,
} = storeToRefs(gameCategoriesStore);

const navbarRef = ref();

const categoryId = computed(() => {
  return String(route.query.categoryId || gameTypes.value[0]?.id || "");
});

const handleClearFilters = () => {
  gameFiltersStore.clearFilters(router, route);
  navbarRef.value?.setCheckedProviders();
};
const handleConfirmFilters = (categories: string[] = [], providerDetails: any[] = []) => {
  gameFiltersStore.setSelectedCategories(categories, router, route);
};
const handleSearch = (value: string) => {
  gameFiltersStore.setSearchValue(value, router, route);
};

const shouldShowNoData = (category: any) => {
  return !isDataLoading.value && (!category.pagedGames || !category.pagedGames.length);
};

const handleTabChange = async (index: number): Promise<void> => {
  await gameCategoriesStore.handleTabChange(index);
};

const handleScroll = async (event: Event, categoryId: string | number): Promise<void> => {
  const element = event.target as HTMLElement;
  const { scrollTop, scrollHeight, clientHeight } = element;
  if (scrollHeight - scrollTop - clientHeight < 50) {
    const category = filteredCategories.value.find((c) => c.id === categoryId);
    if (category?.hasMore && !isLoadingMore.value) {
      await gameCategoriesStore.loadMoreGames(categoryId);
    }
  }
};

const handleUpdateLike = async (updatedGame: Game): Promise<void> => {
  await gameCategoriesStore.handleUpdateLike(updatedGame);
};

const isSpecialCategory = (categoryName: string | number): boolean => {
  return String(categoryName).toLowerCase() === "casino";
};
const isLineBreak = (game: Game): boolean => {
  return String(game.id).startsWith("lineBreak_");
};
const getGameSpan = (game: Game, cate: GameCategory): number => {
  return game.big_images_set !== 0 && !["like", "history"].includes(String(cate.id)) ? 12 : 8;
};

const initializeTab = async () => {
  if (categoryId.value) {
    const index = filteredCategories.value.findIndex(
      (cat) => `${cat.id}` === `${categoryId.value}`
    );
    if (index !== -1) {
      await handleTabChange(index);
      return;
    }
  }
  if (filteredCategories.value.length > 0) {
    await handleTabChange(0);
  }
};

onMounted(async () => {
  // 首次进入时，如果有 query 参数，初始化筛选条件
  gameFiltersStore.initFromQueryOnFirstVisit(route);
  await initializeTab();
});
onActivated(() => {
  navbarRef.value?.forceRefresh?.();
  // 只在激活时同步 store 的筛选条件到地址栏
  gameFiltersStore.syncToQuery(router, route);
});
</script>

<style lang="scss" scoped>
.categories-container {
  height: 100%;

  .nav-bar-effect {
    min-height: 60px;

    .nav-bar {
      // 导航栏固定顶部并添加磨砂玻璃效果
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 100;
      backdrop-filter: blur(10px);
      background-color: rgba(255, 255, 255, 0.9);
    }
  }

  .categories {
    height: calc(100vh - 60px);
    overflow: hidden;
    background: linear-gradient(to bottom, #ffffff, #f4f8fb 20%);

    .categories-tabs {
      height: 100%;

      // 自定义 van-tabs 组件样式
      &:deep(.van-tabs) {
        height: 100%;

        .van-tabs__nav {
          // 优化 tab 导航栏样式
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(12px);
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .van-tabs__content {
          height: calc(100% - 44px);
        }

        .van-tab__panel {
          height: 100%;
        }
      }

      .games-container {
        height: 100%;
        overflow-y: auto;
        padding: 12px;
        -webkit-overflow-scrolling: touch;
        /* 关键属性，启用iOS弹性滚动 */

        .games-grid {
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;

          &:deep(.game-item) {
            margin-bottom: 20px;

            .game-item-img {
              height: 110px !important;
            }

            .game-item-like {
              right: 6px;
              bottom: 6px;
            }
          }
        }

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          // width: 4px;
          width: 0;
        }

        &::-webkit-scrollbar-track {
          background: transparent;
        }

        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
        }

        // iOS 特定优化
        @supports (-webkit-overflow-scrolling: touch) {
          // 强制启用硬件加速
          -webkit-transform: translate3d(0, 0, 0);
          // 优化滚动回弹
          -webkit-overflow-scrolling: touch;
          // 减少滚动延迟
          -webkit-scroll-snap-type: none;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.2);
        }
      }

      // 特殊分类图片展示区域样式
      .special-category-images {
        margin-bottom: 12px;
      }

      // 无数据状态样式
      .no-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px 20px;
        text-align: center;

        .clear-filters-btn {
          margin-top: 16px;
          cursor: pointer;
          background-color: #eee;
          display: inline-block;
          padding: 6px 10px;
          border-radius: 20px;
        }
      }
    }
  }
}
</style>
