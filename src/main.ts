import { createApp } from "vue";
import { Lazyload } from "vant";
import App from "./App.vue";
import router from "./router";
import { setToastDefaultOptions } from "vant";
import "@vant/touch-emulator";

// 优先导入 iconfont 样式 - 使用同步导入避免重复加载
import "./assets/iconfonts/iconfont.css";
// 其他样式
import "./assets/main.scss";
import "./assets/reset.css";
import "./assets/styles/base.scss";

// 导入配置函数
import { registerGlobalComponents } from "@/enter/registerComponents";
import { registerVantComponents, setGlobalDialog, setGlobalActionSheet } from "@/enter/vant";
import { lazyloadConfig } from "@/enter/vueLazyload";
import { configureHttp } from "@/enter/http";
import { pinia } from "@/stores";
import { useDialog } from "@/components/ZDialog/dialog";
import { useActionSheet } from "@/components/ZActionSheet/actionSheet";

// 初始化 VConsole 调试工具
import { vConsoleManager } from "@/utils/managers/VConsoleManager";

// 异步初始化 VConsole
vConsoleManager.initialize({
  theme: "light",
  maxLogNumber: 1000,
  onReady: () => {
    console.log("📱 VConsole 调试工具已就绪");
  },
});

// HTTP, 需放在前面初始化
configureHttp();

const app = createApp(App);
// 配置全局属性
const dialogInstance = useDialog(app._context);
app.config.globalProperties.$dialog = dialogInstance;

// 配置 ActionSheet 全局属性
const actionSheetInstance = useActionSheet(app._context);
app.config.globalProperties.$actionSheet = actionSheetInstance;

// 保存到工具模块
setGlobalDialog(dialogInstance);
setGlobalActionSheet(actionSheetInstance);
// 使用
// import { getGlobalDialog,getGlobalActionSheet } from "@/enter/vant";
// const $dialog = getGlobalDialog();
// const $actionSheet = getGlobalActionSheet();
// 抽取组件注册
registerGlobalComponents(app);
// 抽取 Vant 组件注册
registerVantComponents(app);

// 全局配置所有 Toast 的默认换行方式为 break-word
setToastDefaultOptions({
  wordBreak: "break-word",
});

// 懒加载
app.use(pinia);
app.use(Lazyload, lazyloadConfig);
app.use(router);

// 移除 promoJump 插件使用，改为直接导入 utils 方法
// app.use(promoJump, { router });

app.mount("#app");
