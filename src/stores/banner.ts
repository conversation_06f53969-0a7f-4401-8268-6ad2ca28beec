/**
 * Banner Store
 * 管理轮播图数据，供全局使用
 */

import { defineStore } from "pinia";
import { orderBy } from "lodash-es";
import { getBanners } from "@/api/games";
import type { BannerItem, BannerStoreState } from "@/types/store";

export const BANNER_STORE = "BANNER_STORE";

// 缓存过期时间：5分钟
const CACHE_EXPIRY_TIME = 5 * 60 * 1000;

// 初始状态值
const initValue: BannerStoreState = {
  banners: [],
  isLoading: false,
  lastFetchTime: 0,
  cacheExpiry: CACHE_EXPIRY_TIME,
};

export const useBannerStore = defineStore("banner", {
  state: (): BannerStoreState => ({
    ...initValue,
  }),

  getters: {
    /**
     * 是否需要重新获取数据
     */
    shouldRefetch: (state): boolean => {
      const now = Date.now();
      return now - state.lastFetchTime > state.cacheExpiry;
    },

    /**
     * 获取首页轮播图
     */
    homeBanners: (state): BannerItem[] => {
      return state.banners.filter((banner) => banner.home_page_banner);
    },

    /**
     * 获取促销页轮播图
     */
    promoBanners: (state): BannerItem[] => {
      return state.banners.filter((banner) => banner.promo_page_banner);
    },

    /**
     * 获取奖金钱包轮播图
     */
    rewardWalletBanners: (state): BannerItem[] => {
      return state.banners.filter((banner) => banner.rewards_wallet_banner);
    },

    /**
     * 根据类型获取轮播图
     * @param bannerType 轮播图类型
     */
    getBannersByType:
      (state) =>
      (bannerType: string): BannerItem[] => {
        return state.banners.filter((banner) => {
          switch (bannerType) {
            case "rewardWallet":
              return (
                banner.rewards_wallet_banner || banner.promo_page_banner || banner.home_page_banner
              );
            case "promo":
              return banner.promo_page_banner || banner.home_page_banner;
            case "home":
            default:
              return banner.home_page_banner;
          }
        });
      },

    /**
     * 获取轮播图的图片URL
     * @param banner 轮播图数据
     * @param bannerType 轮播图类型
     */
    getBannerImageUrl:
      () =>
      (banner: BannerItem, bannerType: string = "home"): string => {
        switch (bannerType) {
          case "rewardWallet":
            return (
              banner.rewards_wallet_banner ||
              banner.promo_page_banner ||
              banner.home_page_banner ||
              ""
            );
          case "promo":
            return banner.promo_page_banner || banner.home_page_banner || "";
          case "home":
          default:
            return banner.home_page_banner || "";
        }
      },

    /**
     * 是否有轮播图数据
     */
    hasBanners: (state): boolean => state.banners.length > 0,

    /**
     * 获取排序后的轮播图列表
     */
    sortedBanners: (state): BannerItem[] => {
      return orderBy(state.banners, ["sort"], ["desc"]);
    },
  },

  actions: {
    /**
     * 获取轮播图数据
     * @param forceRefresh 是否强制刷新
     * @returns Promise<BannerItem[]>
     */
    async fetchBanners(forceRefresh: boolean = false): Promise<BannerItem[]> {
      // 如果不需要强制刷新且缓存未过期，直接返回缓存数据
      if (!forceRefresh && !this.shouldRefetch && this.banners.length > 0) {
        return this.banners;
      }

      this.isLoading = true;

      try {
        const res = await getBanners({});

        // Handle both direct response and AxiosResponse formats
        const responseData = res.data || res;
        const bannerData = responseData?.banner;

        if (bannerData && Array.isArray(bannerData)) {
          // 按 sort 字段降序排序
          const sortedBanners = orderBy(bannerData, ["sort"], ["desc"]);
          this.banners = sortedBanners;
          this.lastFetchTime = Date.now();

          console.log("Banner data fetched successfully:", this.banners.length, "items");
          return this.banners;
        } else {
          console.warn("Invalid banner response:", res);
          this.banners = [];
          return [];
        }
      } catch (error) {
        console.error("Failed to fetch banners:", error);
        // 如果请求失败，保持现有数据不变
        return this.banners;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * 刷新轮播图数据
     * @returns Promise<BannerItem[]>
     */
    async refreshBanners(): Promise<BannerItem[]> {
      return this.fetchBanners(true);
    },

    /**
     * 设置轮播图数据
     * @param banners 轮播图数组
     */
    setBanners(banners: BannerItem[]): void {
      this.banners = banners;
      this.lastFetchTime = Date.now();
    },

    /**
     * 添加轮播图
     * @param banner 轮播图数据
     */
    addBanner(banner: BannerItem): void {
      this.banners.push(banner);
      // 重新排序
      this.banners = orderBy(this.banners, ["sort"], ["desc"]);
    },

    /**
     * 更新轮播图
     * @param id 轮播图ID
     * @param updates 更新数据
     */
    updateBanner(id: number, updates: Partial<BannerItem>): void {
      const index = this.banners.findIndex((banner) => banner.id === id);
      if (index !== -1) {
        this.banners[index] = { ...this.banners[index], ...updates };
        // 如果更新了排序，重新排序
        if (updates.sort !== undefined) {
          this.banners = orderBy(this.banners, ["sort"], ["desc"]);
        }
      }
    },

    /**
     * 删除轮播图
     * @param id 轮播图ID
     */
    removeBanner(id: number): void {
      this.banners = this.banners.filter((banner) => banner.id !== id);
    },

    /**
     * 清空轮播图数据
     */
    clearBanners(): void {
      this.banners = [];
      this.lastFetchTime = 0;
    },

    /**
     * 设置缓存过期时间
     * @param expiry 过期时间（毫秒）
     */
    setCacheExpiry(expiry: number): void {
      this.cacheExpiry = expiry;
    },

    /**
     * 重置 store 到初始状态
     */
    resetStore(): void {
      Object.assign(this, { ...initValue });
    },

    /**
     * 预加载轮播图数据（如果需要）
     * 这个方法可以在应用启动时调用
     */
    async preloadBanners(): Promise<void> {
      if (this.banners.length === 0 || this.shouldRefetch) {
        await this.fetchBanners();
      }
    },
  },

  persist: {
    key: BANNER_STORE,
    storage: window.localStorage,
    // 只持久化轮播图数据和最后获取时间，不持久化加载状态
    pick: ["banners", "lastFetchTime"],
  },
});
