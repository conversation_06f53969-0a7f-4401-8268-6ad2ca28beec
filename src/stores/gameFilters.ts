/**
 * 游戏筛选状态全局 Store
 * 用于在 game-categories 和 casino-cate 页面间共享筛选状态
 */

import { defineStore } from "pinia";
import { ref, computed, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { FilterState } from "@/views/game-categories/types";

export const useGameFiltersStore = defineStore("gameFilters", () => {
  // ==================== 状态定义 ====================

  // 筛选状态
  const filterState = ref<FilterState>({
    searchValue: "",
    selectedCategories: ["all"],
  });

  // 是否正在同步 URL 参数（防止循环更新）
  const isSyncingUrl = ref(false);

  // ==================== 计算属性 ====================

  // 是否有筛选条件
  const hasFilters = computed(() => {
    return (
      filterState.value.selectedCategories.length > 0 &&
      !filterState.value.selectedCategories.includes("all")
    );
  });

  // ==================== 方法定义 ====================

  /**
   * 首次访问时从 URL query 参数初始化筛选状态（会重置所有状态）
   */
  const initFromQueryOnFirstVisit = (route: any) => {
    const { search, providerIds } = route.query;

    // 初始化搜索值
    filterState.value.searchValue = search && typeof search === "string" ? search : "";

    // 初始化厂商筛选
    if (providerIds) {
      const providers = Array.isArray(providerIds)
        ? providerIds.flatMap((id) => String(id).split(",")).filter((id) => id.trim())
        : String(providerIds)
            .split(",")
            .filter((id) => id.trim());

      filterState.value.selectedCategories = providers.length > 0 ? providers : ["all"];
    } else {
      filterState.value.selectedCategories = ["all"];
    }
  };

  /**
   * 从 URL query 参数更新筛选状态（只更新有参数的部分）
   */
  const initFromQuery = (route: any) => {
    if (isSyncingUrl.value) {
      return;
    }

    const { search, providerIds } = route.query;

    // 只有当有搜索参数时才更新搜索值
    if (search && typeof search === "string") {
      filterState.value.searchValue = search;
    } else if (search === "") {
      // 明确传入空字符串时才清空
      filterState.value.searchValue = "";
    } else {
    }

    // 只有当有厂商参数时才更新厂商筛选
    if (providerIds) {
      const providers = Array.isArray(providerIds)
        ? providerIds.flatMap((id) => String(id).split(",")).filter((id) => id.trim())
        : String(providerIds)
            .split(",")
            .filter((id) => id.trim());

      if (providers.length > 0) {
        filterState.value.selectedCategories = providers;
      } else {
        filterState.value.selectedCategories = ["all"];
      }
    } else {
      console.log("🔄 gameFilters: 保持当前厂商筛选", filterState.value.selectedCategories);
    }
  };

  /**
   * 将筛选状态同步到 URL query 参数
   */
  const syncToQuery = (router: any, route: any) => {
    if (isSyncingUrl.value) return;

    isSyncingUrl.value = true;

    const query: Record<string, any> = { ...route.query };

    // 同步搜索值
    if (filterState.value.searchValue && filterState.value.searchValue.trim()) {
      query.search = filterState.value.searchValue.trim();
    } else {
      delete query.search;
    }

    // 同步厂商筛选
    if (
      filterState.value.selectedCategories.length > 0 &&
      !filterState.value.selectedCategories.includes("all")
    ) {
      query.providerIds = filterState.value.selectedCategories.join(",");
    } else {
      delete query.providerIds;
    }

    // 更新 URL（不触发导航）
    router.replace({ query }).finally(() => {
      isSyncingUrl.value = false;
    });
  };

  /**
   * 设置搜索值
   */
  const setSearchValue = (value: string, router?: any, route?: any) => {
    filterState.value.searchValue = value;
    if (router && route) {
      syncToQuery(router, route);
    }
  };

  /**
   * 设置厂商筛选
   */
  const setSelectedCategories = (categories: string[], router?: any, route?: any) => {
    // 确保数组去重并过滤空值
    const cleanedCategories = [...new Set(categories)].filter((cat) => cat && cat.trim());
    filterState.value.selectedCategories =
      cleanedCategories.length > 0 ? cleanedCategories : ["all"];
    console.log("setSelectedCategories:", filterState.value.selectedCategories);
    if (router && route) {
      syncToQuery(router, route);
    }
  };

  /**
   * 清除所有筛选条件
   */
  const clearFilters = (router?: any, route?: any) => {
    filterState.value.selectedCategories = ["all"];
    if (router && route) {
      syncToQuery(router, route);
    }
  };

  /**
   * 重置筛选状态（不同步到 URL）
   */
  const resetFilters = () => {
    filterState.value.searchValue = "";
    filterState.value.selectedCategories = ["all"];
  };

  /**
   * 处理搜索
   */
  const handleSearch = (value: string, router?: any, route?: any) => {
    setSearchValue(value, router, route);
  };

  /**
   * 处理清除筛选条件
   */
  const handleClearFilters = (router?: any, route?: any) => {
    clearFilters(router, route);
  };

  /**
   * 处理确认筛选条件
   */
  const handleConfirmFilters = (categories: string[] = [], router?: any, route?: any) => {
    setSelectedCategories(categories, router, route);
  };

  // ==================== 返回 ====================

  return {
    // 状态
    filterState,
    hasFilters,
    isSyncingUrl,

    // 方法
    initFromQuery,
    initFromQueryOnFirstVisit,
    syncToQuery,
    setSearchValue,
    setSelectedCategories,
    clearFilters,
    resetFilters,
    handleSearch,
    handleClearFilters,
    handleConfirmFilters,
  };
});
