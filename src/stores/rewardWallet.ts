import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { getBonusWalletList, receiveWalletTask, receiveWalletReward } from "@/api/promos";
import { useGlobalStore } from "@/stores/global";
import { showToast } from "vant";

// 任务状态枚举
export enum TaskStatus {
  LOCKED = 1, // 未解锁
  ONGOING = 2, // 进行中
  COMPLETED = 3, // 可领取
  EXPIRED = 4, // 过期
  CLAIMED = 5, // 已领取
}

// 奖金钱包项目接口
export interface WalletTaskItem {
  id: number;
  wallet_task_id: number;
  user_id: number;
  bonus: number;
  task_status: TaskStatus;
  created_at: string;
  task_name: string;
  task_rule: string;
  bet_target_value: number;
  recharge_target_value: number;
  bet_num: number;
  expire_time: number;
  recharge_done: boolean;
  countdown?: string;
}

// 状态排序优先级
const STATUS_ORDER = { 3: 1, 2: 2, 1: 3, 4: 4, 5: 5 };

export const useRewardWalletStore = defineStore("rewardWallet", () => {
  // 状态
  const loading = ref(true);
  const bonusList = ref<WalletTaskItem[]>([]);
  const rules = ref<string[]>([]);
  const availableCount = ref(0);
  const claimingBonusId = ref<string | number | null>(null);
  const showGuideDialog = ref(false);

  // 防重复调用机制
  const isLoadingBonusList = ref(false);
  const lastFetchTime = ref(0);
  const FETCH_COOLDOWN = 2000; // 2秒防重复调用间隔

  // 计算属性
  const hasOngoingBonuses = computed(() =>
    bonusList.value.some((bonus) => bonus.task_status === TaskStatus.ONGOING)
  );

  const hasAvailableBonus = computed(() => availableCount.value > 0);

  const hasContent = computed(() => bonusList.value.length > 0);

  // 计算总奖金金额（所有状态的奖金累计）
  const bonusTotal = computed(() => {
    return bonusList.value.reduce((total, item) => {
      // 只累计未过期和未领取的奖金
      if (![TaskStatus.EXPIRED, TaskStatus.CLAIMED].includes(item.task_status)) {
        return total + Number(item.bonus);
      }
      return total;
    }, 0);
  });

  // 计算可领取奖金总额
  const availableBonusTotal = computed(() => {
    return bonusList.value
      .filter((item) => item.task_status === TaskStatus.COMPLETED)
      .reduce((total, item) => total + Number(item.bonus), 0);
  });

  // 获取奖金列表
  const fetchBonusList = async (userId?: string | number) => {
    // 防重复调用检查
    const now = Date.now();
    if (isLoadingBonusList.value || now - lastFetchTime.value < FETCH_COOLDOWN) {
      // loading.value = false;
      return;
    }

    try {
      isLoadingBonusList.value = true;
      loading.value = true;
      lastFetchTime.value = now;

      // 获取用户ID
      if (!userId) {
        const globalStore = useGlobalStore();
        userId = globalStore.userInfo?.user_id;
        if (!userId) {
          console.warn("RewardWalletStore: 用户未登录，无法获取奖金列表");
          return;
        }
      }

      const res = await getBonusWalletList({ user_id: userId });
      const data = res?.data || [];

      // 确保 bonus 字段为数字类型
      const normalizedData: WalletTaskItem[] = data.map((item: any) => ({
        ...item,
        bonus: Number(item.bonus) || 0,
        bet_target_value: Number(item.bet_target_value) || 0,
        recharge_target_value: Number(item.recharge_target_value) || 0,
        bet_num: Number(item.bet_num) || 0,
        expire_time: Number(item.expire_time) || 0,
      }));

      // 排序逻辑
      bonusList.value = [...normalizedData].sort((a, b) => {
        // 首先按task_status排序
        const statusDiff = STATUS_ORDER[a.task_status] - STATUS_ORDER[b.task_status];
        if (statusDiff !== 0) return statusDiff;

        // 对于状态3和1的任务，进一步按created_at倒序排列
        if ([1, 3].includes(a.task_status) && [1, 3].includes(b.task_status)) {
          const timeDiff = new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          if (timeDiff !== 0) return timeDiff;

          // 时间相同则按bonus倒序
          const bonusDiff = Number(b.bonus) - Number(a.bonus);
          if (bonusDiff !== 0) return bonusDiff;

          // bonus相同则按task_name正序
          return a.task_name.localeCompare(b.task_name);
        }
        // 其他状态任务保持原有顺序
        return 0;
      });

      rules.value = normalizedData.map((t) => t.task_rule).filter(Boolean);
      availableCount.value = normalizedData.filter(
        (item) => item.task_status === TaskStatus.COMPLETED
      ).length;

      // 首次显示引导弹窗
      if (hasAvailableBonus.value && !localStorage.getItem("bonusGuideViewed")) {
        showGuideDialog.value = true;
      }
      return res;
    } catch (error) {
      resetState();
      console.error("获取奖金列表失败:", error);
      throw error;
    } finally {
      loading.value = false;
      isLoadingBonusList.value = false;
    }
  };

  // 领取任务
  const receiveTask = async (bonus: WalletTaskItem) => {
    if (hasOngoingBonuses.value) {
      showToast("Please complete the ongoing task first");
      return;
    }

    try {
      const res = await receiveWalletTask({
        user_id: bonus.user_id,
        task_id: bonus.id,
      });
      if (res.status === 200) {
        const data = res.data?.[0] || {};

        // 更新任务状态
        const index = bonusList.value.findIndex((item) => item.id === bonus.id);
        if (index > -1) {
          bonusList.value[index] = {
            ...bonus,
            task_status: data.status,
            expire_time: data.expire_time,
          };
        }
      } else {
        showToast(res.msg || "request error");
      }
    } catch (error) {
      console.error("解锁任务失败:", error);
      throw error;
    }
  };

  // 领取奖励
  const claimReward = async (bonus: WalletTaskItem) => {
    if (claimingBonusId.value) return;

    try {
      claimingBonusId.value = bonus.id;
      const res = await receiveWalletReward({
        user_id: bonus.user_id,
        task_id: bonus.id,
      });
      // TODO code待确认
      if (
        res?.data?.code !== 200 &&
        res?.status !== 200 &&
        res?.data?.code !== 0 &&
        res?.code !== 0
      ) {
        showToast("Failed to claim the reward. Please try again later");
        return;
      }

      // 更新状态
      const index = bonusList.value.findIndex((item) => item.id === bonus.id);
      if (index > -1) {
        bonusList.value[index].task_status = TaskStatus.CLAIMED;
      }
      availableCount.value = Math.max(0, availableCount.value - 1);

      // 更新全局余额
      const globalStore = useGlobalStore();
      await globalStore.getBalance();

      return res;
    } catch (error) {
      console.error("领取奖金失败:", error);
      throw error;
    } finally {
      claimingBonusId.value = null;
    }
  };

  // 初始化数据
  const initData = async (userId?: string | number) => {
    try {
      const globalStore = useGlobalStore();
      await Promise.all([globalStore.getBalance(), fetchBonusList(userId)]);
    } catch (error) {
      console.error("初始化数据失败:", error);
      throw error;
    }
  };

  // 重置状态
  const resetState = () => {
    loading.value = true;
    bonusList.value = [];
    rules.value = [];
    availableCount.value = 0;
    claimingBonusId.value = null;
    showGuideDialog.value = false;
  };

  // 确认引导弹窗
  const confirmGuide = () => {
    showGuideDialog.value = false;
    localStorage.setItem("bonusGuideViewed", "true");
  };

  return {
    // 状态
    loading,
    bonusList,
    rules,
    availableCount,
    claimingBonusId,
    showGuideDialog,

    // 计算属性
    hasOngoingBonuses,
    hasAvailableBonus,
    hasContent,
    bonusTotal,
    availableBonusTotal,

    // 方法
    fetchBonusList,
    receiveTask,
    claimReward,
    initData,
    resetState,
    confirmGuide,
  };
});
