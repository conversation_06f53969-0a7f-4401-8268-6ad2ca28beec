// D-DIN 字体系列 - 使用 public 目录路径
@font-face {
  font-family: "D-DIN";
  src: url("/fonts/D-DIN/D-DIN.ttf") format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
  // unicode-range: U+0030-0039; /* 仅应用于数字 0-9 */
}

@font-face {
  font-family: "D-DIN";
  src: url("/fonts/D-DIN/D-DIN-Bold.ttf") format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "DINPro";
  src: url("/fonts/D-DIN/DINPro-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "DINPro";
  src: url("/fonts/D-DIN/DINPro-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

// Inter 字体系列 - 使用 public 目录路径
@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Thin-11.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-ExtraLight-6.otf") format("opentype");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Light-7.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Regular-9.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Medium-8.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-SemiBold-10.otf") format("opentype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Bold-4.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-ExtraBold-5.otf") format("opentype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("/fonts/Inter/Inter-Black-3.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

// 字体变量定义
:root {
  --font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-display: "D-DIN", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-family-mono: "DINPro", "Courier New", monospace;
}

// 字体工具类
.font-inter {
  font-family: var(--font-family-primary);
}

.font-din {
  font-family: var(--font-family-display);
}

.font-dinpro {
  font-family: var(--font-family-mono);
}

// 字重工具类
.font-thin {
  font-weight: 100;
}
.font-extralight {
  font-weight: 200;
}
.font-light {
  font-weight: 300;
}
.font-normal {
  font-weight: 400;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-black {
  font-weight: 900;
}
