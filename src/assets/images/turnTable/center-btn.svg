<svg width="107" height="107" viewBox="0 0 107 107" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_10030_11174)">
<g filter="url(#filter1_ii_10030_11174)">
<circle cx="53.2739" cy="53.1516" r="37.7145" transform="rotate(49.7708 53.2739 53.1516)" fill="url(#paint0_linear_10030_11174)"/>
</g>
<mask id="mask0_10030_11174" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="15" y="15" width="76" height="76">
<circle cx="53.2739" cy="53.1516" r="37.7145" transform="rotate(49.7708 53.2739 53.1516)" fill="url(#paint1_linear_10030_11174)"/>
</mask>
<g mask="url(#mask0_10030_11174)">
<g filter="url(#filter2_f_10030_11174)">
<ellipse cx="83.6533" cy="65.9586" rx="27.4064" ry="18.4248" transform="rotate(119.764 83.6533 65.9586)" fill="#FA00FF"/>
</g>
<g filter="url(#filter3_f_10030_11174)">
<ellipse cx="74.8022" cy="26.2832" rx="24.0081" ry="11.644" transform="rotate(49.7708 74.8022 26.2832)" fill="#B0F559"/>
</g>
<g filter="url(#filter4_f_10030_11174)">
<ellipse cx="28.505" cy="31.341" rx="22.5179" ry="15.3036" transform="rotate(122.882 28.505 31.341)" fill="#FF3250"/>
</g>
<g filter="url(#filter5_f_10030_11174)">
<ellipse cx="34.4186" cy="81.0197" rx="32.0135" ry="19.2655" transform="rotate(33.3433 34.4186 81.0197)" fill="#25D8FF"/>
</g>
</g>
<circle cx="53.273" cy="53.1516" r="34.5942" fill="url(#paint2_linear_10030_11174)"/>
<circle cx="53.2736" cy="53.1515" r="27.1545" fill="#700B2A"/>
<g filter="url(#filter6_ii_10030_11174)">
<circle cx="53.2732" cy="53.1517" r="30.7503" fill="url(#paint3_linear_10030_11174)"/>
</g>
<circle cx="53.2732" cy="53.1517" r="30.3503" stroke="url(#paint4_linear_10030_11174)" stroke-width="0.8"/>
<circle cx="53.2733" cy="51.0677" r="24.3441" fill="url(#paint5_linear_10030_11174)"/>
</g>
<g filter="url(#filter7_dddddd_10030_11174)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.2295 41.602C33.1056 42.5137 32.5595 43.6374 32.5911 44.973C32.6063 45.6147 32.7776 46.2528 33.105 46.8872C33.4493 47.5038 34.155 48.3549 35.222 49.4404C36.3963 50.6621 37.1744 51.6416 37.5563 52.3789C37.9382 53.1161 38.1388 53.8923 38.1581 54.7075C38.1831 55.7656 37.9255 56.6134 37.3852 57.251C36.845 57.8885 36.1239 58.218 35.2219 58.2393C34.4587 58.2574 33.8461 58.0376 33.384 57.5799C32.922 57.1223 32.6823 56.5292 32.6651 55.8007C32.647 55.0375 32.9104 54.4325 33.4552 53.9858C34.0173 53.5386 34.7494 53.3043 35.6513 53.283C35.6039 52.7461 35.3686 52.3352 34.9453 52.0502C34.5216 51.7478 33.9802 51.6044 33.3211 51.62C32.263 51.645 31.3705 52.0653 30.6436 52.8808C29.9166 53.6964 29.5679 54.7286 29.5974 55.9774C29.6311 57.3997 30.1515 58.4895 31.1586 59.2466C32.1658 60.0038 33.5193 60.3622 35.2191 60.322C37.3526 60.2715 39.0905 59.6403 40.4329 58.4284C41.7752 57.2165 42.423 55.6219 42.3762 53.6445C42.3508 52.5691 42.111 51.6029 41.6568 50.7459C41.2023 49.8716 40.2757 48.8522 38.8771 47.6878C37.8146 46.7931 37.1209 46.0806 36.796 45.5503C36.4707 45.0026 36.3008 44.4253 36.2864 43.8182C36.2688 43.0723 36.4632 42.4864 36.8696 42.0602C37.2934 41.6337 37.8782 41.4116 38.6241 41.3939C39.2485 41.3791 39.7292 41.524 40.0661 41.8284C40.4027 42.1155 40.5775 42.5365 40.5906 43.0916C40.6013 43.5425 40.4988 43.9788 40.2832 44.4005C40.0849 44.8217 39.8147 45.1405 39.4728 45.3568C39.6334 45.5439 39.8101 45.6786 40.003 45.7608C40.2132 45.8426 40.4397 45.8806 40.6825 45.8749C41.3243 45.8597 41.8302 45.6047 42.2003 45.11C42.5878 44.6149 42.7723 43.9771 42.7538 43.1966C42.73 42.1906 42.3388 41.4275 41.5803 40.9075C40.839 40.387 39.8093 40.1424 38.4911 40.1735C36.7912 40.2138 35.3707 40.6899 34.2295 41.602ZM61.5447 44.1317C61.9515 43.7229 62.1482 43.2323 62.1347 42.6599C62.1211 42.0875 61.9015 41.6068 61.4757 41.2177C61.067 40.8108 60.5764 40.6142 60.004 40.6277C59.4316 40.6413 58.9508 40.8609 58.5617 41.2866C58.1722 41.695 57.9843 42.1854 57.9978 42.7578C58.0114 43.3302 58.2223 43.8111 58.6307 44.2006C59.0391 44.5901 59.5295 44.7781 60.1019 44.7646C60.6742 44.751 61.1552 44.5401 61.5447 44.1317ZM55.8538 55.5645C55.8257 55.8428 55.8143 56.0947 55.8196 56.3202C55.843 57.3089 56.1041 58.075 56.6029 58.6186C57.1186 59.1444 57.8101 59.397 58.6774 59.3765C59.8916 59.3477 60.9573 58.9147 61.8747 58.0773C61.9441 58.0139 62.0125 57.9483 62.0796 57.8806L61.8426 59.1454L65.5892 59.0567L67.1608 50.6631C67.3686 49.9119 67.6411 49.3241 67.9781 48.8996C68.3321 48.4573 68.7173 48.2313 69.1336 48.2214C69.4458 48.214 69.6826 48.3212 69.844 48.543C70.0055 48.7648 70.0913 49.0926 70.1016 49.5262C70.1086 49.8211 70.0825 50.1861 70.0234 50.6214C69.9643 51.0567 69.825 51.7715 69.6055 52.7659C69.3869 53.7951 69.2398 54.5448 69.1641 55.0152C69.1058 55.4851 69.0804 55.8762 69.0878 56.1885C69.1091 57.0904 69.395 57.8039 69.9454 58.3289C70.4954 58.8365 71.2561 59.0788 72.2275 59.0558C73.3202 59.03 74.2474 58.6089 75.0091 57.7925C75.7881 56.9758 76.436 55.7543 76.9529 54.128L75.8601 54.1539C75.4834 55.1 75.1424 55.7241 74.8371 56.0264C74.5319 56.3286 74.1624 56.4849 73.7288 56.4952C73.3819 56.5034 73.1197 56.4228 72.9421 56.2534C72.7641 56.0668 72.6709 55.7913 72.6622 55.427C72.6565 55.1842 72.684 54.8798 72.7448 54.5139C72.8055 54.1481 72.9382 53.5201 73.1427 52.6302C73.3936 51.4962 73.556 50.6593 73.63 50.1195C73.721 49.562 73.7617 49.0838 73.7523 48.6849C73.7297 47.7309 73.4609 47.0083 72.946 46.5172C72.4311 46.0261 71.714 45.7914 70.7947 45.8132C70.205 45.8271 69.6615 45.9615 69.1642 46.2162C68.6669 46.4709 68.2072 46.8463 67.7851 47.3422L68.0393 45.9825L64.2927 46.0711L62.5388 55.4302C62.3893 55.6965 62.2135 55.9356 62.0112 56.1477C61.5875 56.5742 61.1327 56.7932 60.6471 56.8047C60.2655 56.8137 59.9863 56.7509 59.8096 56.6163C59.6498 56.4639 59.5658 56.2142 59.5576 55.8673C59.5543 55.7285 59.5599 55.5982 59.5743 55.4764C59.5884 55.3372 59.6109 55.1892 59.6419 55.0323L61.3059 46.1418L57.5593 46.2305L55.9877 54.6241C55.9261 54.9553 55.8815 55.2688 55.8538 55.5645ZM47.0929 56.787C47.1668 56.9762 47.2917 57.1207 47.4677 57.2207C47.6436 57.3207 47.8529 57.3678 48.0958 57.362C49.1885 57.3362 50.0478 56.613 50.6737 55.1924C51.3169 53.7714 51.6166 52.133 51.5727 50.277C51.5559 49.5659 51.4391 49.0306 51.2223 48.6713C51.0055 48.312 50.6717 48.1377 50.2207 48.1483C49.9085 48.1557 49.5905 48.2847 49.2666 48.5354C48.9424 48.7687 48.6721 49.0788 48.4556 49.4657L47.0929 56.787ZM45.5195 65.1026L41.6129 66.1322L45.4734 45.5533L49.22 45.4646L48.8499 47.4258C49.2749 47.0513 49.7367 46.7627 50.2352 46.56C50.7337 46.3573 51.2778 46.249 51.8675 46.235C52.9082 46.2104 53.7057 46.5386 54.2598 47.2197C54.8139 47.9008 55.107 48.9178 55.139 50.2708C55.2018 52.9246 54.6813 55.1323 53.5776 56.8939C52.4911 58.6551 50.9506 59.5593 48.9559 59.6065C48.4008 59.6197 47.9307 59.5527 47.5454 59.4056C47.1774 59.2581 46.903 59.0303 46.7222 58.7222L45.5195 65.1026Z" fill="url(#paint6_linear_10030_11174)"/>
</g>
<defs>
<filter id="filter0_d_10030_11174" x="10.5586" y="10.4363" width="85.4307" height="85.4307" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.366734 0 0 0 0 0.231932 0 0 0 0 0.00489736 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10030_11174"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10030_11174" result="shape"/>
</filter>
<filter id="filter1_ii_10030_11174" x="15.5586" y="14.9363" width="75.4307" height="76.4307" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.926063 0 0 0 0 0.657427 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.9515 0 0 0 0 0.589619 0 0 0 0.78 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_10030_11174" result="effect2_innerShadow_10030_11174"/>
</filter>
<filter id="filter2_f_10030_11174" x="45.6548" y="23.4631" width="75.9971" height="84.991" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8.5" result="effect1_foregroundBlur_10030_11174"/>
</filter>
<filter id="filter3_f_10030_11174" x="42.9258" y="-7.53418" width="63.7524" height="67.6348" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7" result="effect1_foregroundBlur_10030_11174"/>
</filter>
<filter id="filter4_f_10030_11174" x="-3.23291" y="-3.31934" width="63.4756" height="69.3206" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="7" result="effect1_foregroundBlur_10030_11174"/>
</filter>
<filter id="filter5_f_10030_11174" x="-12.353" y="39.1731" width="93.5435" height="83.6934" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="9" result="effect1_foregroundBlur_10030_11174"/>
</filter>
<filter id="filter6_ii_10030_11174" x="22.5229" y="15.4014" width="61.5005" height="69.0005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.928107 0 0 0 0 0.949618 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-7"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.677696 0 0 0 0 0.0471441 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_10030_11174" result="effect2_innerShadow_10030_11174"/>
</filter>
<filter id="filter7_dddddd_10030_11174" x="29.5957" y="40.1709" width="47.3574" height="27.1612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_10030_11174" result="effect2_dropShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_10030_11174" result="effect3_dropShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_10030_11174" result="effect4_dropShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.54902 0 0 0 0 0.0117647 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_10030_11174" result="effect5_dropShadow_10030_11174"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.547241 0 0 0 0 0.0109448 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect5_dropShadow_10030_11174" result="effect6_dropShadow_10030_11174"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect6_dropShadow_10030_11174" result="shape"/>
</filter>
<linearGradient id="paint0_linear_10030_11174" x1="53.2739" y1="15.4371" x2="53.2739" y2="90.8661" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDF3AA"/>
<stop offset="0.54" stop-color="#FFA800"/>
<stop offset="1" stop-color="#E8CE61"/>
</linearGradient>
<linearGradient id="paint1_linear_10030_11174" x1="53.2739" y1="15.4371" x2="53.2739" y2="90.8661" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDF3AA"/>
<stop offset="0.54" stop-color="#FFA800"/>
<stop offset="1" stop-color="#E8CE61"/>
</linearGradient>
<linearGradient id="paint2_linear_10030_11174" x1="83.9655" y1="76.631" x2="30.5367" y2="29.1688" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDF3AA"/>
<stop offset="0.54" stop-color="#FFA800"/>
<stop offset="1" stop-color="#FDF3AA"/>
</linearGradient>
<linearGradient id="paint3_linear_10030_11174" x1="53.2732" y1="24.4204" x2="53.2732" y2="80.1832" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA552"/>
<stop offset="0.22" stop-color="#F74E0E"/>
<stop offset="1" stop-color="#E80003"/>
</linearGradient>
<linearGradient id="paint4_linear_10030_11174" x1="53.2732" y1="22.4014" x2="53.2732" y2="83.902" gradientUnits="userSpaceOnUse">
<stop stop-color="#D60E8F" stop-opacity="0.31"/>
<stop offset="1" stop-color="#400000"/>
</linearGradient>
<linearGradient id="paint5_linear_10030_11174" x1="53.2733" y1="26.7236" x2="53.2733" y2="48.9016" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.47"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_10030_11174" x1="76.0401" y1="53.231" x2="28.6503" y2="54.3524" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFCE1E"/>
<stop offset="0.53" stop-color="#FFE9D3"/>
<stop offset="1" stop-color="#FFCE1E"/>
</linearGradient>
</defs>
</svg>
