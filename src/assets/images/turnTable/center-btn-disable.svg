<svg width="107" height="107" viewBox="0 0 107 107" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_10030_11248)">
<g filter="url(#filter1_ii_10030_11248)">
<circle cx="53.1543" cy="53.1516" r="37.7145" transform="rotate(49.7708 53.1543 53.1516)" fill="url(#paint0_linear_10030_11248)"/>
<circle cx="53.1543" cy="53.1516" r="37.7145" transform="rotate(49.7708 53.1543 53.1516)" fill="url(#paint1_linear_10030_11248)"/>
</g>
<circle cx="53.1533" cy="53.1516" r="34.5942" fill="url(#paint2_linear_10030_11248)"/>
<circle cx="53.154" cy="53.1515" r="27.1545" fill="#700B2A"/>
<g filter="url(#filter2_ii_10030_11248)">
<circle cx="53.1536" cy="53.1517" r="30.7503" fill="url(#paint3_linear_10030_11248)"/>
</g>
<circle cx="53.1536" cy="53.1517" r="30.3503" stroke="url(#paint4_linear_10030_11248)" stroke-width="0.8"/>
<circle cx="53.1536" cy="51.0677" r="24.3441" fill="url(#paint5_linear_10030_11248)"/>
</g>
<g filter="url(#filter3_dddddd_10030_11248)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.1099 41.602C32.986 42.5137 32.4399 43.6374 32.4715 44.973C32.4867 45.6147 32.658 46.2528 32.9854 46.8872C33.3297 47.5038 34.0354 48.3549 35.1023 49.4404C36.2767 50.6621 37.0548 51.6416 37.4367 52.3789C37.8186 53.1161 38.0192 53.8923 38.0385 54.7075C38.0635 55.7656 37.8059 56.6134 37.2656 57.251C36.7253 57.8885 36.0042 58.218 35.1023 58.2393C34.3391 58.2574 33.7265 58.0376 33.2644 57.5799C32.8023 57.1223 32.5627 56.5292 32.5455 55.8007C32.5274 55.0375 32.7908 54.4325 33.3356 53.9858C33.8977 53.5386 34.6297 53.3043 35.5317 53.283C35.4843 52.7461 35.2489 52.3352 34.8257 52.0502C34.402 51.7478 33.8606 51.6044 33.2015 51.62C32.1434 51.645 31.2509 52.0653 30.5239 52.8808C29.797 53.6964 29.4483 54.7286 29.4778 55.9774C29.5115 57.3997 30.0319 58.4895 31.039 59.2466C32.0462 60.0038 33.3997 60.3622 35.0995 60.322C37.233 60.2715 38.9709 59.6403 40.3132 58.4284C41.6556 57.2165 42.3034 55.6219 42.2566 53.6445C42.2312 52.5691 41.9914 51.6029 41.5372 50.7459C41.0827 49.8716 40.1561 48.8522 38.7575 47.6878C37.695 46.7931 37.0013 46.0806 36.6764 45.5503C36.351 45.0026 36.1812 44.4253 36.1668 43.8182C36.1492 43.0723 36.3436 42.4864 36.75 42.0602C37.1738 41.6337 37.7586 41.4116 38.5044 41.3939C39.1289 41.3791 39.6095 41.524 39.9465 41.8284C40.283 42.1155 40.4579 42.5365 40.471 43.0916C40.4817 43.5425 40.3792 43.9788 40.1636 44.4005C39.9653 44.8217 39.6951 45.1405 39.3531 45.3568C39.5138 45.5439 39.6905 45.6786 39.8833 45.7608C40.0935 45.8426 40.3201 45.8806 40.5629 45.8749C41.2047 45.8597 41.7106 45.6047 42.0807 45.11C42.4682 44.6149 42.6527 43.9771 42.6342 43.1966C42.6104 42.1906 42.2192 41.4275 41.4606 40.9075C40.7194 40.387 39.6897 40.1424 38.3714 40.1735C36.6716 40.2138 35.2511 40.6899 34.1099 41.602ZM61.4251 44.1317C61.8319 43.7229 62.0286 43.2323 62.015 42.6599C62.0015 42.0875 61.7818 41.6068 61.3561 41.2177C60.9473 40.8108 60.4567 40.6142 59.8843 40.6277C59.3119 40.6413 58.8312 40.8609 58.4421 41.2866C58.0526 41.695 57.8646 42.1854 57.8782 42.7578C57.8917 43.3302 58.1027 43.8111 58.5111 44.2006C58.9194 44.5901 59.4098 44.7781 59.9822 44.7646C60.5546 44.751 61.0356 44.5401 61.4251 44.1317ZM55.7342 55.5645C55.706 55.8428 55.6946 56.0947 55.7 56.3202C55.7234 57.3089 55.9845 58.075 56.4833 58.6186C56.999 59.1444 57.6905 59.397 58.5578 59.3765C59.7719 59.3477 60.8377 58.9147 61.755 58.0773C61.8245 58.0139 61.8928 57.9483 61.96 57.8806L61.723 59.1454L65.4696 59.0567L67.0412 50.6631C67.249 49.9119 67.5215 49.3241 67.8585 48.8996C68.2125 48.4573 68.5976 48.2313 69.0139 48.2214C69.3261 48.214 69.563 48.3212 69.7244 48.543C69.8859 48.7648 69.9717 49.0926 69.982 49.5262C69.9889 49.8211 69.9629 50.1861 69.9037 50.6214C69.8446 51.0567 69.7053 51.7715 69.4859 52.7659C69.2673 53.7951 69.1202 54.5448 69.0445 55.0152C68.9862 55.4851 68.9608 55.8762 68.9681 56.1885C68.9895 57.0904 69.2754 57.8039 69.8258 58.3289C70.3758 58.8365 71.1365 59.0788 72.1078 59.0558C73.2006 59.03 74.1278 58.6089 74.8894 57.7925C75.6684 56.9758 76.3164 55.7543 76.8333 54.128L75.7405 54.1539C75.3637 55.1 75.0227 55.7241 74.7175 56.0264C74.4122 56.3286 74.0428 56.4849 73.6092 56.4952C73.2623 56.5034 73 56.4228 72.8225 56.2534C72.6445 56.0668 72.5512 55.7913 72.5426 55.427C72.5369 55.1842 72.5644 54.8798 72.6251 54.5139C72.6859 54.1481 72.8186 53.5201 73.0231 52.6302C73.274 51.4962 73.4364 50.6593 73.5104 50.1195C73.6013 49.562 73.6421 49.0838 73.6326 48.6849C73.6101 47.7309 73.3413 47.0083 72.8264 46.5172C72.3115 46.0261 71.5944 45.7914 70.6751 45.8132C70.0853 45.8271 69.5418 45.9615 69.0446 46.2162C68.5473 46.4709 68.0876 46.8463 67.6655 47.3422L67.9196 45.9825L64.1731 46.0711L62.4192 55.4302C62.2697 55.6965 62.0939 55.9356 61.8916 56.1477C61.4678 56.5742 61.0131 56.7932 60.5274 56.8047C60.1458 56.8137 59.8667 56.7509 59.6899 56.6163C59.5301 56.4639 59.4461 56.2142 59.4379 55.8673C59.4346 55.7285 59.4402 55.5982 59.4547 55.4764C59.4688 55.3372 59.4913 55.1892 59.5223 55.0323L61.1863 46.1418L57.4397 46.2305L55.8681 54.6241C55.8065 54.9553 55.7619 55.2688 55.7342 55.5645ZM46.9733 56.787C47.0472 56.9762 47.1721 57.1207 47.348 57.2207C47.5239 57.3207 47.7333 57.3678 47.9761 57.362C49.0689 57.3362 49.9282 56.613 50.5541 55.1924C51.1973 53.7714 51.497 52.133 51.4531 50.277C51.4362 49.5659 51.3194 49.0306 51.1027 48.6713C50.8859 48.312 50.552 48.1377 50.1011 48.1483C49.7888 48.1557 49.4708 48.2847 49.147 48.5354C48.8228 48.7687 48.5524 49.0788 48.336 49.4657L46.9733 56.787ZM45.3999 65.1026L41.4933 66.1322L45.3538 45.5533L49.1004 45.4646L48.7303 47.4258C49.1553 47.0513 49.617 46.7627 50.1155 46.56C50.614 46.3573 51.1582 46.249 51.7479 46.235C52.7886 46.2104 53.586 46.5386 54.1402 47.2197C54.6943 47.9008 54.9873 48.9178 55.0194 50.2708C55.0821 52.9246 54.5617 55.1323 53.4579 56.8939C52.3715 58.6551 50.831 59.5593 48.8363 59.6065C48.2812 59.6197 47.811 59.5527 47.4258 59.4056C47.0578 59.2581 46.7834 59.0303 46.6026 58.7222L45.3999 65.1026Z" fill="url(#paint6_linear_10030_11248)"/>
</g>
<defs>
<filter id="filter0_d_10030_11248" x="10.439" y="10.4363" width="85.4307" height="85.4307" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.366734 0 0 0 0 0.231932 0 0 0 0 0.00489736 0 0 0 0.66 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10030_11248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_10030_11248" result="shape"/>
</filter>
<filter id="filter1_ii_10030_11248" x="15.439" y="14.9363" width="75.4307" height="76.4307" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.78 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_10030_11248" result="effect2_innerShadow_10030_11248"/>
</filter>
<filter id="filter2_ii_10030_11248" x="22.4033" y="19.4014" width="61.5005" height="65.0005" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.5"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.928107 0 0 0 0 0.949618 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.19146 0 0 0 0 0.19146 0 0 0 0 0.19146 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_10030_11248" result="effect2_innerShadow_10030_11248"/>
</filter>
<filter id="filter3_dddddd_10030_11248" x="29.4761" y="40.1709" width="47.3574" height="27.1612" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_10030_11248" result="effect2_dropShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_10030_11248" result="effect3_dropShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.8"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_10030_11248" result="effect4_dropShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 0 0.258824 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_10030_11248" result="effect5_dropShadow_10030_11248"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.257012 0 0 0 0 0.257012 0 0 0 0 0.257012 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect5_dropShadow_10030_11248" result="effect6_dropShadow_10030_11248"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect6_dropShadow_10030_11248" result="shape"/>
</filter>
<linearGradient id="paint0_linear_10030_11248" x1="53.1543" y1="15.4371" x2="53.1543" y2="90.8661" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDF3AA"/>
<stop offset="0.54" stop-color="#FFA800"/>
<stop offset="1" stop-color="#E8CE61"/>
</linearGradient>
<linearGradient id="paint1_linear_10030_11248" x1="53.1543" y1="15.4371" x2="53.1543" y2="90.8661" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDEDED"/>
<stop offset="0.54" stop-color="#AAAAAA"/>
<stop offset="1" stop-color="#EDEDED"/>
</linearGradient>
<linearGradient id="paint2_linear_10030_11248" x1="87.7476" y1="53.1516" x2="18.5591" y2="53.1516" gradientUnits="userSpaceOnUse">
<stop stop-color="#EDEDED"/>
<stop offset="0.54" stop-color="#B5B5B5"/>
<stop offset="1" stop-color="#EDEDED"/>
</linearGradient>
<linearGradient id="paint3_linear_10030_11248" x1="53.1536" y1="24.4204" x2="53.1536" y2="80.1832" gradientUnits="userSpaceOnUse">
<stop stop-color="#787878"/>
<stop offset="0.544379" stop-color="#B3B3B3"/>
<stop offset="1" stop-color="#424242"/>
</linearGradient>
<linearGradient id="paint4_linear_10030_11248" x1="53.1536" y1="22.4014" x2="53.1536" y2="83.902" gradientUnits="userSpaceOnUse">
<stop stop-color="#8D8D8D" stop-opacity="0.31"/>
<stop offset="1" stop-color="#4C4C4C"/>
</linearGradient>
<linearGradient id="paint5_linear_10030_11248" x1="53.1537" y1="26.7236" x2="53.1536" y2="48.9016" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.47"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_10030_11248" x1="54.4624" y1="47.5963" x2="57.7009" y2="54.5182" gradientUnits="userSpaceOnUse">
<stop stop-color="#FDFDFD"/>
<stop offset="1" stop-color="#D5D5D5"/>
</linearGradient>
</defs>
</svg>
