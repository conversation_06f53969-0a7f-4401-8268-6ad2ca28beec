/* ------------ 基础 Reset ------------ */
/* 1. 清除默认边距，设置盒模型 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* 盒模型：width/height 包含 padding 和 border，更易布局 */
}

/* 2. 处理行内元素空格问题（可选，根据布局需求） */
/* 若需精确控制 inline-block 间距，可添加 */
/* font-size: 0; 配合子元素重置 font-size */
/* 示例：.parent { font-size: 0; } .child { font-size: 16px; } */

/* 3. 移除列表默认样式 */
ul,
ol {
  list-style: none;
  /* 清除列表项的默认圆点、数字 */
}

/* 4. 规范图片、视频等替换元素行为 */
img,
picture,
video,
canvas,
svg {
  display: block;
  /* 转为块级元素，避免底部默认空白 */
  max-width: 100%;
  /* 自适应容器宽度，防止溢出 */
  height: auto;
  /* 保持宽高比，避免拉伸变形 */
}

/* 5. 重置表格样式 */
table {
  border-collapse: collapse;
  /* 合并单元格边框，避免默认间距 */
  border-spacing: 0;
  /* 清除单元格间距 */
  width: 100%;
  /* 可选：让表格占满容器 */
}

/* 6. 重置表单元素 */
input,
button,
textarea,
select {
  font: inherit;
  /* 继承父级字体样式，统一表单与页面字体 */
  color: inherit;
  /* 继承父级文字颜色 */
  background-color: transparent;
  /* 清除默认背景，如需自定义可覆盖 */
  border: none;
  /* 清除默认边框，如需自定义可覆盖 */
  padding: 0;
  /* 清除默认内边距 */
  margin: 0;
  /* 清除默认外边距 */
  outline: none;
  /* 清除默认聚焦轮廓，建议自定义聚焦样式 */
  appearance: none;
  /* 清除浏览器默认外观（如 Safari 下的 select 样式） */
}

/* 7. 重置链接样式 */
a {
  text-decoration: none;
  /* 清除默认下划线，如需可在 hover 时添加 */
  color: inherit;
  /* 继承父级文字颜色 */
}

/* 8. 重置标题、段落等文本元素 */
h1,
h2,
h3,
h4,
h5,
h6,
p {
  font-weight: normal;
  /* 清除默认加粗，如需可自定义 */
  font-size: inherit;
  /* 继承父级字体大小 */
}

/* 9. 清除默认浮动影响（现代布局多用 Flex/Grid，可按需保留） */
/* 若需兼容旧布局，可保留 clearfix */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 10. 统一全局字体（可选，根据项目设计） */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell,
    "Open Sans", "Helvetica Neue", sans-serif;
  /* 系统字体 fallback，保证多设备一致性 */
  line-height: 1.4;
  /* 基础行高，可根据设计调整 */
  color: #333;
  /* 基础文字颜色，可根据设计调整 */
  background-color: #fff;
  /* 基础背景色，可根据设计调整 */
}

/* ------------ 可选扩展：自定义聚焦样式 ------------ */
/* 替代浏览器默认 outline，提升可访问性 */
:focus {
  outline: 2px solid #007bff; /* 示例：蓝色聚焦轮廓 */
  outline-offset: 2px; /* 轮廓与元素的间距 */
}

/* ------------ 可选扩展：打印样式优化（按需添加） ------------ */
@media print {
  body {
    font-size: 12pt; /* 打印时基础字体大小 */
    color: #000; /* 打印时文字颜色 */
  }
  /* 隐藏非必要元素 */
  .no-print {
    display: none;
  }
}
