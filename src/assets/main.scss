/* src/assets/styles/global.css */
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 确保内容容器继承宽度 */
#app {
  width: 100%;
  min-height: 100vh;
}

/* 极验logo */
.geetest_box_wrap .geetest_box .geetest_footer .geetest_footer_right {
  display: none;
}
/* van-switpe 组件 */
.van-swipe__indicator--active {
  width: 20px;
  height: 5px;
  flex-shrink: 0;
  border-radius: 10px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -ms-border-radius: 10px;
  -o-border-radius: 10px;
}

/* van-tabs 组件 覆盖tabs页签样式 -- 全局 */
.van-tabs {
  .van-tabs__content {
    height: calc(100vh - 100px);
  }

  .van-tabs__wrap {
    height: 35px;
    padding: 10px;
    box-sizing: content-box;
  }

  .van-tabs__nav {
    background-color: transparent;
    line-height: 35px;
    padding: 0;
    display: flex;
    gap: 8px;
    border: 0 !important;
  }

  .van-tab {
    border-radius: 999px;
    background: rgba(172, 17, 64, 0.05);
    padding: 8px 16px;
  }

  .van-tab--active {
    background-color: #ac1140;
    color: #fff;
  }
}

/* 图片弹窗，透明样式 */
.z-dialog-transparent {
  width: 100% !important;
  background-color: transparent !important;
}

.z-dialog-transparent .custom-content {
  padding: 0 !important;
  padding-bottom: 80px !important;
  padding-top: 100px !important;
}

.icon-paypwd:before,
.icon-loginpwd:before,
.icon-phone:before,
.icon-Logout:before,
.icon-kyc:before,
.icon-version:before {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  font-size: 24px;
}
.icon-version:before {
  color: #97acff;
}
.icon-qianjin:before {
  font-size: 12px;
}
.icon-xuanzhong1:before {
  color: var(--red-color);
}
.icon-orders_fill:before {
  color: #97acff;
}
.icon-Shield:before {
  color: #64dcb7;
}
.icon-qianbao:before {
  color: #ff8181;
}

.icon-loginpwd:before {
  color: #97acff;
  background-color: #97acff1a;
}

.icon-paypwd:before {
  color: #64dcb7;
  background-color: rgba(100, 220, 183, 0.1);
}

.icon-kyc:before {
  color: #ff8181;
  background-color: #ff81811a;
}

.icon-phone:before {
  color: #ffc471;
  background-color: #ffc4711a;
}

.icon-Logout:before {
  color: #ff8181;
  background-color: #ff81811a;
}

.icon-xinxi-quan:before {
  color: #5bceff;
}

.icon-huhuan:before {
  color: #ffb958;
}

.icon-card:before {
  color: #ff8181;
}
.icon-a-Group76:before {
  color: #64dcb7;
}
.icon-xuanzhong1:before {
  color: var(--red-color);
}

#app {
  max-width: 480px;
  margin: 0 auto;
  font-weight: normal;
}

.AIHelpSupportBox {
  width: 100%;
  height: 80%;
  position: fixed;
  bottom: 0rem;
  z-index: 999;
}
.AIHelpSupportClose {
  position: absolute;
  right: 5px;
  top: -45px;
  width: 40px;
  height: 40px;
  color: #fff;
  background: #f9c633;
  border-radius: 25px;
  cursor: pointer;
}

.AIHelpSupportClose:before {
  position: absolute;
  content: "";
  width: 24px;
  height: 2px;
  background: #000;
  transform: rotate(45deg);
  top: 19px;
  left: 8px;
}

.AIHelpSupportClose:after {
  content: "";
  position: absolute;
  width: 24px;
  height: 2px;
  background: #000;
  transform: rotate(-45deg);
  top: 19px;
  left: 8px;
}
/*
@media (max-width: 767px) {
  .AIHelpSupportBox {
    width: 100%;
    height: 80%;
    position: fixed;
    bottom: 0rem;
    z-index: 999;
  }
  .AIHelpSupportClose {
    position: absolute;
    right: 5px;
    top: -45px;
    width: 40px;
    height: 40px;
    color: #fff;
    background: #f9c633;
    border-radius: 25px;
    cursor: pointer;
  }

  .AIHelpSupportClose:before {
    position: absolute;
    content: "";
    width: 24px;
    height: 2px;
    background: #000;
    transform: rotate(45deg);
    top: 19px;
    left: 8px;
  }

  .AIHelpSupportClose:after {
    content: "";
    position: absolute;
    width: 24px;
    height: 2px;
    background: #000;
    transform: rotate(-45deg);
    top: 19px;
    left: 8px;
  }
} */
/*
@media (min-width: 768px) {
  .AIHelpSupportBox {
    width: 375px;
    height: 500px;
    position: fixed;
    right: 1rem;
    bottom: 4rem;
    z-index: 999;
  }
  .AIHelpSupportClose {
    position: absolute;
    right: -15px;
    top: -15px;
    width: 30px;
    height: 30px;
    color: #fff;
    background: #f9c633;
    border-radius: 25px;
    cursor: pointer;
  }

  .AIHelpSupportClose:before {
    position: absolute;
    content: "";
    width: 20px;
    height: 2px;
    background: #000;
    transform: rotate(45deg);
    top: 14px;
    left: 6px;
  }

  .AIHelpSupportClose:after {
    content: "";
    position: absolute;
    width: 20px;
    height: 2px;
    background: #000;
    transform: rotate(-45deg);
    top: 14px;
    left: 6px;
  }
} */
