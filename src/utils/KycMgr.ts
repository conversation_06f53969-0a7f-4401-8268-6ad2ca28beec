import { getUserStatus } from "@/api/user";
import { getToken } from "@/utils/auth";
import { useGlobalStore } from "@/stores/global";
import { useKycStore } from "@/stores/kyc";
import { getGlobalDialog } from "@/enter/vant";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import router from "@/router";

export enum InGameType {
  Login = 0, //登录场景
  Withdraw, //提现 验证
  MyCenter, //个人中心
  GoThirdGame, //打开第三方游戏
  UNKNOWN = -1, // 未知
}

export enum KycState {
  NO_VERIFY = 0, // 未验证
  COMPLETE = 1, // 已认证
  REVIEWING = 2, // 审核中
  REJECTED = 3, // 拒绝
  UNKNOWN = -1, // 未知
}

export class KycMgr {
  //状态0未认证 1已认证 2审核中 3拒绝 -1未读服务器
  public kycState: KycState = KycState.UNKNOWN;
  // 0简版 1完整版 -1未知
  public kycSimple: number = -1;
  public kycData: any = null;
  //拒绝信息
  public rejectMsg: string = "You are younger than 21.";
  //进入验证的 入口状态
  public inGameType: InGameType = InGameType.UNKNOWN;
  //用来通知 返回的结果
  private callback?: (state: boolean) => void;
  //是否有弹窗 正在显示用于自动弹窗功能
  public poping = false;
  //首页是否 添加过 自动弹窗 如果添加过 则不再添加
  public hallShowed = false;

  private static _instance: KycMgr;
  public static get instance() {
    if (!this._instance) this._instance = new KycMgr();
    return this._instance;
  }

  public clearData() {
    this.kycSimple = -1;
    this.kycState = KycState.UNKNOWN;
    this.poping = false;
    this.hallShowed = false;
    this.callback = undefined;
  }
  /**
   * 获取 KYC 状态
   */
  private async fetchKycStatus() {
    const token = getToken();
    if (!token) return;
    try {
      const res = await getUserStatus({});
      const data = res.data || res;
      this.kycData = data;
      this.kycState = Number(data.status);
      this.kycSimple = Number(data.is_full);
      this.rejectMsg = data.reject || this.rejectMsg;

      if (this.inGameType === InGameType.UNKNOWN) return data;
      if (this.kycState === KycState.COMPLETE) {
        // 审核完成
        this.backToTarget(true);
      } else if (this.kycState === KycState.REVIEWING) {
        // 审核中,提现场景，不关闭提现弹窗
        // this.backToTarget(false);
      } else if (this.kycState === KycState.NO_VERIFY) {
        //未完成kyc 提示弹窗
        this.noVerifyPop();
      } else if (this.kycState === KycState.REJECTED) {
        // 被拒绝 提示弹窗
        this.rejectPop();
      }
    } catch (e) {
      this.backToTarget(false);
    }
  }
  /**
   * 回调并清空
   */
  private backToTarget(state = false) {
    this.callback?.(state);
    this.callback = undefined;
  }

  /**
   * 验证手机号
   */
  public checkBindPhone() {
    const globalStore = useGlobalStore();

    const user = globalStore.userInfo;
    if (user?.phone) {
      // 如果 有手机号 直接开始kyc去除 提现密码设置
      this.openKycVerify();
    } else {
      // 手机号弹窗
      const kycStore = useKycStore();
      kycStore.showPhoneChangeDialog = true;
    }
  }

  /**
   * 打开 KYC 验证弹窗
   */
  private openKycVerify() {
    const globalStore = useGlobalStore();
    if (globalStore.channel == CHANEL_TYPE.MAYA) {
      return;
    }
    if (this.inGameType === InGameType.UNKNOWN) return;
    // 审核中或者 审核完成后 不再弹出
    if (this.kycState === KycState.COMPLETE || this.kycState === KycState.REVIEWING) return;
    // 跳转kyc 验证弹窗
    if (this.kycSimple === 0) {
      router.push(`/kyc/simple-form`);
    } else if (this.kycSimple === 1) {
      router.push(`/kyc/normal-form`);
    }
  }

  /**
   * 入口方法：校验 KYC
   */
  public async verifyKyc(inType: InGameType, callback?: (state: boolean) => void) {
    this.callback = callback;
    this.inGameType = inType;
    //kyc验证只针对web 端
    const globalStore = useGlobalStore();
    if (globalStore.channel !== CHANEL_TYPE.WEB) {
      this.backToTarget(true);
      return;
    }
    if (this.kycState === KycState.UNKNOWN) {
      const res = await this.fetchKycStatus();
      return res;
    }
    //未验证和拒绝状态 验证下一步
    if (this.kycState === KycState.NO_VERIFY) {
      this.noVerifyPop();
      if (this.kycSimple === 0) {
        if (inType !== InGameType.Withdraw) {
          this.backToTarget(true);
        } else {
          this.backToTarget(false);
        }
      }
      return this.kycData;
    }
    //未验证和拒绝状态 验证下一步
    if (this.kycState === KycState.REJECTED) {
      this.rejectPop();
      if (this.kycSimple === 0) {
        if (inType !== InGameType.Withdraw) {
          this.backToTarget(true);
        } else {
          this.backToTarget(false);
        }
      }
      return this.kycData;
    }
    //验证审核中状态
    if (this.kycState === KycState.REVIEWING) {
      switch (inType) {
        //继续游戏不弹窗
        case InGameType.Login:
        case InGameType.GoThirdGame:
          this.backToTarget(true);
          break;
        case InGameType.MyCenter:
          //继续下一步不弹窗这里是查询输入信息
          break;
        case InGameType.Withdraw:
          //提现 只在完整版 弹窗提示！
          if (this.kycSimple === 1) {
            this.backToTarget(false);
          }
          this.reviewPop();
          break;
      }
      return this.kycData;
    }
    // 其它情况直接通过, 进入第三方游戏和提现的时候用到
    this.backToTarget(true);
    return this.kycData;
  }
  /**
   * 完整版 审核中 弹窗提示！ 可关闭的弹窗 ,充值场景
   */
  private reviewPop() {
    this.poping = true;
    const $dialog = getGlobalDialog();
    // <div>We are reviewing your application.</div>
    $dialog({
      title: "KYC Verification",
      message: `<div style="margin-bottom:10px;text-align:center;color:#000">
      <div>Estimated review time: 1 day.</div>
      `,
      describe: `
      <div style="color:#666;text-align:center;"> Thank you for providing the required information. After the review is completed, we will notify you via an internal message.</div>
      `,
      confirmText: "Refresh",
      onConfirm: async () => {
        //  刷新一下
        await this.refreshKycState();
      },
      onCancel: () => {
        KycMgr.instance.poping = false;
      },
    });
  }

  /**
   * 审核拒绝弹窗
   */
  private rejectPop() {
    if (this.kycSimple === 0) {
      if (this.inGameType !== InGameType.Withdraw) {
        return;
      }
    }
    this.poping = true;
    const $dialog = getGlobalDialog();
    $dialog({
      title: "KYC Verification",
      message: `<div style="text-align:center">Review failed</div>`,
      describe: this.rejectMsg,
      onConfirm: async () => {
        //拒绝之后不用验证 输入手机号之类的 直接弹出kyc
        this.openKycVerify();
      },
      onCancel: () => {
        //中断继续 （hall 里面kyc验证 关闭之后 弹出新用户奖励场景）
        if (this.kycSimple === 0) {
          this.backToTarget(true);
        } else {
          this.backToTarget(false);
          this.logoutGame();
        }
      },
    });
  }

  /**
   * 未提交弹窗，首页使用组件替代了
   */
  private noVerifyPop() {
    //简版且不是提现场景， 弹窗不提示
    if (this.kycSimple === 0) {
      if (this.inGameType !== InGameType.Withdraw) {
        return;
      }
    }
    this.poping = true;
    const $dialog = getGlobalDialog();
    $dialog({
      title: "KYC Verification",
      message: `<div style="color:#222;text-align:center;">Your account is not yet fully verified</div>`,
      describe: `<div style="color:#999;text-align:center;">Your access to a certain service on the NUSTAR Online will be restricted.</div>`,
      confirmText: "Verify Now",
      onConfirm: async () => {
        //验证是否 绑定手机号
        this.checkBindPhone();
      },
      onCancel: () => {
        //中断继续 （hall 里面kyc验证 关闭之后 弹出新用户奖励场景）
        if (this.kycSimple === 0) {
          this.backToTarget(true);
        } else {
          this.backToTarget(false);
          //完整版 退出游戏登录页面
          this.logoutGame();
        }
      },
    });
  }

  /**
   * 刷新 KYC 状态
   */
  public async refreshKycState() {
    await this.fetchKycStatus();
  }

  /**
   * 退出游戏
   */
  private logoutGame() {
    //下一次进入游戏 弹出登陆页面
    window["isShowLoginPage"] = 1;
    const globalStore = useGlobalStore();
    globalStore.loginOut();
  }

  /**
   * 是否需要弹窗
   */
  public needShowPopKYC(): boolean {
    if (this.hallShowed) return false;
    let bPop = true;
    const globalStore = useGlobalStore();
    if (globalStore.channel !== CHANEL_TYPE.WEB) bPop = false;
    if (window["isShowLoginPage"] == 1) bPop = false;
    if (!globalStore.token) bPop = false;
    if (this.kycState === KycState.COMPLETE || this.kycState === KycState.REVIEWING) bPop = false;
    this.hallShowed = true;
    return bPop;
  }
}
