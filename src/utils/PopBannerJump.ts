import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import router from "@/router";
import { useGlobalStore } from "@/stores/global";
import { getGlobalDialog } from "@/enter/vant";
import { rebateConf } from "@/api/user";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";

export enum ACTIVITY_TYPE {
  SIGNBIND = 1,
  FIRSTDEPOSIT = 2,
  CASHBACK = 3,
  VIPCASHBACK = 4,
  RANK = 5,
  SPIN = 6,
  RANKJILI = 7,
  VALENTINE = 8,
  WEEKLY = 9,
  PP_DAILY_WINS = 10,
  LATE_NIGHT_CASHBACK = 11,
  FC_FREE_SPIN = 12,
  GET_EASTER_BONUS = 13,
  YELLOW_BAT = 14,
  JILI_FREE_SPIN = 15,
  FC_FREE_SPIN2 = 16,
  CASHBACK_DETIALS = 100,
  VIP_CASHBACK_DETIALS = 101,
}

export const popBannerJump = async (element) => {
  const jumptype = parseInt(element.activity_list);
  const globalStore = useGlobalStore();
  const $dialog = getGlobalDialog();
  switch (jumptype) {
    case ACTIVITY_TYPE.CASHBACK:
      //是否是vip用户
      let isVip;
      if (globalStore.userInfo.is_vip == 1) {
        isVip = 1;
      } else {
        isVip = 0;
      }
      let res;
      try {
        showZLoading();
        res = await rebateConf();
      } finally {
        closeZLoading();
      }
      if (isVip && res?.status) {
        $dialog({
          message: "You are a VIP member,and can participate in higher Cashback activities.",
          confirmText: "Click To Go",
          showClose: true,
          showCancelButton: false,
          onConfirm: async () => {
            router.push("/promos/promo_4");
          },
        });
      } else {
        router.push("/promos/promo_3");
      }
      break;
    case ACTIVITY_TYPE.SIGNBIND:
      router.push("/promos/promo_1");
      break;
    case ACTIVITY_TYPE.FIRSTDEPOSIT:
      router.push("/promos/promo_2");
      break;
    case ACTIVITY_TYPE.SPIN:
      const autoPopMgrStore = useAutoPopMgrStore();
      autoPopMgrStore.openSpinWheel();
      break;
    case ACTIVITY_TYPE.RANK:
      router.push("/promos/promo_5");
      break;
    case ACTIVITY_TYPE.RANKJILI:
      router.push("/promos/promo_7");
      break;
    case ACTIVITY_TYPE.VALENTINE:
      // 没写，弹出情人节活动
      break;
    case ACTIVITY_TYPE.WEEKLY:
      router.push("/promos/promo_9");
      break;
    case ACTIVITY_TYPE.PP_DAILY_WINS:
      router.push("/promos/promo_10");
      break;
    case ACTIVITY_TYPE.LATE_NIGHT_CASHBACK:
      router.push("/promos/promo_11");
      break;
    case ACTIVITY_TYPE.FC_FREE_SPIN:
      router.push("/promos/promo_12");
      break;
    case ACTIVITY_TYPE.YELLOW_BAT:
      router.push("/promos/promo_14");
      break;
    case ACTIVITY_TYPE.JILI_FREE_SPIN:
      router.push("/promos/promo_15");
      break;
    case ACTIVITY_TYPE.FC_FREE_SPIN2:
      router.push("/promos/promo_16");
      break;
    default:
      break;
  }
};
