import { CHANEL_TYPE } from "./GlobalConstant";
import { myOS, generateRandomId } from "@/utils/core/tools";
import { isBrowser } from "@/utils/core/tools";

export const ALL_APP_SOURCE_CONFIG = {
  channel: CHANEL_TYPE.WEB,
  appPackageName: "com.playmate.playzone",
  deviceModel: "WEB",
  deviceVersion: "WEB",
  sysTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  sysLanguage: navigator.language,
  source: myOS(),
  telephoneCode: "+63",
  registration_channel: CHANEL_TYPE.WEB,
  aisec_token: null,
  deviceId: generateRandomId(),
  appVersion: "1.1.20",
  app_id: 10000,
  isNative: !isBrowser ? 1 : 0,
  geetest_key: "0",
  app_bundle_id: "com.playmate.playzone",
  app_version: "*******", //这里是版本号 每次上线需要更改的地方
};

export const getEnvConfig = () => {
  return import.meta.env;
};

// (<any>window).GlobalSourceConfig = ALL_APP_SOURCE_CONFIG;

export const ENUMCONFIGS = [
  {
    type: "12",
    title: "First top-up bonus",
  },
  {
    type: "239",
    title: "First top-up bonus",
  },
  {
    type: "65",
    title: "Free Registration Bonus",
  },
  {
    type: "111",
    title: "Daily Cashback",
  },
  {
    type: "119",
    title: "VIP Cashback",
  },
  {
    type: "241",
    title: "VIP Cashback",
  },
  {
    type: "294",
    title: "Bind mobile phone",
  },
  {
    type: "265",
    title: "Sign up bonus",
  },
  {
    type: "310",
    title: "Weekly sign-in",
  },
  {
    type: "311",
    title: "Daily Cashback",
  },
  {
    type: "312",
    title: "Payday bonus",
  },
  {
    type: "313",
    title: "Sport first time rebate",
  },
  {
    type: "236",
    title: "Invite friends",
  },
  {
    type: "237",
    title: "Invite 5 peoples gift",
  },
  {
    type: "238",
    title: "Invitee gift",
  },
  {
    type: "242",
    title: "VIP Cashback",
  },
  {
    type: "243",
    title: "Jili Games 1.0% Cashback",
  },
  {
    type: "244",
    title: "Casino Elite Wealth Leaderboard",
  },
  {
    type: "245",
    title: "Get Easter Bonus",
  },
  {
    type: "246",
    title: "YB Extra 0.35% Cashback",
  },
  {
    type: "247",
    title: "Sports Loss Cashback",
  },
  {
    type: "248",
    title: "Sports Daily Bonus",
  },
  {
    type: "249",
    title: "NBA Champion Prediction",
  },
  {
    type: "400",
    title: "Spin Activity",
  },
  {
    type: "401",
    title: "JILI Slot Rank",
  },
  {
    type: "402",
    title: "Valentine",
  },
  {
    type: "403",
    title: "Weekly payday",
  },
  {
    type: "404",
    title: "Late Night Cashback",
  },
  {
    type: "10000",
    title: "Congratulations！\nWelcome to NUSTAR Online!",
  },
  {
    type: "10001",
    title: "Congratulations on successfully\nbinding your mobile phone number",
  },
];
