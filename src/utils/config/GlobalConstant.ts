export const MAINTENANCETIPCODE = 102121;
export const LOCK_ACCOUNT = 102008;

// 渠道ID
export const METHODS_ID = {
  GCash: 12,
  Maya: 11,
} as const;

// 渠道枚举
export enum CHANEL_TYPE {
  GOOGLE_PLAY = "GP",
  G_CASH = "GCash",
  H5 = "H5",
  WEB = "Web",
  MAYA = "Maya",
  IOS = "iOS", //增加iOS渠道
}

// 渠道ID 获取 渠道名称
export const METHODS_NAMES = {
  [METHODS_ID.GCash]: CHANEL_TYPE.G_CASH,
  [METHODS_ID.Maya]: CHANEL_TYPE.MAYA,
} as const;

// 渠道ID 获取渠道颜色
export const METHODS_COLORS = {
  [METHODS_ID.GCash]: "#4086f4",
  [METHODS_ID.Maya]: "#01d46a",
} as const;

export const getMethodsInfo = (methodId) => {
  switch (methodId) {
    case METHODS_ID.GCash:
      return {
        name: METHODS_NAMES[methodId],
        activeColor: METHODS_COLORS[methodId],
        unActiveColor: "#ccc",
      };
    case METHODS_ID.Maya:
      return {
        name: METHODS_NAMES[methodId],
        activeColor: METHODS_COLORS[methodId],
        unActiveColor: "#bbb",
      };
    default:
      // 处理未定义的渠道ID，返回默认值或抛出错误
      return {
        name: "Unknown",
        activeColor: "#000",
        unActiveColor: "#ccc",
      };
  }
};

export enum AWARD_UPDATE_TYPE {
  FIRST_RECHARGE = 12,
  FREE_REGISTRATION = 65,
  CASHBACK = 111,
  VIP_CASHBACK_119 = 119, // VIP Cashback //原来是119
  BING_PHONE = 294, // Bind mobile phone
  SIGN_UP_BONUS = 265, // Sign up bonus
  WEEKLY_SIGNIN = 310, // Weekly sign-in
  DAILY_BETTING = 311, // Daily betting rebate
  PAYDAY_BONUS = 312, // Payday bonus
  SPORT_FIRST_TIME = 313, // Sport first time rebate
  INVITE_FRIENDS = 236, // Invite friends
  INVITE_5GIFT = 237, // Invite 5 peoples gift
  INVITEE_GIFT = 238, // Invitee gift
  FIRST_DESPOSIT = 239, // First deposit bonus
  VIP_CASHBACK = 241, // VIP Cashback //原来是119
  SUPERACE_CASH = 242, //
  JILI_GAMES_CASHBACK = 243, //Jili Games cashback
  CASINO_LEADERBOARD = 244, //排行榜奖励
  GET_EASTER_BONUS = 245, //Get Easter Bonus
  YB_SLOT_CASHBACK = 246, //YB Slot Cashback
  SPORTS_LOSS_CASHBACK = 247, //Sports Loss Cashback
  SPORTS_DAILY_BONUS = 248, //Sports Daily Bonus
  NBA_CHAMPION_PREDICTION = 249, //NBA Champion Prediction
  SPIN_ACTIVITY = 400,
  JILI_LEADERBOARD = 401, //jili排行榜奖励
  Valentine = 402, //情人节活动
  WEEKLY_PAYDAY = 403, //WeeklyPayday活动
  LATE_NIGHT_CASHBACK = 404, //Late Night Cashback

  REGISTER_USER = 10000, //新用户注册 奖励30
  BING_IPHONE_USER = 10001, //新用户绑定 原来这三个活动是系统弹窗改变过来
}

export const E_THIRD_PROVIDER = {
  JILI: "JILI",
  PG: "PG",
  EVO: "EVO",
  RTG: "Realtime Gaming",
  FC: "FaChai",
  Galaxsys: "Galaxsys",
  Pinnacle: "Pinnacle",
  BetConstruct: "Bet Construct",
  SAGaming: "SA Gaming",
  PP: "Pragmatic Play",
  JDB: "JDB",
  PM: "PM",
};
// 支付参数
export const METHODS = {
  GCASH: "gcash",
  GCASH_WEB: "gcashwebpay",
  GOOGLE_PAY: "googlepay",
  MAYA_PAY: "mayapay",
  PAYCOOLS: "paycools",
  BANK_CARD: "paycoolsbank",
  MAYA_WEB: "mayawebpay",
};

export const PAY_METHOD = {
  MAYA_WEB: "9",
  GCASH_WEB: "10",
};

export enum E_FUND_TYPE {
  Gcash = 3,
  Maya = 4,
  GrabPay = 5,
  BankCard = 6,
  MAYA_MINI = 7,
  MAYA_WEB = 11,
  GCASH_WEB = 12,
}

export enum PRODUCT_TYPE {
  ITEM = 1,
  CASH = 2,
  GCASH = 3,
  MAYA = 4,
  GRAB_PAY = 5,
  BANK_CARD = 6,
  MAYA_MINI = 7,
  MAYA_WEB = 9, //Maya充值
  GCASH_WEB = 10, //GCash充值
  WITHDRAW_MAYA_WEB = 11, //Maya提现
  WITHDRAW_GCASH_WEB = 12, //GCash提现
}

export const AWARD_NAME = {
  FREE_REGISTRATION: "Free Registration Bonus",
  CASHBACK: "Daily Cashback",
  BING_PHONE: "Bind mobile phone",
  SIGN_UP_BONUS: "Sign up bonus",
  WEEKLY_SIGNIN: "Weekly sign-in",
  DAILY_BETTING: "Daily Cashback",
  PAYDAY_BONUS: "Payday bonus",
  SPORT_FIRST_TIME: "Sport first time rebate",
  INVITE_FRIENDS: "Invite friends",
  INVITE_5GIFT: "Invite 5 peoples gift",
  INVITEE_GIFT: "Invitee gift",
  FIRST_DESPOSIT: "First deposit bonus",
  VIP_CASHBACK: "VIP Cashback", //原来是119
  SUPERACE_CASH: "SuperAce 1.0% Cashback",
  JILI_GAMES_CASH: "Jili Games 1.0% Cashback",
  CASINO_LEADERBOARD: "Casino Elite Wealth Leaderboard",
  GET_EASTER_BONUS: "Get Easter Bonus",
  YB_SLOT_CASHBACK: "YB Extra 0.35% Cashback",
  SPORTS_LOSS_CASHBACK: "Sports Loss Cashback",
  SPORTS_DAILY_BONUS: "Sports Daily Bonus",
  NBA_CHAMPION_PREDICTION: "NBA Champion Prediction",
  SPIN_ACTIVITY: "Spin Activity",
  JILI_LEADERBOARD: "JILI Slot Rank",
  Valentine: "Valentine",
  WEEKLY_PAYDAY: "Weekly payday",
  LATE_NIGHT_CASHBACK: "Late Night Cashback",

  REGISTER_USER: "Welcome to NUSTAR Online!",
  BING_IPHONE_USER: "Congratulations on successfully\nbinding your mobile phone number",
};

const DATE_TYPE = {
  TODAY: "Today",
  YESTERDAY: "Yesterday",
  LAST_THREEDAYS: "Last 3 days",
  LAST_SEVENTDAYS: "Last 7 days",
};

export enum CHANEL_TERMINAL {
  "GP" = 1,
  "H5" = 2, // otherApp
  "iOS" = 4,
  "Gcash" = 8,
  "Maya" = 64,
  "Web" = 128,
}
