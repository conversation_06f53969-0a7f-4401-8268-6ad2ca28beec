/**
 * 性能监控工具
 * 监控页面加载性能、用户交互性能等指标
 */

interface PerformanceMetrics {
  // 页面加载指标
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte

  // 自定义指标
  routeChangeTime?: number;
  componentRenderTime?: number;
  apiResponseTime?: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];

  /**
   * 初始化性能监控
   */
  initialize(): void {
    this.observeWebVitals();
    this.observeNavigation();
    this.observeResources();
    console.log("📊 性能监控已启动");
  }

  /**
   * 监控 Web Vitals 指标
   */
  private observeWebVitals(): void {
    // FCP (First Contentful Paint)
    this.observePerformanceEntry("paint", (entries) => {
      const fcpEntry = entries.find((entry) => entry.name === "first-contentful-paint");
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime;
        console.log(`📈 FCP: ${fcpEntry.startTime.toFixed(2)}ms`);
      }
    });

    // LCP (Largest Contentful Paint)
    this.observePerformanceEntry("largest-contentful-paint", (entries) => {
      const lcpEntry = entries[entries.length - 1];
      if (lcpEntry) {
        this.metrics.lcp = lcpEntry.startTime;
        console.log(`📈 LCP: ${lcpEntry.startTime.toFixed(2)}ms`);
      }
    });

    // FID (First Input Delay)
    this.observePerformanceEntry("first-input", (entries) => {
      const fidEntry = entries[0];
      if (fidEntry) {
        this.metrics.fid = fidEntry.processingStart - fidEntry.startTime;
        console.log(`📈 FID: ${this.metrics.fid.toFixed(2)}ms`);
      }
    });

    // CLS (Cumulative Layout Shift)
    this.observePerformanceEntry("layout-shift", (entries) => {
      let clsValue = 0;
      entries.forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      this.metrics.cls = clsValue;
      console.log(`📈 CLS: ${clsValue.toFixed(4)}`);
    });
  }

  /**
   * 监控导航性能
   */
  private observeNavigation(): void {
    this.observePerformanceEntry("navigation", (entries) => {
      const navEntry = entries[0] as PerformanceNavigationTiming;
      if (navEntry) {
        this.metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
        console.log(`📈 TTFB: ${this.metrics.ttfb.toFixed(2)}ms`);
      }
    });
  }

  /**
   * 监控资源加载性能
   */
  private observeResources(): void {
    this.observePerformanceEntry("resource", (entries) => {
      entries.forEach((entry) => {
        const resourceEntry = entry as PerformanceResourceTiming;
        const duration = resourceEntry.responseEnd - resourceEntry.startTime;

        if (duration > 1000) {
          // 超过1秒的资源
          console.warn(`⚠️ 慢资源: ${resourceEntry.name} - ${duration.toFixed(2)}ms`);
        }
      });
    });
  }

  /**
   * 通用的性能观察器
   */
  private observePerformanceEntry(
    entryType: string,
    callback: (entries: PerformanceEntry[]) => void
  ): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });

      observer.observe({ entryTypes: [entryType] });
      this.observers.push(observer);
    } catch (error) {
      console.warn(`无法观察性能指标: ${entryType}`, error);
    }
  }

  /**
   * 测量路由切换时间
   */
  measureRouteChange(routeName: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.metrics.routeChangeTime = duration;
      console.log(`📈 路由切换 ${routeName}: ${duration.toFixed(2)}ms`);
    };
  }

  /**
   * 测量组件渲染时间
   */
  measureComponentRender(componentName: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.metrics.componentRenderTime = duration;
      console.log(`📈 组件渲染 ${componentName}: ${duration.toFixed(2)}ms`);
    };
  }

  /**
   * 测量 API 响应时间
   */
  measureApiResponse(apiName: string): () => void {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.metrics.apiResponseTime = duration;
      console.log(`📈 API响应 ${apiName}: ${duration.toFixed(2)}ms`);
    };
  }

  /**
   * 获取当前性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const report = [
      "📊 性能报告",
      "================",
      `FCP: ${this.metrics.fcp?.toFixed(2) || "N/A"}ms`,
      `LCP: ${this.metrics.lcp?.toFixed(2) || "N/A"}ms`,
      `FID: ${this.metrics.fid?.toFixed(2) || "N/A"}ms`,
      `CLS: ${this.metrics.cls?.toFixed(4) || "N/A"}`,
      `TTFB: ${this.metrics.ttfb?.toFixed(2) || "N/A"}ms`,
      "================",
    ].join("\n");

    console.log(report);
    return report;
  }

  /**
   * 清理观察器
   */
  cleanup(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
  }
}

// 导出单例
export const performanceMonitor = new PerformanceMonitor();

// 开发环境下自动启动
if (process.env.NODE_ENV === "development") {
  // performanceMonitor.initialize();
}
