/**
 * 资源预加载管理器
 * 统一管理图片、字体、脚本等资源的预加载
 */

interface PreloadResource {
  url: string;
  type: "image" | "font" | "script" | "style";
  priority: "high" | "medium" | "low";
  crossOrigin?: boolean;
}

class ResourcePreloader {
  private preloadedResources = new Set<string>();
  private preloadPromises = new Map<string, Promise<void>>();

  /**
   * 预加载单个资源
   */
  async preloadResource(resource: PreloadResource): Promise<void> {
    const { url, type, crossOrigin } = resource;

    if (this.preloadedResources.has(url)) {
      return;
    }

    if (this.preloadPromises.has(url)) {
      return this.preloadPromises.get(url);
    }

    const promise = this.loadResource(url, type, crossOrigin);
    this.preloadPromises.set(url, promise);

    try {
      await promise;
      this.preloadedResources.add(url);
      console.log(`✅ 资源预加载完成: ${url}`);
    } catch (error) {
      console.warn(`❌ 资源预加载失败: ${url}`, error);
    } finally {
      this.preloadPromises.delete(url);
    }
  }

  /**
   * 加载资源的具体实现
   */
  private loadResource(url: string, type: string, crossOrigin?: boolean): Promise<void> {
    return new Promise((resolve, reject) => {
      let element: HTMLElement;

      switch (type) {
        case "image":
          element = new Image();
          if (crossOrigin) {
            (element as HTMLImageElement).crossOrigin = "anonymous";
          }
          break;

        case "script":
          element = document.createElement("script");
          (element as HTMLScriptElement).src = url;
          if (crossOrigin) {
            (element as HTMLScriptElement).crossOrigin = "anonymous";
          }
          break;

        case "style":
          element = document.createElement("link");
          (element as HTMLLinkElement).rel = "stylesheet";
          (element as HTMLLinkElement).href = url;
          break;

        case "font":
          // 使用 FontFace API 预加载字体
          if ("FontFace" in window) {
            const fontFace = new FontFace("preload-font", `url(${url})`);
            fontFace
              .load()
              .then(() => {
                document.fonts.add(fontFace);
                resolve();
              })
              .catch(reject);
            return;
          } else {
            // 降级处理
            element = document.createElement("link");
            (element as HTMLLinkElement).rel = "preload";
            (element as HTMLLinkElement).as = "font";
            (element as HTMLLinkElement).href = url;
            (element as HTMLLinkElement).crossOrigin = "anonymous";
          }
          break;

        default:
          reject(new Error(`不支持的资源类型: ${type}`));
          return;
      }

      element.onload = () => resolve();
      element.onerror = () => reject(new Error(`加载失败: ${url}`));

      if (type === "image") {
        (element as HTMLImageElement).src = url;
      } else if (type === "style" || type === "font") {
        document.head.appendChild(element);
      }
    });
  }

  /**
   * 批量预加载资源
   */
  async preloadResources(resources: PreloadResource[]): Promise<void> {
    // 按优先级分组
    const highPriority = resources.filter((r) => r.priority === "high");
    const mediumPriority = resources.filter((r) => r.priority === "medium");
    const lowPriority = resources.filter((r) => r.priority === "low");

    // 高优先级并行加载
    if (highPriority.length > 0) {
      await Promise.all(highPriority.map((r) => this.preloadResource(r)));
    }

    // 中优先级串行加载
    for (const resource of mediumPriority) {
      await this.preloadResource(resource);
    }

    // 低优先级在空闲时加载
    if (lowPriority.length > 0) {
      this.preloadOnIdle(lowPriority);
    }
  }

  /**
   * 在浏览器空闲时预加载
   */
  private preloadOnIdle(resources: PreloadResource[]): void {
    if ("requestIdleCallback" in window) {
      requestIdleCallback(async () => {
        for (const resource of resources) {
          await this.preloadResource(resource);
        }
      });
    } else {
      setTimeout(async () => {
        for (const resource of resources) {
          await this.preloadResource(resource);
        }
      }, 2000);
    }
  }

  /**
   * 预加载关键图片
   */
  async preloadCriticalImages(): Promise<void> {
    const criticalImages: PreloadResource[] = [
      {
        url: "/src/assets/images/logo.png",
        type: "image",
        priority: "high",
      },
      {
        url: "/src/assets/images/home/<USER>",
        type: "image",
        priority: "high",
      },
    ];

    await this.preloadResources(criticalImages);
  }

  /**
   * 预加载游戏相关图片
   */
  preloadGameImages(): void {
    const gameImages: PreloadResource[] = [
      {
        url: "/src/assets/images/game-placeholder.png",
        type: "image",
        priority: "medium",
      },
      {
        url: "/src/assets/images/banner-placeholder.png",
        type: "image",
        priority: "medium",
      },
    ];

    this.preloadResources(gameImages);
  }

  /**
   * 获取预加载状态
   */
  getPreloadStatus(): { loaded: string[]; loading: string[] } {
    return {
      loaded: Array.from(this.preloadedResources),
      loading: Array.from(this.preloadPromises.keys()),
    };
  }
}

// 导出单例
export const resourcePreloader = new ResourcePreloader();
