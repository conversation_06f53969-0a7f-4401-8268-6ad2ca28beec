/**
 * 性能优化模块统一入口
 * 提供所有性能优化相关的工具和方法
 */

// 导出所有性能优化工具
export { routePreloader } from './RoutePreloader';
export { componentPreloader } from './ComponentPreloader';
export { resourcePreloader } from './ResourcePreloader';
export { performanceOptimizer } from './PerformanceOptimizer';
export { performanceMonitor } from './PerformanceMonitor';

// 导出类型定义
export type { PerformanceMetrics } from './PerformanceMonitor';

/**
 * 快速初始化所有性能优化
 */
export const initializePerformance = async () => {
  const { performanceOptimizer } = await import('./PerformanceOptimizer');
  return performanceOptimizer.initialize();
};

/**
 * 获取所有性能指标
 */
export const getAllPerformanceMetrics = () => {
  const { performanceOptimizer } = require('./PerformanceOptimizer');
  return performanceOptimizer.getPerformanceMetrics();
};
