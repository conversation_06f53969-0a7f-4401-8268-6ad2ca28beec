// 金币动画事件总线
import { ref } from "vue";

interface CoinAnimationConfig {
  startElement: HTMLElement;
  endElement: HTMLElement;
  coinCount?: number;
}

class CoinAnimationBus {
  private animationRef = ref<any>(null);
  private homeBalanceRef = ref<any>(null);

  // 设置金币动画组件引用
  setAnimationRef(ref: any) {
    this.animationRef.value = ref;
  }

  // 设置首页 Balance 组件引用
  setHomeBalanceRef(ref: any) {
    this.homeBalanceRef.value = ref;
  }

  // 触发金币动画
  startAnimation(config: CoinAnimationConfig) {
    if (!this.animationRef.value) {
      console.error("❌ 金币动画组件引用未设置");
      return;
    }

    if (!config.startElement || !config.endElement) {
      console.error("❌ 起始或结束元素未设置");
      return;
    }

    // 直接调用动画组件的方法，传递起始和结束元素
    this.animationRef.value.startAnimationWithElements(
      config.startElement,
      config.endElement,
      config.coinCount || 10
    );
    // 触发目标元素的动画效果
    if (this.homeBalanceRef.value?.startAnimation) {
      this.homeBalanceRef.value.startAnimation();
    }
  }

  // 清理引用
  clear() {
    this.animationRef.value = null;
    this.homeBalanceRef.value = null;
  }
}

// 导出单例
export const coinAnimationBus = new CoinAnimationBus();
