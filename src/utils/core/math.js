// math-utils.js
import Decimal from "decimal.js";

class MathUtils {
  /**
   * 加法运算
   * @param {number|string|Decimal} a - 加数
   * @param {number|string|Decimal} b - 被加数
   * @returns {number} - 计算结果
   */
  static add(a, b) {
    return new Decimal(a).add(b).toNumber();
  }

  /**
   * 减法运算
   * @param {number|string|Decimal} a - 减数
   * @param {number|string|Decimal} b - 被减数
   * @returns {number} - 计算结果
   */
  static subtract(a = 0, b = 0) {
    return new Decimal(a).sub(b).toNumber();
  }

  /**
   * 乘法运算
   * @param {number|string|Decimal} a - 乘数
   * @param {number|string|Decimal} b - 被乘数
   * @returns {number} - 计算结果
   */
  static multiply(a, b) {
    return new Decimal(a).mul(b).toNumber();
  }

  /**
   * 除法运算
   * @param {number|string|Decimal} a - 除数
   * @param {number|string|Decimal} b - 被除数
   * @param {number} [precision=20] - 精度（小数位数）
   * @returns {number} - 计算结果
   */
  static divide(a, b, precision = 2) {
    if (new Decimal(b).isZero()) {
      throw new Error("除数不能为零");
    }
    return parseFloat(new Decimal(a).div(b).toFixed(precision));
  }

  /**
   * 格式化数字（可选）
   * @param {number|string|Decimal} value - 要格式化的数字
   * @param {string} [format='0,0.00'] - 格式化规则
   * @returns {string} - 格式化后的字符串
   */
  static format(value, format = "0,0.00") {
    return new Decimal(value).toFormat(format);
  }
}

export default MathUtils;
