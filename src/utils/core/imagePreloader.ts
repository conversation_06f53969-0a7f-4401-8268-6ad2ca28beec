/**
 * 通用图片预加载工具
 * 支持普通图片、SVG、CSS背景图片等多种格式的预加载
 */

export interface PreloadOptions {
  /** 超时时间（毫秒），默认 8000ms */
  timeout?: number;
  /** 进度回调函数 */
  onProgress?: (loaded: number, total: number, currentImage?: string) => void;
  /** 错误回调函数 */
  onError?: (error: Error, src: string) => void;
  /** 是否并发加载，默认 true */
  concurrent?: boolean;
  /** 并发数量限制，默认 5 */
  concurrency?: number;
}

export interface PreloadResult {
  /** 成功加载的图片数量 */
  loaded: number;
  /** 总图片数量 */
  total: number;
  /** 失败的图片列表 */
  failed: string[];
  /** 成功的图片列表 */
  success: string[];
}

export class ImagePreloader {
  private preloadedImages = new Set<string>();
  private loadingPromises = new Map<string, Promise<void>>();

  /**
   * 预加载单个图片
   * @param src 图片地址
   * @param options 预加载选项
   */
  async preloadSingle(src: string, options: PreloadOptions = {}): Promise<boolean> {
    const { timeout = 8000, onError } = options;

    try {
      // 如果已经预加载过，直接返回成功
      if (this.preloadedImages.has(src)) {
        return true;
      }

      // 如果正在加载中，等待加载完成
      if (this.loadingPromises.has(src)) {
        await this.loadingPromises.get(src);
        return this.preloadedImages.has(src);
      }

      // 开始加载
      const loadPromise = this.loadImage(src, timeout);
      this.loadingPromises.set(src, loadPromise);

      await loadPromise;
      this.preloadedImages.add(src);
      this.loadingPromises.delete(src);

      return true;
    } catch (error) {
      this.loadingPromises.delete(src);
      const err = error instanceof Error ? error : new Error(String(error));
      onError?.(err, src);
      return false;
    }
  }

  /**
   * 预加载图片列表
   * @param images 图片地址列表
   * @param options 预加载选项
   */
  async preloadList(images: string[], options: PreloadOptions = {}): Promise<PreloadResult> {
    const { concurrent = true, concurrency = 5, onProgress } = options;
    const total = images.length;
    let loaded = 0;
    const failed: string[] = [];
    const success: string[] = [];

    if (concurrent) {
      // 并发加载（限制并发数）
      const chunks = this.chunkArray(images, concurrency);

      for (const chunk of chunks) {
        const promises = chunk.map(async (src) => {
          const result = await this.preloadSingle(src, options);
          loaded++;

          if (result) {
            success.push(src);
          } else {
            failed.push(src);
          }

          onProgress?.(loaded, total, src);
          return result;
        });

        await Promise.all(promises);
      }
    } else {
      // 串行加载
      for (const src of images) {
        const result = await this.preloadSingle(src, options);
        loaded++;

        if (result) {
          success.push(src);
        } else {
          failed.push(src);
        }

        onProgress?.(loaded, total, src);
      }
    }

    return { loaded, total, failed, success };
  }

  /**
   * 预加载 CSS 背景图片
   * @param images 图片地址列表
   * @param options 预加载选项
   */
  async preloadCSSBackgrounds(
    images: string[],
    options: PreloadOptions = {}
  ): Promise<PreloadResult> {
    // 创建样式表来预加载 CSS 背景图片
    const style = document.createElement("style");
    const cssRules = images
      .map(
        (src, index) => `.preload-bg-${Date.now()}-${index} { background-image: url('${src}'); }`
      )
      .join("\n");

    style.textContent = cssRules;
    document.head.appendChild(style);

    // 创建隐藏元素来触发图片加载
    const elements: HTMLElement[] = [];
    images.forEach((src, index) => {
      const div = document.createElement("div");
      div.className = `preload-bg-${Date.now()}-${index}`;
      div.style.cssText = `
        position: absolute;
        left: -9999px;
        top: -9999px;
        width: 1px;
        height: 1px;
        opacity: 0;
        pointer-events: none;
      `;
      document.body.appendChild(div);
      elements.push(div);
    });

    // 等待图片开始加载
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 使用普通方式预加载以确保完成
    const result = await this.preloadList(images, options);

    // 清理临时元素
    elements.forEach((el) => el.remove());
    document.head.removeChild(style);

    return result;
  }

  /**
   * 加载单个图片的核心方法
   */
  private loadImage(src: string, timeout: number): Promise<void> {
    return new Promise((resolve, reject) => {
      // 普通图片预加载
      const img = new Image();

      const timeoutId = setTimeout(() => {
        reject(new Error(`图片加载超时: ${src}`));
      }, timeout);

      img.onload = () => {
        clearTimeout(timeoutId);
        resolve();
      };

      img.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`图片加载失败: ${src}`));
      };

      img.src = src;
    });
  }

  /**
   * 加载 SVG 文件
   */
  private async loadSVG(src: string, timeout: number): Promise<void> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(src, { signal: controller.signal });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`SVG 加载失败: ${response.status}`);
      }

      const svgText = await response.text();

      // 验证是否为有效的 SVG
      if (!svgText.includes("<svg")) {
        throw new Error("无效的 SVG 文件");
      }

      // 创建一个隐藏的 div 来缓存 SVG 内容
      const div = document.createElement("div");
      div.style.display = "none";
      div.innerHTML = svgText;
      document.body.appendChild(div);

      // 立即移除，但浏览器已经解析和缓存了 SVG
      setTimeout(() => {
        if (document.body.contains(div)) {
          document.body.removeChild(div);
        }
      }, 100);
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === "AbortError") {
        throw new Error(`SVG 加载超时: ${src}`);
      }
      throw error;
    }
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 检查图片是否已预加载
   */
  isImageCached(src: string): boolean {
    return this.preloadedImages.has(src);
  }

  /**
   * 获取已预加载的图片数量
   */
  getCachedCount(): number {
    return this.preloadedImages.size;
  }

  /**
   * 清除预加载缓存
   */
  clearCache(): void {
    this.preloadedImages.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取图片文件名
   */
  getImageName(src: string): string {
    return src.split("/").pop() || src;
  }
}

// 创建全局实例
export const imagePreloader = new ImagePreloader();

// 便捷函数
export const preloadImage = (src: string, options?: PreloadOptions) =>
  imagePreloader.preloadSingle(src, options);

export const preloadImages = (images: string[], options?: PreloadOptions) =>
  imagePreloader.preloadList(images, options);

export const preloadCSSBackgrounds = (images: string[], options?: PreloadOptions) =>
  imagePreloader.preloadCSSBackgrounds(images, options);

export const isImageCached = (src: string) => imagePreloader.isImageCached(src);
