import { AWARD_UPDATE_TYPE, AWARD_NAME } from "@/utils/config/GlobalConstant";

export const init_enum_typestr = (update_type, type_str, register_str, lab_str?) => {
  //产品要求保险起见 客户端先保留枚举
  switch (update_type) {
    case AWARD_UPDATE_TYPE.FIRST_RECHARGE: //12-首充奖励
      if (lab_str) lab_str.string = AWARD_NAME.FIRST_DESPOSIT;
      else if (!type_str) type_str = AWARD_NAME.FIRST_DESPOSIT;
      break;
    case AWARD_UPDATE_TYPE.FREE_REGISTRATION: //65-注册
      if (lab_str) lab_str.string = register_str;
      else if (!type_str) type_str = register_str;
      break;
    case AWARD_UPDATE_TYPE.CASHBACK: //111-返水活动
      if (lab_str) lab_str.string = AWARD_NAME.CASHBACK;
      else if (!type_str) type_str = AWARD_NAME.CASHBACK;
      break;
    case AWARD_UPDATE_TYPE.VIP_CASHBACK: //241 119-VIP专属返水活动
    case AWARD_UPDATE_TYPE.VIP_CASHBACK_119:
      if (lab_str) lab_str.string = AWARD_NAME.VIP_CASHBACK;
      else if (!type_str) type_str = AWARD_NAME.VIP_CASHBACK;
      break;
    case AWARD_UPDATE_TYPE.BING_PHONE:
      if (lab_str) lab_str.string = AWARD_NAME.BING_PHONE;
      else if (!type_str) type_str = AWARD_NAME.BING_PHONE;
      break;
    case AWARD_UPDATE_TYPE.DAILY_BETTING:
      if (lab_str) lab_str.string = AWARD_NAME.DAILY_BETTING;
      else if (!type_str) type_str = AWARD_NAME.DAILY_BETTING;
      break;
    case AWARD_UPDATE_TYPE.FIRST_DESPOSIT:
      if (lab_str) lab_str.string = AWARD_NAME.FIRST_DESPOSIT; //也是首充 兼容以前版本
      else if (!type_str) type_str = AWARD_NAME.FIRST_DESPOSIT; //也是首充 兼容以前版本
      break;
    case AWARD_UPDATE_TYPE.INVITEE_GIFT:
      if (lab_str) lab_str.string = AWARD_NAME.INVITEE_GIFT;
      else if (!type_str) type_str = AWARD_NAME.INVITEE_GIFT;
      break;
    case AWARD_UPDATE_TYPE.INVITE_5GIFT:
      if (lab_str) lab_str.string = AWARD_NAME.INVITE_5GIFT;
      else if (!type_str) type_str = AWARD_NAME.INVITE_5GIFT;
      break;
    case AWARD_UPDATE_TYPE.INVITE_FRIENDS:
      if (lab_str) lab_str.string = AWARD_NAME.INVITE_FRIENDS;
      else if (!type_str) type_str = AWARD_NAME.INVITE_FRIENDS;
      break;
    case AWARD_UPDATE_TYPE.PAYDAY_BONUS:
      if (lab_str) lab_str.string = AWARD_NAME.PAYDAY_BONUS;
      else if (!type_str) type_str = AWARD_NAME.PAYDAY_BONUS;
      break;
    case AWARD_UPDATE_TYPE.SIGN_UP_BONUS:
      if (lab_str) lab_str.string = AWARD_NAME.SIGN_UP_BONUS;
      else if (!type_str) type_str = AWARD_NAME.SIGN_UP_BONUS;
      break;
    case AWARD_UPDATE_TYPE.SPORT_FIRST_TIME:
      if (lab_str) lab_str.string = AWARD_NAME.SPORT_FIRST_TIME;
      else if (!type_str) type_str = AWARD_NAME.SPORT_FIRST_TIME;
      break;
    case AWARD_UPDATE_TYPE.SUPERACE_CASH:
      if (lab_str) lab_str.string = AWARD_NAME.SUPERACE_CASH;
      else if (!type_str) type_str = AWARD_NAME.SUPERACE_CASH;
      break;
    case AWARD_UPDATE_TYPE.JILI_GAMES_CASHBACK:
      if (lab_str) lab_str.string = AWARD_NAME.JILI_GAMES_CASH;
      else if (!type_str) type_str = AWARD_NAME.JILI_GAMES_CASH;
      break;
    case AWARD_UPDATE_TYPE.WEEKLY_SIGNIN:
      if (lab_str) lab_str.string = AWARD_NAME.WEEKLY_SIGNIN;
      else if (!type_str) type_str = AWARD_NAME.WEEKLY_SIGNIN;
      break;
    case AWARD_UPDATE_TYPE.CASINO_LEADERBOARD:
      if (lab_str) lab_str.string = AWARD_NAME.CASINO_LEADERBOARD;
      else if (!type_str) type_str = AWARD_NAME.CASINO_LEADERBOARD;
      break;
    case AWARD_UPDATE_TYPE.GET_EASTER_BONUS:
      if (lab_str) lab_str.string = AWARD_NAME.GET_EASTER_BONUS;
      else if (!type_str) type_str = AWARD_NAME.GET_EASTER_BONUS;
      break;
    case AWARD_UPDATE_TYPE.YB_SLOT_CASHBACK:
      if (lab_str) lab_str.string = AWARD_NAME.YB_SLOT_CASHBACK;
      else if (!type_str) type_str = AWARD_NAME.YB_SLOT_CASHBACK;
      break;
    case AWARD_UPDATE_TYPE.SPORTS_LOSS_CASHBACK:
      if (lab_str) lab_str.string = AWARD_NAME.SPORTS_LOSS_CASHBACK;
      else if (!type_str) type_str = AWARD_NAME.SPORTS_LOSS_CASHBACK;
      break;
    case AWARD_UPDATE_TYPE.SPORTS_DAILY_BONUS:
      if (lab_str) lab_str.string = AWARD_NAME.SPORTS_DAILY_BONUS;
      else if (!type_str) type_str = AWARD_NAME.SPORTS_DAILY_BONUS;
      break;
    case AWARD_UPDATE_TYPE.NBA_CHAMPION_PREDICTION:
      if (lab_str) lab_str.string = AWARD_NAME.NBA_CHAMPION_PREDICTION;
      else if (!type_str) type_str = AWARD_NAME.NBA_CHAMPION_PREDICTION;
      break;
    case AWARD_UPDATE_TYPE.SPIN_ACTIVITY:
      if (lab_str) lab_str.string = AWARD_NAME.SPIN_ACTIVITY;
      else if (!type_str) type_str = AWARD_NAME.SPIN_ACTIVITY;
      break;
    case AWARD_UPDATE_TYPE.JILI_LEADERBOARD:
      if (lab_str) lab_str.string = AWARD_NAME.JILI_LEADERBOARD;
      else if (!type_str) type_str = AWARD_NAME.JILI_LEADERBOARD;
      break;
    case AWARD_UPDATE_TYPE.Valentine:
      if (lab_str) lab_str.string = AWARD_NAME.Valentine;
      else if (!type_str) type_str = AWARD_NAME.Valentine;
      break;
    case AWARD_UPDATE_TYPE.WEEKLY_PAYDAY:
      if (lab_str) lab_str.string = AWARD_NAME.WEEKLY_PAYDAY;
      else if (!type_str) type_str = AWARD_NAME.WEEKLY_PAYDAY;
      break;
    case AWARD_UPDATE_TYPE.LATE_NIGHT_CASHBACK:
      if (lab_str) lab_str.string = AWARD_NAME.LATE_NIGHT_CASHBACK;
      else if (!type_str) type_str = AWARD_NAME.LATE_NIGHT_CASHBACK;
      break;
    default:
      break;
  }
};
