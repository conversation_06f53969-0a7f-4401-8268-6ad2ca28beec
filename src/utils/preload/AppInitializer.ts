import { ref } from "vue";
import { useGameStore } from "@/stores/game";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { popupImagePreloader, coinImagePreloader } from "@/utils/preload/images";
import { fontLoader } from "@/utils/preload/FontLoader";
import { serviceMgr } from "@/utils/ServiceMgr";

export interface InitProgress {
  value: number;
  stage: string;
  message: string;
}

/**
 * 应用初始化器
 * 负责应用启动时的各种资源和服务初始化
 */
export class AppInitializer {
  private initProgress = ref(0);

  // 日志开关控制
  // private static DEBUG_ENABLED =
  //   process.env.NODE_ENV === "development" || localStorage.getItem("APP_INIT_DEBUG") === "true";
  private static DEBUG_ENABLED = false;

  // 进度阶段定义 - 更合理的进度分配
  private static readonly PROGRESS_STAGES = {
    START: 0,
    CRITICAL_INIT_START: 5,
    CRITICAL_POPUP_DATA: 15,
    CRITICAL_INIT_COMPLETE: 25,
    POPUP_IMAGES_START: 30,
    POPUP_IMAGES_COMPLETE: 50,
    USER_DATA_START: 55,
    USER_DATA_COMPLETE: 75,
    BASIC_SERVICES: 80,
    FONTS_START: 85,
    FONTS_COMPLETE: 95,
    FINALIZE_COMPLETE: 100,
  } as const;

  private getGameStore() {
    return useGameStore();
  }

  private getAutoPopMgrStore() {
    return useAutoPopMgrStore();
  }

  getProgress() {
    return this.initProgress;
  }

  /**
   * 日志打印方法
   */
  private log(message: string, ...args: any[]): void {
    if (AppInitializer.DEBUG_ENABLED) {
      console.log(`[AppInitializer] ${message}`, ...args);
    }
  }

  private warn(message: string, ...args: any[]): void {
    if (AppInitializer.DEBUG_ENABLED) {
      console.warn(`[AppInitializer] ${message}`, ...args);
    }
  }

  private error(message: string, ...args: any[]): void {
    console.error(`[AppInitializer] ${message}`, ...args);
  }

  /**
   * 开始应用初始化
   * 按照优化后的顺序和进度分配执行初始化
   */
  async initialize(onProgress?: (progress: InitProgress) => void): Promise<void> {
    try {
      this.log("=== 开始应用初始化 ===");

      // 阶段1: 关键初始化 (0-25%)
      await this.initializeCritical(onProgress);

      // 阶段2: 弹窗图片立即加载 (25-50%)
      await this.initializePopupImages(onProgress);

      // 阶段3: 用户数据加载 (50-75%)
      await this.initializeUserData(onProgress);

      // 阶段4: 启动后台任务 (75-95%)
      this.startBackgroundTasks(onProgress);

      // 阶段5: 完成初始化 (95-100%)
      await this.finalizeInitialization(onProgress);

      this.log("=== 应用初始化完成 ===");
    } catch (error) {
      this.error("应用初始化失败:", error);
      throw error;
    }
  }
  /**
   * 关键初始化阶段 (0-25%)
   * 加载应用启动必需的关键资源
   */
  private async initializeCritical(onProgress?: (progress: InitProgress) => void): Promise<void> {
    this.log("开始关键初始化阶段");

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.START,
      "关键初始化",
      "开始关键初始化...",
      onProgress
    );

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.CRITICAL_INIT_START,
      "关键初始化",
      "图标字体已就绪...",
      onProgress
    );

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.CRITICAL_POPUP_DATA,
      "关键初始化",
      "加载关键弹窗数据...",
      onProgress
    );

    await this.preloadCriticalPopupData();

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.CRITICAL_INIT_COMPLETE,
      "关键初始化",
      "关键初始化完成",
      onProgress
    );

    this.log("关键初始化阶段完成");
  }
  /**
   * 弹窗图片立即加载阶段 (25-50%)
   * 立即加载弹窗图片，不再延迟
   */
  private async initializePopupImages(
    onProgress?: (progress: InitProgress) => void
  ): Promise<void> {
    this.log("开始弹窗图片加载阶段");

    try {
      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_START,
        "弹窗图片",
        "开始预加载弹窗图片...",
        onProgress
      );

      await popupImagePreloader.preloadAll((loaded: number, total: number) => {
        if (total > 0) {
          // 将弹窗图片加载进度映射到 30-50% 区间
          const imageProgress = (loaded / total) * 20; // 20% 的进度空间
          const currentProgress = AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_START + imageProgress;
          this.updateProgress(
            Math.min(currentProgress, AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_COMPLETE),
            "弹窗图片",
            `预加载弹窗图片 ${loaded}/${total}`,
            onProgress
          );
        }
      });

      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_COMPLETE,
        "弹窗图片",
        "弹窗图片预加载完成",
        onProgress
      );

      // 预加载金币动画图片
      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_COMPLETE + 2,
        "金币图片",
        "预加载金币动画图片...",
        onProgress
      );

      try {
        await coinImagePreloader.preloadImages();
        this.log("金币图片预加载完成");
      } catch (error) {
        this.warn("金币图片预加载失败:", error);
      }

      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_COMPLETE + 5,
        "金币图片",
        "金币图片预加载完成",
        onProgress
      );

      this.log("弹窗图片加载阶段完成");
    } catch (error) {
      this.warn("弹窗图片预加载失败:", error);
      // 即使失败也要更新进度
      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.POPUP_IMAGES_COMPLETE,
        "弹窗图片",
        "弹窗图片预加载失败，跳过",
        onProgress
      );
    }
  }

  /**
   * 用户数据加载阶段 (50-75%)
   */
  private async initializeUserData(onProgress?: (progress: InitProgress) => void): Promise<void> {
    this.log("开始用户数据加载阶段");

    try {
      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.USER_DATA_START,
        "用户数据",
        "加载用户数据...",
        onProgress
      );

      const gameStore = this.getGameStore();
      await gameStore.init();

      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.USER_DATA_COMPLETE,
        "用户数据",
        "用户数据加载完成",
        onProgress
      );

      this.log("用户数据加载阶段完成");
    } catch (error) {
      this.warn("用户数据加载失败:", error);
      // 即使失败也要更新进度
      this.updateProgress(
        AppInitializer.PROGRESS_STAGES.USER_DATA_COMPLETE,
        "用户数据",
        "用户数据加载失败，跳过",
        onProgress
      );
    }
  }

  /**
   * 启动后台任务阶段 (75-95%)
   */
  private startBackgroundTasks(onProgress?: (progress: InitProgress) => void): void {
    this.log("启动后台任务阶段");

    // 基础服务初始化
    this.initializeBasicServicesOnIdle(onProgress);

    // 字体异步加载
    this.initializeFontsAsync(onProgress);
  }

  /**
   * 完成初始化阶段 (95-100%)
   */
  private async finalizeInitialization(
    onProgress?: (progress: InitProgress) => void
  ): Promise<void> {
    this.log("开始完成初始化阶段");

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.FONTS_COMPLETE,
      "完成初始化",
      "完成最终初始化...",
      onProgress
    );

    await this.ensureCriticalResourcesLoaded();

    this.updateProgress(
      AppInitializer.PROGRESS_STAGES.FINALIZE_COMPLETE,
      "完成初始化",
      "应用初始化完成",
      onProgress
    );

    this.log("完成初始化阶段完成");
  }

  /**
   * 预加载关键弹窗数据
   */
  private async preloadCriticalPopupData(): Promise<void> {
    try {
      this.log("开始加载关键弹窗数据");
      const autoPopMgrStore = this.getAutoPopMgrStore();
      await Promise.all([autoPopMgrStore.getBanners()]);
      this.log("关键弹窗数据加载完成");
    } catch (error) {
      this.warn("关键弹窗数据加载失败:", error);
    }
  }

  /**
   * 基础服务初始化
   * 客服脚本等（Geetest 现在在 App.vue 中初始化）
   */
  private initializeBasicServices(): void {
    try {
      this.log("开始基础服务初始化");
      // 注意：Geetest 现在在 App.vue 中的 preLogin 后初始化，不在这里初始化
      new serviceMgr().init();
      this.log("基础服务初始化完成");
    } catch (error) {
      this.warn("基础服务初始化失败:", error);
    }
  }

  /**
   * 确保关键资源已加载
   */
  private async ensureCriticalResourcesLoaded(): Promise<void> {
    this.log("关键资源检查完成");
    // 可以在这里添加额外的资源检查逻辑
  }

  /**
   * 在浏览器空闲时初始化基础服务
   */
  private initializeBasicServicesOnIdle(onProgress?: (progress: InitProgress) => void): void {
    if ("requestIdleCallback" in window) {
      (window as any).requestIdleCallback(
        () => {
          this.initializeBasicServices();
          this.updateProgress(
            AppInitializer.PROGRESS_STAGES.BASIC_SERVICES,
            "基础服务",
            "基础服务空闲时初始化完成",
            onProgress
          );
        },
        { timeout: 5000 }
      );
    } else {
      setTimeout(() => {
        this.initializeBasicServices();
        this.updateProgress(
          AppInitializer.PROGRESS_STAGES.BASIC_SERVICES,
          "基础服务",
          "基础服务延迟初始化完成",
          onProgress
        );
      }, 50);
    }
  }

  /**
   * 字体异步加载
   */
  private initializeFontsAsync(onProgress?: (progress: InitProgress) => void): void {
    setTimeout(async () => {
      try {
        this.log("开始字体异步加载");

        this.updateProgress(
          AppInitializer.PROGRESS_STAGES.FONTS_START,
          "字体加载",
          "空闲时预加载字体...",
          onProgress
        );

        fontLoader
          .preloadHighPriorityFonts()
          .then(() => this.log("高优先级字体异步加载完成"))
          .catch((error) => this.warn("高优先级字体异步加载失败:", error));

        fontLoader.loadFontsOnIdle();

        this.updateProgress(
          AppInitializer.PROGRESS_STAGES.FONTS_COMPLETE,
          "字体加载",
          "字体空闲加载已启动",
          onProgress
        );

        this.log("字体异步加载完成");
      } catch (error) {
        this.warn("字体空闲加载启动失败:", error);
      }
    }, 200); // 减少延迟时间
  }

  /**
   * 更新初始化进度
   */
  private updateProgress(
    value: number,
    stage: string,
    message: string,
    onProgress?: (progress: InitProgress) => void
  ): void {
    // 确保进度值在合理范围内
    const clampedValue = Math.max(0, Math.min(100, value));
    this.initProgress.value = clampedValue;

    const progress = { value: clampedValue, stage, message };
    onProgress?.(progress);

    // 使用日志开关控制进度日志
    this.log(`[${stage}] ${message} (${clampedValue}%)`);
  }

  /**
   * 启用调试日志
   * 可以在控制台调用 AppInitializer.enableDebug() 来启用
   */
  static enableDebug(): void {
    localStorage.setItem("APP_INIT_DEBUG", "true");
    AppInitializer.DEBUG_ENABLED = true;
    console.log("[AppInitializer] 调试日志已启用");
  }

  /**
   * 禁用调试日志
   */
  static disableDebug(): void {
    localStorage.removeItem("APP_INIT_DEBUG");
    AppInitializer.DEBUG_ENABLED = false;
    console.log("[AppInitializer] 调试日志已禁用");
  }
}

// 导出单例实例
export const appInitializer = new AppInitializer();
