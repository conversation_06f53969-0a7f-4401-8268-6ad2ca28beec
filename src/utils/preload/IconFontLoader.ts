/**
 * 图标字体动态加载器
 * 用于动态加载 iconfont.css 而不是在 main.ts 中同步导入
 */

export class IconFontLoader {
  private static instance: IconFontLoader;
  private isLoaded = false;
  private loadingPromise: Promise<void> | null = null;

  static getInstance(): IconFontLoader {
    if (!IconFontLoader.instance) {
      IconFontLoader.instance = new IconFontLoader();
    }
    return IconFontLoader.instance;
  }

  /**
   * 动态加载图标字体 CSS
   */
  async loadIconFontCSS(): Promise<void> {
    if (this.isLoaded) {
      return Promise.resolve();
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise<void>((resolve, reject) => {
      // 检查是否已经有 iconfont 样式
      const existingLink = document.querySelector('link[href*="iconfont.css"]');
      if (existingLink) {
        this.isLoaded = true;
        resolve();
        return;
      }

      // 创建 link 元素
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/src/assets/iconfonts/iconfont.css';
      link.type = 'text/css';

      // 设置加载事件
      link.onload = () => {
        this.isLoaded = true;
        console.log('图标字体 CSS 加载成功');
        resolve();
      };

      link.onerror = () => {
        console.error('图标字体 CSS 加载失败');
        reject(new Error('Failed to load iconfont.css'));
      };

      // 添加到 head
      document.head.appendChild(link);
    });

    return this.loadingPromise;
  }

  /**
   * 内联加载图标字体样式
   * 更快的加载方式，直接注入样式
   */
  async loadIconFontInline(): Promise<void> {
    if (this.isLoaded) {
      return Promise.resolve();
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    this.loadingPromise = new Promise<void>(async (resolve, reject) => {
      try {
        // 检查是否已经有 iconfont 样式
        const existingStyle = document.querySelector('style[data-iconfont]');
        if (existingStyle) {
          this.isLoaded = true;
          resolve();
          return;
        }

        // 创建内联样式
        const style = document.createElement('style');
        style.setAttribute('data-iconfont', 'true');
        style.textContent = `
          @font-face {
            font-family: "iconfont";
            src: url('/src/assets/iconfonts/iconfont.woff2?t=1750902059451') format('woff2'),
                 url('/src/assets/iconfonts/iconfont.woff?t=1750902059451') format('woff'),
                 url('/src/assets/iconfonts/iconfont.ttf?t=1750902059451') format('truetype');
            font-display: block;
          }

          .iconfont {
            font-family: "iconfont" !important;
            font-size: 16px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
        `;

        // 添加到 head 的最前面，确保优先级
        document.head.insertBefore(style, document.head.firstChild);

        // 等待字体加载
        if ('fonts' in document) {
          try {
            await (document as any).fonts.load('16px iconfont');
            console.log('图标字体内联加载成功');
          } catch (error) {
            console.warn('图标字体加载检测失败，但样式已注入');
          }
        }

        this.isLoaded = true;
        resolve();
      } catch (error) {
        console.error('图标字体内联加载失败:', error);
        reject(error);
      }
    });

    return this.loadingPromise;
  }

  /**
   * 预加载图标字体文件
   */
  async preloadIconFontFiles(): Promise<void> {
    const fontUrls = [
      '/src/assets/iconfonts/iconfont.woff2?t=1750902059451',
      '/src/assets/iconfonts/iconfont.woff?t=1750902059451',
    ];

    const preloadPromises = fontUrls.map(url => {
      return new Promise<void>((resolve) => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = url;
        link.as = 'font';
        link.type = url.includes('woff2') ? 'font/woff2' : 'font/woff';
        link.crossOrigin = 'anonymous';

        link.onload = () => {
          console.log(`图标字体文件预加载成功: ${url.split('/').pop()}`);
          resolve();
        };

        link.onerror = () => {
          console.warn(`图标字体文件预加载失败: ${url.split('/').pop()}`);
          resolve(); // 失败也继续
        };

        document.head.appendChild(link);
      });
    });

    await Promise.all(preloadPromises);
  }

  /**
   * 完整的图标字体加载流程
   */
  async loadIconFont(): Promise<void> {
    try {
      console.log('开始加载图标字体...');

      // 1. 预加载字体文件
      await this.preloadIconFontFiles();

      // 2. 内联加载样式（更快）
      await this.loadIconFontInline();

      console.log('图标字体加载完成');
    } catch (error) {
      console.error('图标字体加载失败:', error);
      // 降级到 CSS 文件加载
      try {
        await this.loadIconFontCSS();
      } catch (cssError) {
        console.error('图标字体 CSS 降级加载也失败:', cssError);
      }
    }
  }

  /**
   * 检查图标字体是否已加载
   */
  isIconFontLoaded(): boolean {
    return this.isLoaded;
  }

  /**
   * 等待图标字体加载完成
   */
  async waitForIconFont(): Promise<void> {
    if (this.isLoaded) {
      return Promise.resolve();
    }

    if (this.loadingPromise) {
      return this.loadingPromise;
    }

    // 如果没有开始加载，则开始加载
    return this.loadIconFont();
  }
}

// 导出单例实例
export const iconFontLoader = IconFontLoader.getInstance();
