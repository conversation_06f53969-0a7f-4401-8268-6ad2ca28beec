/**
 * 游戏图片预加载器
 * 基于通用图片预加载工具构建，专门用于游戏相关图片的预加载
 */

import { imagePreloader, type PreloadOptions } from "@/utils/core/imagePreloader";

export class GameImagePreloader {
  /**
   * 预加载游戏列表中的图片
   * @param games 游戏列表
   * @param options 预加载选项
   */
  async preloadGameImages(
    games: Array<{ image?: string; company_logo?: string }>,
    options?: PreloadOptions
  ): Promise<void> {
    const images: string[] = [];

    // 收集所有游戏图片
    games.forEach((game) => {
      if (game.image) {
        images.push(game.image);
      }
      if (game.company_logo) {
        images.push(game.company_logo);
      }
    });

    // 去重
    const uniqueImages = [...new Set(images)];

    if (uniqueImages.length === 0) {
      return;
    }

    console.log(`开始预加载 ${uniqueImages.length} 张游戏图片...`);

    const defaultOptions: PreloadOptions = {
      timeout: 5000,
      concurrent: true,
      concurrency: 8,
      onProgress: (loaded, total) => {
        console.log(`游戏图片预加载进度: ${loaded}/${total}`);
      },
      onError: (error, src) => {
        console.warn(`游戏图片预加载失败: ${src}`, error);
      },
      ...options,
    };

    await imagePreloader.preloadList(uniqueImages, defaultOptions);
    console.log("游戏图片预加载完成");
  }

  /**
   * 预加载厂商 Logo
   * @param providers 厂商列表
   * @param options 预加载选项
   */
  async preloadProviderLogos(
    providers: Array<{ logo?: string; image?: string }>,
    options?: PreloadOptions
  ): Promise<void> {
    const logos: string[] = [];

    providers.forEach((provider) => {
      if (provider.logo) {
        logos.push(provider.logo);
      }
      if (provider.image) {
        logos.push(provider.image);
      }
    });

    const uniqueLogos = [...new Set(logos)];

    if (uniqueLogos.length === 0) {
      return;
    }

    console.log(`开始预加载 ${uniqueLogos.length} 张厂商 Logo...`);

    const defaultOptions: PreloadOptions = {
      timeout: 3000,
      concurrent: true,
      concurrency: 10,
      ...options,
    };

    await imagePreloader.preloadList(uniqueLogos, defaultOptions);
    console.log("厂商 Logo 预加载完成");
  }

  /**
   * 预加载游戏分类图标
   * @param categories 分类列表
   * @param options 预加载选项
   */
  async preloadCategoryIcons(
    categories: Array<{ icon?: string; image?: string }>,
    options?: PreloadOptions
  ): Promise<void> {
    const icons: string[] = [];

    categories.forEach((category) => {
      if (category.icon) {
        icons.push(category.icon);
      }
      if (category.image) {
        icons.push(category.image);
      }
    });

    const uniqueIcons = [...new Set(icons)];

    if (uniqueIcons.length === 0) {
      return;
    }

    console.log(`开始预加载 ${uniqueIcons.length} 张分类图标...`);

    const defaultOptions: PreloadOptions = {
      timeout: 2000,
      concurrent: true,
      concurrency: 15,
      ...options,
    };

    await imagePreloader.preloadList(uniqueIcons, defaultOptions);
    console.log("分类图标预加载完成");
  }

  /**
   * 预加载轮播图
   * @param banners 轮播图列表
   * @param options 预加载选项
   */
  async preloadBanners(
    banners: Array<{ image?: string; mobile_image?: string }>,
    options?: PreloadOptions
  ): Promise<void> {
    const images: string[] = [];

    banners.forEach((banner) => {
      if (banner.image) {
        images.push(banner.image);
      }
      if (banner.mobile_image) {
        images.push(banner.mobile_image);
      }
    });

    const uniqueImages = [...new Set(images)];

    if (uniqueImages.length === 0) {
      return;
    }

    console.log(`开始预加载 ${uniqueImages.length} 张轮播图...`);

    const defaultOptions: PreloadOptions = {
      timeout: 10000, // 轮播图通常较大，给更多时间
      concurrent: true,
      concurrency: 3, // 轮播图并发数少一些，避免占用太多带宽
      ...options,
    };

    await imagePreloader.preloadList(uniqueImages, defaultOptions);
    console.log("轮播图预加载完成");
  }

  /**
   * 预加载用户头像
   * @param avatars 头像列表
   * @param options 预加载选项
   */
  async preloadAvatars(avatars: string[], options?: PreloadOptions): Promise<void> {
    const uniqueAvatars = [...new Set(avatars)];

    if (uniqueAvatars.length === 0) {
      return;
    }

    console.log(`开始预加载 ${uniqueAvatars.length} 张用户头像...`);

    const defaultOptions: PreloadOptions = {
      timeout: 3000,
      concurrent: true,
      concurrency: 6,
      ...options,
    };

    await imagePreloader.preloadList(uniqueAvatars, defaultOptions);
    console.log("用户头像预加载完成");
  }

  /**
   * 检查游戏图片是否已缓存
   * @param src 图片地址
   */
  isGameImageCached(src: string): boolean {
    return imagePreloader.isImageCached(src);
  }

  /**
   * 获取已缓存的图片数量
   */
  getCachedImageCount(): number {
    return imagePreloader.getCachedCount();
  }

  /**
   * 清除游戏图片缓存
   */
  clearGameImageCache(): void {
    imagePreloader.clearCache();
  }
}

// 创建全局实例
export const gameImagePreloader = new GameImagePreloader();

// 便捷函数
export const preloadGameImages = (
  games: Array<{ image?: string; company_logo?: string }>,
  options?: PreloadOptions
) => gameImagePreloader.preloadGameImages(games, options);

export const preloadProviderLogos = (
  providers: Array<{ logo?: string; image?: string }>,
  options?: PreloadOptions
) => gameImagePreloader.preloadProviderLogos(providers, options);

export const preloadBanners = (
  banners: Array<{ image?: string; mobile_image?: string }>,
  options?: PreloadOptions
) => gameImagePreloader.preloadBanners(banners, options);

export const isGameImageCached = (src: string) => 
  gameImagePreloader.isGameImageCached(src);
