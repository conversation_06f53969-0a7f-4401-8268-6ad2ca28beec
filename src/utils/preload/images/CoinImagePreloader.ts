/**
 * 金币图片预加载器
 * 基于通用图片预加载工具构建，专门用于金币动画图片的预加载
 */

import { imagePreloader, type PreloadOptions } from "@/utils/core/imagePreloader";

// 导入 assets 中的金币图片
import coin1 from "@/assets/frame/coin/1.png";
import coin2 from "@/assets/frame/coin/2.png";
import coin3 from "@/assets/frame/coin/3.png";
import coin4 from "@/assets/frame/coin/4.png";
import coin5 from "@/assets/frame/coin/5.png";
import coin6 from "@/assets/frame/coin/6.png";

// 金币图片路径常量
export const COIN_IMAGE_PATHS = Object.freeze([coin1, coin2, coin3, coin4, coin5, coin6]);

// 金币图片尺寸常量
export const COIN_FRAME_SIZES = Object.freeze([
  { width: 50, height: 50 },
  { width: 44, height: 50 },
  { width: 33, height: 50 },
  { width: 14, height: 50 },
  { width: 31, height: 50 },
  { width: 44, height: 50 },
]);

export interface CoinImageInfo {
  path: string;
  size: { width: number; height: number };
  index: number;
}

export class CoinImagePreloader {
  private static instance: CoinImagePreloader;
  private isPreloading = false;
  private preloadPromise: Promise<void> | null = null;

  private constructor() {}

  static getInstance(): CoinImagePreloader {
    if (!CoinImagePreloader.instance) {
      CoinImagePreloader.instance = new CoinImagePreloader();
    }
    return CoinImagePreloader.instance;
  }

  /**
   * 获取金币图片路径数组
   */
  getImagePaths(): readonly string[] {
    return COIN_IMAGE_PATHS;
  }

  /**
   * 获取金币图片尺寸数组
   */
  getImageSizes(): readonly { width: number; height: number }[] {
    return COIN_FRAME_SIZES;
  }

  /**
   * 获取金币图片信息（路径 + 尺寸）
   */
  getImageInfos(): CoinImageInfo[] {
    return COIN_IMAGE_PATHS.map((path, index) => ({
      path,
      size: COIN_FRAME_SIZES[index],
      index,
    }));
  }

  /**
   * 预加载所有金币图片
   * @param options 预加载选项
   */
  async preloadImages(options?: PreloadOptions): Promise<void> {
    // 如果已经在预加载或已完成，直接返回
    if (this.preloadPromise) {
      return this.preloadPromise;
    }

    if (this.isPreloading) {
      return;
    }

    this.isPreloading = true;
    // console.log("🪙 开始预加载金币动画图片...");

    this.preloadPromise = this.doPreload(options);

    try {
      await this.preloadPromise;
      // console.log("🪙 金币图片预加载完成");
    } catch (error) {
      console.warn("🪙 金币图片预加载失败:", error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * 执行预加载
   */
  private async doPreload(options?: PreloadOptions): Promise<void> {
    const defaultOptions: PreloadOptions = {
      timeout: 5000,
      concurrent: true,
      concurrency: 6, // 金币图片不多，可以全部并发
      onProgress: (loaded, total) => {
        // console.log(`🪙 金币图片预加载进度: ${loaded}/${total}`);
      },
      onError: (error, src) => {
        console.warn(`🪙 金币图片加载失败: ${src.split("/").pop()}`, error);
      },
      ...options,
    };

    try {
      await imagePreloader.preloadList([...COIN_IMAGE_PATHS], defaultOptions);
    } catch (error) {
      console.warn("🪙 部分金币图片预加载失败:", error);
      // 即使部分失败也继续，不阻塞应用
    }
  }

  /**
   * 检查图片是否已预加载
   */
  isImagePreloaded(src: string): boolean {
    return imagePreloader.isImageCached(src);
  }

  /**
   * 检查所有金币图片是否都已预加载
   */
  areAllImagesPreloaded(): boolean {
    return COIN_IMAGE_PATHS.every((src) => this.isImagePreloaded(src));
  }

  /**
   * 获取已预加载的图片数量
   */
  getPreloadedCount(): number {
    return COIN_IMAGE_PATHS.filter((src) => this.isImagePreloaded(src)).length;
  }

  /**
   * 获取预加载进度百分比
   */
  getPreloadProgress(): number {
    const loaded = this.getPreloadedCount();
    const total = COIN_IMAGE_PATHS.length;
    return Math.round((loaded / total) * 100);
  }

  /**
   * 强制预加载指定的金币图片
   */
  async forcePreloadImage(index: number): Promise<boolean> {
    if (index < 0 || index >= COIN_IMAGE_PATHS.length) {
      console.warn(`🪙 无效的金币图片索引: ${index}`);
      return false;
    }

    const src = COIN_IMAGE_PATHS[index];
    try {
      const result = await imagePreloader.preloadSingle(src, {
        timeout: 3000,
        onError: (error) => {
          console.warn(`🪙 强制预加载金币图片失败: ${src.split("/").pop()}`, error);
        },
      });

      if (result) {
        console.log(`🪙 强制预加载金币图片成功: ${src.split("/").pop()}`);
      }

      return result;
    } catch (error) {
      console.warn(`🪙 强制预加载金币图片异常: ${src.split("/").pop()}`, error);
      return false;
    }
  }

  /**
   * 清理缓存（用于测试或重置）
   */
  clearCache(): void {
    // 注意：这会清理整个图片预加载器的缓存，不只是金币图片
    imagePreloader.clearCache();
    this.isPreloading = false;
    this.preloadPromise = null;
    console.log("🪙 金币图片缓存已清理");
  }

  /**
   * 获取预加载状态信息
   */
  getStatus() {
    return {
      isPreloading: this.isPreloading,
      totalImages: COIN_IMAGE_PATHS.length,
      preloadedCount: this.getPreloadedCount(),
      progress: this.getPreloadProgress(),
      allPreloaded: this.areAllImagesPreloaded(),
    };
  }
}

// 导出单例实例
export const coinImagePreloader = CoinImagePreloader.getInstance();

// 便捷函数
export const preloadCoinImages = (options?: PreloadOptions) =>
  coinImagePreloader.preloadImages(options);

export const isCoinImagePreloaded = (index: number): boolean => {
  if (index < 0 || index >= COIN_IMAGE_PATHS.length) return false;
  return coinImagePreloader.isImagePreloaded(COIN_IMAGE_PATHS[index]);
};

export const getCoinImagePath = (index: number): string | null => {
  if (index < 0 || index >= COIN_IMAGE_PATHS.length) return null;
  return COIN_IMAGE_PATHS[index];
};

export const getCoinImageSize = (index: number): { width: number; height: number } | null => {
  if (index < 0 || index >= COIN_FRAME_SIZES.length) return null;
  return COIN_FRAME_SIZES[index];
};

// 向后兼容的导出
export { COIN_IMAGE_PATHS as coinImages, COIN_FRAME_SIZES as coinSizes };
