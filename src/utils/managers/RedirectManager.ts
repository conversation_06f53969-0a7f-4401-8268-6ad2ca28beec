/**
 * 重定向管理器
 * 负责处理页面跳转时的参数传递和重定向逻辑
 */
import router from "@/router";

export interface RedirectParams {
  type?: string;
  id?: string;
  amount?: string;
  [key: string]: string | undefined;
}

// 重定向页面类型常量
const REDIRECT_PAGE = {
  game: "game", // 游戏分类
  promos: "promos", // 促销页面
  news: "news", // 新闻页面
  vip: "vip", // VIP页面
  all: "all", // 更多游戏
  service: "service", // 客服
  bet: "bet", // 投注
  banner: "banner", // Banner跳转
  pay: "pay", // 支付回调
} as const;

export class RedirectManager {
  /**
   * 跳转到登录页面，携带重定向参数
   * @param redirectParams 重定向参数
   */
  static toLogin(redirectParams?: RedirectParams): void {
    const loginParams = new URLSearchParams();

    if (redirectParams) {
      Object.entries(redirectParams).forEach(([key, value]) => {
        if (value) {
          loginParams.set(`redirect_${key}`, value);
        }
      });
    }

    const loginUrl = loginParams.toString() ? `/login?${loginParams.toString()}` : "/login";
    router.push(loginUrl);
  }

  /**
   * 跳转到登录页面，携带当前页面的所有参数
   */
  static toLoginWithCurrentParams(): void {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectParams: RedirectParams = {};

    // 提取当前页面的参数
    const type = urlParams.get("type");
    const id = urlParams.get("id");
    const amount = urlParams.get("amount");

    if (type) redirectParams.type = type;
    if (id) redirectParams.id = id;
    if (amount) redirectParams.amount = amount;

    RedirectManager.toLogin(redirectParams);
  }

  /**
   * 从 URL 参数中获取重定向参数
   */
  static getRedirectParams(): RedirectParams {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectParams: RedirectParams = {};

    // 提取所有以 redirect_ 开头的参数
    urlParams.forEach((value, key) => {
      if (key.startsWith("redirect_")) {
        const paramName = key.replace("redirect_", "");
        redirectParams[paramName] = value;
      }
    });

    return redirectParams;
  }

  /**
   * 登录成功后执行重定向
   * 使用与 LoginManager.jumpPage() 相同的逻辑
   */
  static handleLoginSuccess(): void {
    const redirectParams = RedirectManager.getRedirectParams();
    const redirectType = redirectParams.type || "home";
    const redirectId = redirectParams.id || "";
    const redirectAmount = redirectParams.amount || "";

    // 使用与 LoginManager 相同的页面跳转逻辑
    switch (redirectType) {
      case REDIRECT_PAGE.game:
        // 游戏页面 - 跳转到首页
        router.replace("/home");
        break;
      case REDIRECT_PAGE.service:
        // 客服页面 - 跳转到账户页面
        router.replace("/account");
        break;
      case REDIRECT_PAGE.all:
        // 更多游戏 - 跳转到游戏分类页面
        router.replace(`/game-categories?categoryId=${redirectId}`);
        break;
      case REDIRECT_PAGE.promos:
        // 促销页面
        router.replace("/promos");
        break;
      case REDIRECT_PAGE.vip:
        // VIP页面 - 跳转到账户页面
        router.replace("/account");
        break;
      case REDIRECT_PAGE.news:
        // 新闻页面
        router.replace("/news");
        break;
      case REDIRECT_PAGE.banner:
        // Banner跳转 - 需要异步处理，暂时跳转到促销页面
        router.replace("/promos");
        break;
      case REDIRECT_PAGE.pay:
        // 支付页面 - 跳转到首页并携带支付参数
        if (redirectAmount) {
          router.replace(`/home?amount=${redirectAmount}`);
        } else {
          router.replace("/home");
        }
        break;
      case REDIRECT_PAGE.bet:
        // 投注页面 - 跳转到首页
        router.replace("/home");
        break;
      default:
        // 默认跳转到首页
        router.replace("/home");
        break;
    }

    // 跳转后清除重定向参数
    setTimeout(() => {
      RedirectManager.clearRedirectParams();
    }, 100);
  }

  /**
   * 清除 URL 中的重定向参数
   */
  static clearRedirectParams(): void {
    const url = new URL(window.location.href);
    const paramsToRemove: string[] = [];

    url.searchParams.forEach((_, key) => {
      if (key.startsWith("redirect_")) {
        paramsToRemove.push(key);
      }
    });

    paramsToRemove.forEach((param) => {
      url.searchParams.delete(param);
    });

    window.history.replaceState({}, document.title, url.toString());
  }

  /**
   * 构建带参数的跳转 URL
   * @param path 目标路径
   * @param params 参数对象
   */
  static buildUrlWithParams(path: string, params: RedirectParams): string {
    const urlParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        urlParams.set(key, value);
      }
    });

    return urlParams.toString() ? `${path}?${urlParams.toString()}` : path;
  }
}

// 导出便捷方法
export const redirectToLogin = RedirectManager.toLogin;
export const redirectToLoginWithCurrentParams = RedirectManager.toLoginWithCurrentParams;
export const handleLoginSuccessRedirect = RedirectManager.handleLoginSuccess;
