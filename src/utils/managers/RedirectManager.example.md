# RedirectManager 使用示例

## 基本用法

### 1. 跳转到登录页面并携带参数

```typescript
import { redirectToLogin } from "@/utils/RedirectManager";

// 跳转到登录页面，携带指定参数
redirectToLogin({
  type: "game",
  id: "123",
  amount: "100",
});

// 生成的 URL: /login?redirect_type=game&redirect_id=123&redirect_amount=100
```

### 2. 跳转到登录页面并携带当前页面的所有参数

```typescript
import { redirectToLoginWithCurrentParams } from "@/utils/RedirectManager";

// 当前页面 URL: /some-page?type=promos&id=456
// 跳转后的 URL: /login?redirect_type=promos&redirect_id=456
redirectToLoginWithCurrentParams();
```

### 3. 在登录成功后处理重定向

```typescript
import { handleLoginSuccessRedirect } from "@/utils/RedirectManager";

// 在登录成功的回调中调用
const onLoginSuccess = () => {
  // 其他登录成功逻辑...

  // 处理重定向
  handleLoginSuccessRedirect();
  // 如果登录页面 URL 是 /login?redirect_type=game&redirect_id=123
  // 将会跳转到 /home (游戏页面跳转到首页)

  // 如果登录页面 URL 是 /login?redirect_type=news
  // 将会跳转到 /news (新闻页面)

  // 如果登录页面 URL 是 /login?redirect_type=promos
  // 将会跳转到 /promos (促销页面)
};
```

## 高级用法

### 使用 RedirectManager 类的静态方法

```typescript
import { RedirectManager } from "@/utils/RedirectManager";

// 1. 跳转到登录页面
RedirectManager.toLogin({
  type: "vip",
  id: "premium",
});

// 2. 获取当前页面的重定向参数
const redirectParams = RedirectManager.getRedirectParams();
console.log(redirectParams); // { type: "game", id: "123" }

// 3. 构建带参数的 URL
const url = RedirectManager.buildUrlWithParams("/some-page", {
  type: "news",
  id: "latest",
});
console.log(url); // "/some-page?type=news&id=latest"

// 4. 清除 URL 中的重定向参数
RedirectManager.clearRedirectParams();
```

## 实际应用场景

### 场景 1: 游戏页面需要登录

```typescript
// 在游戏页面组件中
const playGame = (gameId: string) => {
  const globalStore = useGlobalStore();

  if (!globalStore.token) {
    // 用户未登录，跳转到登录页面并携带游戏信息
    redirectToLogin({
      type: "game",
      id: gameId,
    });
  } else {
    // 用户已登录，直接开始游戏
    startGame(gameId);
  }
};
```

### 场景 2: 支付页面需要登录

```typescript
// 在支付组件中
const handlePayment = (amount: string) => {
  const globalStore = useGlobalStore();

  if (!globalStore.token) {
    // 用户未登录，跳转到登录页面并携带支付信息
    redirectToLogin({
      type: "pay",
      amount: amount,
    });
  } else {
    // 用户已登录，直接处理支付
    processPayment(amount);
  }
};
```

### 场景 3: 从外部链接进入需要登录的页面

```typescript
// 在路由守卫中
router.beforeEach((to, from, next) => {
  const globalStore = useGlobalStore();

  // 检查是否需要登录的页面
  if (to.meta.requiresAuth && !globalStore.token) {
    // 提取当前路由的参数
    const redirectParams = {
      type: to.query.type as string,
      id: to.query.id as string,
      amount: to.query.amount as string,
    };

    // 跳转到登录页面
    redirectToLogin(redirectParams);
    return;
  }

  next();
});
```

## 参数说明

### RedirectParams 接口

```typescript
interface RedirectParams {
  type?: string; // 页面类型 (game, promos, vip, pay 等)
  id?: string; // 资源ID (游戏ID、促销ID等)
  amount?: string; // 金额 (支付金额等)
  [key: string]: string | undefined; // 其他自定义参数
}
```

### 支持的页面类型和跳转映射

| 类型      | 说明        | 跳转目标                           |
| --------- | ----------- | ---------------------------------- |
| `game`    | 游戏页面    | `/home`                            |
| `promos`  | 促销页面    | `/promos`                          |
| `vip`     | VIP 页面    | `/account`                         |
| `pay`     | 支付页面    | `/home` (携带 amount 参数)         |
| `news`    | 新闻页面    | `/news`                            |
| `service` | 客服页面    | `/account`                         |
| `banner`  | Banner 跳转 | `/promos`                          |
| `all`     | 更多游戏    | `/game-categories?categoryId={id}` |
| `bet`     | 投注页面    | `/home`                            |
| 其他      | 默认        | `/home`                            |

## 注意事项

1. 所有重定向参数都会以 `redirect_` 前缀存储在 URL 中
2. 登录成功后会自动清除重定向参数
3. 如果没有重定向参数，默认跳转到首页
4. 支持自定义参数，不限于预定义的类型
