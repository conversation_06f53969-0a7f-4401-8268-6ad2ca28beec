/**
 * VConsole 管理器
 * 统一管理移动端调试工具的初始化和配置
 */

interface VConsoleConfig {
  theme?: "light" | "dark";
  defaultPlugins?: ("network" | "element" | "storage" | "system")[];
  maxLogNumber?: number;
  onReady?: () => void;
  onClearLog?: () => void;
}

class VConsoleManager {
  private vConsoleInstance: any = null;
  private isInitialized = false;

  /**
   * 检查是否应该启用 VConsole
   */
  private shouldEnable(): boolean {
    // 优先检查环境变量控制
    if (import.meta.env.VITE_ENABLE_VCONSOLE === "true") {
      return true;
    }

    // 如果环境变量明确设置为 false，则不启用（即使是开发环境）
    if (import.meta.env.VITE_ENABLE_VCONSOLE === "false") {
      return false;
    }

    // 开发环境且未明确设置环境变量时才启用
    if (import.meta.env.DEV && import.meta.env.VITE_ENABLE_VCONSOLE === undefined) {
      return true;
    }

    // URL 参数控制：?vconsole=true
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get("vconsole") === "true") {
      return true;
    }

    // localStorage 控制（方便生产环境临时调试）
    if (localStorage.getItem("enable_vconsole") === "true") {
      return true;
    }

    return false;
  }

  /**
   * 初始化 VConsole
   */
  async initialize(config: VConsoleConfig = {}): Promise<void> {
    if (this.isInitialized) {
      console.warn("VConsole 已经初始化");
      return;
    }

    if (!this.shouldEnable()) {
      console.log("VConsole 未启用");
      return;
    }

    try {
      console.log("🔄 正在加载 VConsole...");

      // 动态导入 VConsole，避免在构建时包含
      const VConsole = await import("vconsole");

      const defaultConfig: VConsoleConfig = {
        theme: "light",
        // 只加载必要的插件，减少体积
        defaultPlugins: ["network", "element", "storage", "system"],
        maxLogNumber: 1000,
        ...config,
      };

      // 使用默认导出或命名导出
      const VConsoleClass = VConsole.default || VConsole;
      this.vConsoleInstance = new VConsoleClass(defaultConfig);
      this.isInitialized = true;

      // 在全局对象上暴露实例（仅开发环境）
      if (import.meta.env.DEV) {
        (window as any).vConsole = this.vConsoleInstance;
        (window as any).vConsoleManager = this;
      }

      console.log("✅ VConsole 初始化成功");

      // 添加一些有用的调试方法
      this.addDebugMethods();

      // 执行回调
      if (config.onReady) {
        config.onReady();
      }
    } catch (error) {
      console.error("❌ VConsole 初始化失败:", error);
      // 确保即使失败也不会阻塞应用
      this.isInitialized = false;
    }
  }

  /**
   * 添加调试方法到全局
   */
  private addDebugMethods(): void {
    // 添加快速切换主题的方法
    (window as any).toggleVConsoleTheme = () => {
      if (this.vConsoleInstance) {
        const currentTheme = this.vConsoleInstance.option.theme;
        const newTheme = currentTheme === "light" ? "dark" : "light";
        this.vConsoleInstance.setOption("theme", newTheme);
        console.log(`VConsole 主题已切换为: ${newTheme}`);
      }
    };

    // 添加清除日志的方法
    (window as any).clearVConsole = () => {
      if (this.vConsoleInstance) {
        this.vConsoleInstance.clearLog();
        console.log("VConsole 日志已清除");
      }
    };

    // 添加显示/隐藏的方法
    (window as any).toggleVConsole = () => {
      if (this.vConsoleInstance) {
        if (this.vConsoleInstance.isShow) {
          this.vConsoleInstance.hide();
        } else {
          this.vConsoleInstance.show();
        }
      }
    };
  }

  /**
   * 销毁 VConsole
   */
  destroy(): void {
    if (this.vConsoleInstance) {
      this.vConsoleInstance.destroy();
      this.vConsoleInstance = null;
      this.isInitialized = false;

      // 清理全局引用
      delete (window as any).vConsole;
      delete (window as any).vConsoleManager;
      delete (window as any).toggleVConsoleTheme;
      delete (window as any).clearVConsole;
      delete (window as any).toggleVConsole;

      console.log("VConsole 已销毁");
    }
  }

  /**
   * 获取 VConsole 实例
   */
  getInstance(): any {
    return this.vConsoleInstance;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && this.vConsoleInstance !== null;
  }

  /**
   * 动态启用 VConsole（用于生产环境临时调试）
   */
  async enableForDebug(): Promise<void> {
    localStorage.setItem("enable_vconsole", "true");
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  /**
   * 禁用 VConsole
   */
  disable(): void {
    localStorage.removeItem("enable_vconsole");
    this.destroy();
  }
}

// 导出单例
export const vConsoleManager = new VConsoleManager();

// 开发环境下提供快速访问
if (import.meta.env.DEV) {
  (window as any).vConsoleManager = vConsoleManager;
}
