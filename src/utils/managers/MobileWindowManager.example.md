# MobileWindowManager 使用示例

## 问题背景

移动端浏览器（特别是 iOS Safari 和 Android Chrome）对 `window.open()` 有严格的限制：

1. **同步限制**：只有在用户直接触发的事件中才能正常工作
2. **异步阻止**：在异步操作（如 API 请求）的回调中调用会被阻止
3. **弹窗阻止**：浏览器可能完全阻止弹窗

## 解决方案

MobileWindowManager 通过以下策略解决这些问题：

1. **预打开窗口**：在用户点击时立即打开空白窗口
2. **延迟导航**：API 响应后更新窗口 URL
3. **智能回退**：失败时自动使用备选方案

## 基本用法

### 1. 检测移动端设备

```typescript
import { MobileWindowManager } from "@/utils/MobileWindowManager";

// 检测是否为移动端
if (MobileWindowManager.isMobile()) {
  console.log("当前是移动端设备");
}

// 检测具体平台
if (MobileWindowManager.isIOS()) {
  console.log("当前是 iOS 设备");
}

if (MobileWindowManager.isAndroid()) {
  console.log("当前是 Android 设备");
}
```

### 2. 预打开窗口

```typescript
// 在用户点击事件中预打开窗口
const handleGameClick = async () => {
  // 立即预打开窗口（必须在用户事件中）
  const preOpenedWindow = MobileWindowManager.preOpenWindow();
  
  try {
    // 执行异步操作（API 请求等）
    const gameUrl = await fetchGameUrl();
    
    // 使用预打开的窗口导航
    const success = MobileWindowManager.navigateToUrl(gameUrl, preOpenedWindow);
    
    if (!success) {
      console.error("导航失败");
    }
  } catch (error) {
    // 出错时清理窗口
    MobileWindowManager.closeWindow(preOpenedWindow);
  }
};
```

### 3. 高级游戏启动方法

```typescript
// 使用封装好的游戏启动方法
const launchGame = () => {
  MobileWindowManager.launchGame(
    // 游戏 URL 提供者（异步函数）
    async () => {
      const response = await gameItemLogin(params);
      return response.game_url;
    },
    // 错误处理
    (error) => {
      console.error("游戏启动失败:", error);
      showToast("游戏启动失败，请重试");
    }
  );
};
```

## 在 JumpGame.ts 中的应用

### 修改前的问题

```typescript
// ❌ 问题代码：异步回调中的 window.open 会被移动端阻止
const goNext = async (data) => {
  const response = await gameItemLogin(params);
  
  // 这里会被移动端浏览器阻止
  window.open(response.game_url);
};
```

### 修改后的解决方案

```typescript
// ✅ 解决方案：预打开窗口 + 延迟导航
export const jumpGame = async (data) => {
  // 1. 在用户点击时立即预打开窗口
  let preOpenedWindow = null;
  if (MobileWindowManager.isMobile()) {
    preOpenedWindow = MobileWindowManager.preOpenWindow();
  }
  
  // 2. 执行异步操作
  KycMgr.instance.verifyKyc(InGameType.GoThirdGame, (isVerity) => {
    if (isVerity) {
      goNext(data, preOpenedWindow);
    } else {
      // 验证失败，清理窗口
      MobileWindowManager.closeWindow(preOpenedWindow);
    }
  });
};

const goNext = async (data, preOpenedWindow) => {
  try {
    const response = await gameItemLogin(params);
    
    // 3. 使用预打开的窗口导航
    const success = MobileWindowManager.navigateToUrl(
      response.game_url, 
      preOpenedWindow
    );
    
    if (!success) {
      showToast("游戏启动失败");
    }
  } catch (error) {
    // 4. 出错时清理窗口
    MobileWindowManager.closeWindow(preOpenedWindow);
  }
};
```

## 浏览器兼容性

### 支持的浏览器

- ✅ iOS Safari 12+
- ✅ Android Chrome 70+
- ✅ Android Firefox 68+
- ✅ 桌面浏览器（Chrome, Firefox, Safari, Edge）

### 回退策略

1. **预打开窗口失败** → 直接尝试 `window.open()`
2. **window.open() 失败** → 在当前窗口打开（移动端）
3. **所有方法失败** → 显示错误提示

## 调试工具

```typescript
// 获取浏览器信息
const browserInfo = MobileWindowManager.getBrowserInfo();
console.log(browserInfo);
/*
输出：
{
  userAgent: "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)...",
  isMobile: true,
  isIOS: true,
  isAndroid: false,
  supportsPopup: true
}
*/

// 检查窗口状态
const window = MobileWindowManager.preOpenWindow();
const isValid = MobileWindowManager.isWindowValid(window);
console.log("窗口是否有效:", isValid);
```

## 最佳实践

### 1. 用户体验优化

```typescript
// 显示加载状态
const preOpenedWindow = MobileWindowManager.preOpenWindow();
// 预打开的窗口会显示 "Loading Game..." 页面

// API 完成后立即导航
const success = MobileWindowManager.navigateToUrl(gameUrl, preOpenedWindow);
```

### 2. 错误处理

```typescript
const handleGameLaunch = async () => {
  const preOpenedWindow = MobileWindowManager.preOpenWindow();
  
  if (!preOpenedWindow && MobileWindowManager.isMobile()) {
    // 弹窗被阻止，提示用户
    showToast("请允许弹窗以启动游戏");
    return;
  }
  
  try {
    // 游戏启动逻辑
  } catch (error) {
    MobileWindowManager.closeWindow(preOpenedWindow);
    throw error;
  }
};
```

### 3. 性能优化

```typescript
// 避免不必要的预打开
const handleClick = () => {
  if (!MobileWindowManager.isMobile()) {
    // 桌面端直接使用 window.open
    launchGameDirectly();
  } else {
    // 移动端使用预打开策略
    launchGameWithPreOpen();
  }
};
```

## 注意事项

1. **必须在用户事件中调用**：`preOpenWindow()` 必须在用户直接触发的事件中调用
2. **及时清理窗口**：出错时要调用 `closeWindow()` 清理资源
3. **测试各种场景**：在不同设备和浏览器上测试功能
4. **提供回退方案**：为弹窗被阻止的情况提供备选方案
