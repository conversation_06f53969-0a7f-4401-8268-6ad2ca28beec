import { useGlobalStore } from "@/stores/global";
import { getLocalStorage, setLocalStorage } from "@/utils/core/Storage";
import router from "@/router";
import { TaskStatus, type WalletTaskItem } from "@/stores/rewardWallet";

// 存储键名
const BONUS_WALLET_STORAGE_KEY = "bonus_wallet_last_check";
const BONUS_WALLET_AUTO_REDIRECT_KEY = "bonus_wallet_auto_redirect_disabled";
const BONUS_WALLET_VISITED_KEY = "bonus_wallet_visited"; // 标记用户是否已访问过奖励钱包页面
const BONUS_WALLET_LAST_CONTENT_KEY = "bonus_wallet_last_content"; // 记录上次的奖金内容

// 检测结果接口
export interface BonusWalletCheckResult {
  hasContent: boolean; // 是否有内容
  hasAvailableBonus: boolean; // 是否有可领取的奖金
  totalCount: number; // 总数量
  availableCount: number; // 可领取数量
  bonusList: WalletTaskItem[]; // 奖金列表
}

/**
 * 奖金钱包管理器
 * 负责检测奖金钱包状态并处理自动跳转逻辑
 */
export class BonusWalletManager {
  private static instance: BonusWalletManager;
  private lastCheckData: BonusWalletCheckResult | null = null;
  private isChecking = false;

  private constructor() {}

  static getInstance(): BonusWalletManager {
    if (!BonusWalletManager.instance) {
      BonusWalletManager.instance = new BonusWalletManager();
    }
    return BonusWalletManager.instance;
  }

  /**
   * 检查奖金钱包状态
   * @param userId 用户ID
   * @param useCache 是否优先使用缓存数据
   * @returns 检测结果
   */
  async checkBonusWalletStatus(
    userId?: string | number,
    useCache: boolean = true
  ): Promise<BonusWalletCheckResult> {
    if (this.isChecking) {
      return this.lastCheckData || this.getEmptyResult();
    }

    try {
      this.isChecking = true;

      // 获取用户ID
      if (!userId) {
        const globalStore = useGlobalStore();
        userId = globalStore.userInfo?.user_id;
        if (!userId) {
          return this.getEmptyResult();
        }
      }

      let bonusList: WalletTaskItem[] = [];
      const { useRewardWalletStore } = await import("@/stores/rewardWallet");
      const rewardWalletStore = useRewardWalletStore();
      // 优先尝试从rewardWalletStore获取已缓存的数据
      if (useCache) {
        // 如果store中有数据且不是loading状态，直接使用
        if (rewardWalletStore.bonusList.length > 0 && !rewardWalletStore.loading) {
          bonusList = rewardWalletStore.bonusList;
        } else {
          console.log("优先尝试从rewardWalletStore获取已缓存的数据", bonusList);
          // 如果没有缓存数据，调用API获取
          const response = await rewardWalletStore.fetchBonusList(userId);
          bonusList = response?.data || [];
        }
      } else {
        // 强制从API获取数据
        const response = await rewardWalletStore.fetchBonusList(userId);
        bonusList = response?.data || [];
      }

      // 计算统计信息
      const totalCount = bonusList.length;
      const availableCount = bonusList.filter(
        (item) => item.task_status === TaskStatus.COMPLETED
      ).length;
      const hasContent = totalCount > 0;
      const hasAvailableBonus = availableCount > 0;

      const result: BonusWalletCheckResult = {
        hasContent,
        hasAvailableBonus,
        totalCount,
        availableCount,
        bonusList,
      };

      // 更新缓存
      this.lastCheckData = result;
      this.saveLastCheckData(result);

      return result;
    } catch (error) {
      return this.getEmptyResult();
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * 执行自动跳转到奖金钱包页面
   */
  async performAutoRedirect(): Promise<boolean> {
    try {
      // 检查是否禁用了自动跳转
      if (this.isAutoRedirectDisabled()) {
        return false;
      }

      // 检查是否在冷却期内，避免频繁跳转
      if (this.isInCooldown()) {
        return false;
      }

      // 检查当前路由，避免在某些页面进行跳转
      const currentRoute = router.currentRoute.value;
      const restrictedRoutes = ["/login", "/bonus-wallet", "/system/maintenance"];

      if (restrictedRoutes.some((route) => currentRoute.path.startsWith(route))) {
        return false;
      }

      // 执行跳转
      await router.push("/bonus-wallet");

      // 记录跳转时间，避免短时间内重复跳转
      this.recordRedirectTime();

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查是否应该执行自动跳转到奖金钱包
   * 逻辑：
   * 1. 首次进入首页：判断奖金钱包是否有内容，有则自动跳转，无则不跳转
   * 2. 后续每次进入首页：判断是否有新的奖金内容，有新的就跳转，没有则不跳转
   * @param userId 用户ID
   * @returns 是否应该跳转
   */
  async shouldPerformAutoRedirect(userId?: string | number): Promise<boolean> {
    const result = await this.checkBonusWalletStatus(userId);

    // 如果没有内容，不跳转
    if (!result.hasContent) {
      return false;
    }

    // 检查用户是否已经访问过奖励钱包页面
    const hasVisited = this.hasUserVisitedBonusWallet(userId);

    if (!hasVisited) {
      // 首次进入首页：有内容就跳转

      return true;
    } else {
      // 后续每次进入首页：只有在有新奖金时才跳转

      const hasNew = this.hasNewBonusContent(result, userId);

      return hasNew;
    }
  }

  /**
   * 检查是否应该执行首次自动跳转（保持向后兼容）
   * @deprecated 请使用 shouldPerformAutoRedirect 方法
   * @param userId 用户ID
   * @returns 是否应该跳转
   */
  async shouldPerformFirstTimeRedirect(userId?: string | number): Promise<boolean> {
    return this.shouldPerformAutoRedirect(userId);
  }

  /**
   * 禁用自动跳转（用户可以手动禁用）
   */
  disableAutoRedirect(): void {
    setLocalStorage(BONUS_WALLET_AUTO_REDIRECT_KEY, "true");
  }

  /**
   * 启用自动跳转
   */
  enableAutoRedirect(): void {
    setLocalStorage(BONUS_WALLET_AUTO_REDIRECT_KEY, "false");
  }

  /**
   * 检查自动跳转是否被禁用
   */
  private isAutoRedirectDisabled(): boolean {
    const disabled = getLocalStorage(BONUS_WALLET_AUTO_REDIRECT_KEY);
    return disabled === "true";
  }

  /**
   * 记录跳转时间
   */
  private recordRedirectTime(): void {
    setLocalStorage(`${BONUS_WALLET_STORAGE_KEY}_redirect_time`, Date.now().toString());
  }

  /**
   * 检查是否在冷却期内（避免频繁跳转）
   */
  private isInCooldown(): boolean {
    const lastRedirectTime = getLocalStorage(`${BONUS_WALLET_STORAGE_KEY}_redirect_time`);
    if (!lastRedirectTime) return false;

    const cooldownPeriod = 5 * 60 * 1000; // 5分钟冷却期
    return Date.now() - parseInt(lastRedirectTime) < cooldownPeriod;
  }

  /**
   * 保存最后检查的数据
   */
  private saveLastCheckData(data: BonusWalletCheckResult): void {
    try {
      setLocalStorage(
        BONUS_WALLET_STORAGE_KEY,
        JSON.stringify({
          ...data,
          timestamp: Date.now(),
        })
      );
    } catch (error) {}
  }

  /**
   * 获取空结果
   */
  private getEmptyResult(): BonusWalletCheckResult {
    return {
      hasContent: false,
      hasAvailableBonus: false,
      totalCount: 0,
      availableCount: 0,
      bonusList: [],
    };
  }

  /**
   * 清除缓存数据
   */
  clearCache(): void {
    this.lastCheckData = null;
    setLocalStorage(BONUS_WALLET_STORAGE_KEY, "");
  }

  /**
   * 获取当前缓存的检查结果
   */
  getCachedResult(): BonusWalletCheckResult | null {
    return this.lastCheckData;
  }

  /**
   * 强制刷新检查结果
   */
  async forceRefresh(userId?: string | number): Promise<BonusWalletCheckResult> {
    this.lastCheckData = null;
    return await this.checkBonusWalletStatus(userId);
  }

  /**
   * 标记用户已访问奖励钱包页面
   * @param userId 用户ID
   */
  markUserVisitedBonusWallet(userId?: string | number): void {
    const globalStore = useGlobalStore();
    const currentUserId = userId || globalStore.userInfo?.user_id;
    if (!currentUserId) return;

    const key = `${BONUS_WALLET_VISITED_KEY}_${currentUserId}`;
    setLocalStorage(key, "true");
  }

  /**
   * 检查用户是否已访问过奖励钱包页面
   * @param userId 用户ID
   * @returns 是否已访问过
   */
  private hasUserVisitedBonusWallet(userId?: string | number): boolean {
    const globalStore = useGlobalStore();
    const currentUserId = userId || globalStore.userInfo?.user_id;
    if (!currentUserId) return false;

    const key = `${BONUS_WALLET_VISITED_KEY}_${currentUserId}`;
    const visited = getLocalStorage(key);
    return !!visited;
  }

  /**
   * 检查是否有新的奖金内容
   * 比较当前奖金数据与上次保存的数据，判断是否有新增奖金
   * @param currentResult 当前检测结果
   * @param userId 用户ID
   * @returns 是否有新内容
   */
  private hasNewBonusContent(
    currentResult: BonusWalletCheckResult,
    userId?: string | number
  ): boolean {
    const globalStore = useGlobalStore();
    const currentUserId = userId || globalStore.userInfo?.user_id;
    if (!currentUserId) {
      return false;
    }

    const key = `${BONUS_WALLET_LAST_CONTENT_KEY}_${currentUserId}`;
    const lastContent = getLocalStorage(key);

    if (!lastContent) {
      // 如果没有历史记录，说明是第一次检查，保存当前内容并返回false（不跳转）

      this.saveLastBonusContent(currentResult, userId);
      return false;
    }

    try {
      // getLocalStorage已经自动解析了JSON，直接使用
      // 比较奖金总数量和可领取数量，任一增加都认为有新内容
      const hasNewContent =
        currentResult.totalCount > lastContent.totalCount ||
        currentResult.availableCount > lastContent.availableCount;

      // 无论是否有新内容，都更新记录以保持数据同步
      this.saveLastBonusContent(currentResult, userId);

      if (hasNewContent) {
      } else {
      }

      return hasNewContent;
    } catch (error) {
      // 比较失败时，保存当前内容并返回false
      this.saveLastBonusContent(currentResult, userId);
      return false;
    }
  }

  /**
   * 保存当前奖金内容记录
   * @param result 检测结果
   * @param userId 用户ID
   */
  private saveLastBonusContent(result: BonusWalletCheckResult, userId?: string | number): void {
    const globalStore = useGlobalStore();
    const currentUserId = userId || globalStore.userInfo?.user_id;
    if (!currentUserId) return;

    const key = `${BONUS_WALLET_LAST_CONTENT_KEY}_${currentUserId}`;
    const contentRecord = {
      totalCount: result.totalCount,
      availableCount: result.availableCount,
      timestamp: Date.now(),
    };

    setLocalStorage(key, contentRecord);
  }

  /**
   * 保存当前奖金内容（公共方法，供页面调用）
   * @param bonusList 奖金列表
   * @param availableCount 可领取数量
   * @param userId 用户ID
   */
  saveCurrentBonusContent(
    bonusList: WalletTaskItem[],
    availableCount: number,
    userId?: string | number
  ): void {
    const result: BonusWalletCheckResult = {
      hasContent: bonusList.length > 0,
      hasAvailableBonus: availableCount > 0,
      totalCount: bonusList.length,
      availableCount: availableCount,
      bonusList: bonusList,
    };

    this.saveLastBonusContent(result, userId);
  }

  /**
   * 重置用户访问状态（用于测试或特殊情况）
   * @param userId 用户ID
   */
  resetUserVisitStatus(userId?: string | number): void {
    const globalStore = useGlobalStore();
    const currentUserId = userId || globalStore.userInfo?.user_id;
    if (!currentUserId) return;

    const visitedKey = `${BONUS_WALLET_VISITED_KEY}_${currentUserId}`;
    const contentKey = `${BONUS_WALLET_LAST_CONTENT_KEY}_${currentUserId}`;

    setLocalStorage(visitedKey, "");
    setLocalStorage(contentKey, "");
  }

  /**
   * 获取用户访问状态信息
   * @param userId 用户ID
   * @returns 包含用户ID、访问状态和上次内容记录的对象
   */
  getUserStatus(userId?: string | number): {
    userId: string;
    hasVisited: boolean;
    lastContentStr: string;
  } {
    const globalStore = useGlobalStore();
    const currentUserId = (userId || globalStore.userInfo?.user_id || "unknown").toString();

    // 检查访问状态
    const visitedKey = `${BONUS_WALLET_VISITED_KEY}_${currentUserId}`;
    const hasVisited = getLocalStorage(visitedKey) === "true";

    // 检查上次内容记录
    const contentKey = `${BONUS_WALLET_LAST_CONTENT_KEY}_${currentUserId}`;
    const content = getLocalStorage(contentKey);
    let lastContentStr = "无记录";

    if (content) {
      try {
        // 如果content已经是对象，直接使用；如果是字符串，尝试解析
        const parsedContent = typeof content === "string" ? JSON.parse(content) : content;
        lastContentStr = JSON.stringify(parsedContent, null, 2);
      } catch (error) {
        lastContentStr = `解析失败: ${content}`;
      }
    }

    return {
      userId: currentUserId,
      hasVisited,
      lastContentStr,
    };
  }

  /**
   * 调试方法：重置所有用户的访问状态（仅用于开发和测试）
   */
  resetAllUserStatus(): void {
    const keys = Object.keys(localStorage);
    const bonusWalletKeys = keys.filter(
      (key) => key.includes(BONUS_WALLET_VISITED_KEY) || key.includes(BONUS_WALLET_LAST_CONTENT_KEY)
    );

    bonusWalletKeys.forEach((key) => {
      localStorage.removeItem(key);
    });
  }

  /**
   * 调试方法：获取所有用户的访问状态
   */
  getAllUserStatus(): Array<{ key: string; value: any }> {
    const keys = Object.keys(localStorage);
    const bonusWalletKeys = keys.filter(
      (key) => key.includes(BONUS_WALLET_VISITED_KEY) || key.includes(BONUS_WALLET_LAST_CONTENT_KEY)
    );

    return bonusWalletKeys.map((key) => ({
      key,
      value: getLocalStorage(key),
    }));
  }
}

// 导出单例实例
export const bonusWalletManager = BonusWalletManager.getInstance();
