/**
 * 移动端窗口管理器
 * 解决移动端浏览器 window.open() 被阻止的问题
 */

export class MobileWindowManager {
  /**
   * 检测是否为移动端设备
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    );
  }

  /**
   * 检测是否为 iOS 设备
   */
  static isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  /**
   * 检测是否为 Android 设备
   */
  static isAndroid(): boolean {
    return /Android/i.test(navigator.userAgent);
  }

  /**
   * 在用户交互事件中预打开窗口
   * 这样可以避免移动端浏览器的弹窗阻止
   */
  static preOpenWindow(): Window | null {
    if (!this.isMobile()) {
      // 非移动端不需要预打开
      return null;
    }

    try {
      const newWindow = window.open("about:blank", "_blank");
      if (!newWindow) {
        console.warn("Popup blocked by browser");
        return null;
      }

      // 设置一个加载页面，避免空白页面
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Loading...</title>
          <style>
            body {
              margin: 0;
              padding: 0;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              font-family: Arial, sans-serif;
              background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
              background: #000;
              color: white;
            }
            .loading {
              text-align: center;
            }
            .spinner {
              border: 4px solid rgba(255,255,255,0.3);
              border-radius: 50%;
              border-top: 4px solid white;
              width: 40px;
              height: 40px;
              animation: spin 1s linear infinite;
              margin: 0 auto 20px;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="loading">
            <div class="spinner"></div>
            <div>Loading ...</div>
          </div>
        </body>
        </html>
      `);
      newWindow.document.close();

      return newWindow;
    } catch (error) {
      console.warn("Failed to pre-open window:", error);
      return null;
    }
  }

  /**
   * 安全地导航到指定 URL
   * 如果在移动端且没有提供预打开窗口，会尝试自动创建一个
   * 优先使用预打开的窗口，失败时回退到其他方法
   */
  static navigateToUrl(url: string, preOpenedWindow?: Window | null): boolean {
    if (!url) {
      console.error("URL is required");
      return false;
    }

    // 如果是移动端且没有预打开窗口，尝试自动创建一个
    // 注意：这只在用户直接触发的事件中有效
    let autoCreatedWindow: Window | null = null;
    if (this.isMobile() && !preOpenedWindow) {
      try {
        autoCreatedWindow = window.open("about:blank", "_blank");
        if (autoCreatedWindow) {
          preOpenedWindow = autoCreatedWindow;
        }
      } catch (error) {
        console.warn("Failed to auto-create window:", error);
      }
    }

    // 如果有预打开的窗口且未关闭，使用它
    if (preOpenedWindow && !preOpenedWindow.closed) {
      try {
        preOpenedWindow.location.href = url;
        return true;
      } catch (error) {
        console.warn("Failed to navigate pre-opened window:", error);
        preOpenedWindow.close();
      }
    }

    // 尝试直接打开新窗口
    try {
      const newWindow = window.open(url, "_blank");
      if (newWindow) {
        return true;
      }
    } catch (error) {
      console.warn("Failed to open new window:", error);
    }

    // 移动端回退方案：在当前窗口打开
    if (this.isMobile()) {
      console.warn("Falling back to current window navigation");
      window.location.href = url;
      return true;
    }

    console.error("All navigation methods failed");
    return false;
  }

  /**
   * 安全地关闭窗口
   */
  static closeWindow(windowRef: Window | null): void {
    if (windowRef && !windowRef.closed) {
      try {
        windowRef.close();
      } catch (error) {
        console.warn("Failed to close window:", error);
      }
    }
  }

  /**
   * 检查窗口是否仍然有效
   */
  static isWindowValid(windowRef: Window | null): boolean {
    return windowRef !== null && !windowRef.closed;
  }

  /**
   * 为移动端优化的游戏启动方法
   */
  static async launchGame(
    gameUrlProvider: () => Promise<string>,
    onError?: (error: Error) => void
  ): Promise<void> {
    let preOpenedWindow: Window | null = null;

    try {
      // 在移动端预打开窗口
      if (this.isMobile()) {
        preOpenedWindow = this.preOpenWindow();
      }

      // 获取游戏 URL
      const gameUrl = await gameUrlProvider();

      // 导航到游戏 URL
      const success = this.navigateToUrl(gameUrl, preOpenedWindow);

      if (!success) {
        throw new Error("Failed to navigate to game URL");
      }
    } catch (error) {
      // 清理预打开的窗口
      this.closeWindow(preOpenedWindow);

      const gameError = error instanceof Error ? error : new Error("Unknown error");
      console.error("Game launch failed:", gameError);

      if (onError) {
        onError(gameError);
      }
    }
  }

  /**
   * 获取浏览器信息（用于调试）
   */
  static getBrowserInfo(): {
    userAgent: string;
    isMobile: boolean;
    isIOS: boolean;
    isAndroid: boolean;
    supportsPopup: boolean;
  } {
    return {
      userAgent: navigator.userAgent,
      isMobile: this.isMobile(),
      isIOS: this.isIOS(),
      isAndroid: this.isAndroid(),
      supportsPopup: !!window.open,
    };
  }
}

// 导出便捷方法
export const isMobile = MobileWindowManager.isMobile;
export const preOpenWindow = MobileWindowManager.preOpenWindow;
export const navigateToUrl = MobileWindowManager.navigateToUrl;
export const launchGame = MobileWindowManager.launchGame;
