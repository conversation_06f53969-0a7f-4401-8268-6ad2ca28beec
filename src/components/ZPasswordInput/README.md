# ZPasswordInput 聚焦时机优化

## 问题描述
在弹窗切换时，如果在 `onMounted` 时立即聚焦输入框，会导致键盘弹出造成页面跳动。

## 解决方案

### 1. 使用组件属性控制（推荐）

```vue
<template>
  <!-- 默认自动聚焦，延迟300ms -->
  <ZPasswordInput v-model="password" @complete="handleComplete" />
  
  <!-- 禁用自动聚焦 -->
  <ZPasswordInput 
    v-model="password" 
    :autoFocus="false" 
    @complete="handleComplete" 
  />
  
  <!-- 自定义延迟时间 -->
  <ZPasswordInput 
    v-model="password" 
    :focusDelay="500" 
    @complete="handleComplete" 
  />
</template>
```

### 2. 手动控制聚焦时机

```vue
<template>
  <ZPasswordInput 
    ref="pwdRef"
    v-model="password" 
    :autoFocus="false"
    @complete="handleComplete" 
  />
</template>

<script setup>
import { ref, watch } from 'vue'

const pwdRef = ref()
const showDialog = ref(false)

// 监听弹窗显示状态
watch(showDialog, (newVal) => {
  if (newVal) {
    // 方案1: 固定延迟
    setTimeout(() => {
      pwdRef.value?.focusInput()
    }, 300)
    
    // 方案2: 使用智能聚焦
    setTimeout(() => {
      pwdRef.value?.delayedFocus()
    }, 100)
  }
})
</script>
```

### 3. 使用聚焦管理器（最佳实践）

```vue
<script setup>
import { FocusManager } from '@/utils/FocusManager'

const focusManager = FocusManager.getInstance()

// 在弹窗切换开始时通知管理器
const openDialog = () => {
  focusManager.startTransition(400) // 切换持续时间
  showDialog.value = true
}

// 组件会自动使用智能聚焦
</script>
```

## 最佳聚焦时机

### 1. 弹窗动画完成后 (推荐)
```javascript
// 等待弹窗动画完成 (通常300-400ms)
setTimeout(() => {
  inputRef.value?.focus()
}, 350)
```

### 2. 使用 nextTick + 延迟
```javascript
import { nextTick } from 'vue'

nextTick(() => {
  setTimeout(() => {
    inputRef.value?.focus()
  }, 200)
})
```

### 3. 监听 CSS 动画事件
```javascript
// 监听弹窗动画结束
dialogElement.addEventListener('animationend', () => {
  inputRef.value?.focus()
})
```

### 4. 使用 Intersection Observer
```javascript
// 监听元素可见性
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      setTimeout(() => {
        inputRef.value?.focus()
      }, 100)
    }
  })
})
```

## 组件 API

### Props
- `autoFocus`: boolean - 是否自动聚焦，默认 true
- `focusDelay`: number - 聚焦延迟时间(ms)，默认 300
- `maxLength`: number - 密码长度，默认 6
- `errInput`: boolean - 错误状态，默认 false

### Methods
- `focusInput()`: 立即聚焦
- `delayedFocus()`: 智能延迟聚焦

### Events
- `update:modelValue`: 密码值变化
- `complete`: 密码输入完成

## 使用建议

1. **普通场景**: 使用默认的 `autoFocus` 和 `focusDelay`
2. **弹窗切换**: 使用 `FocusManager` 管理聚焦时机
3. **复杂动画**: 手动控制聚焦，在动画完成后调用
4. **性能敏感**: 禁用自动聚焦，按需手动聚焦

## 注意事项

- iOS Safari 对自动聚焦有限制，可能需要用户交互触发
- 在快速切换弹窗时，建议使用聚焦管理器避免冲突
- 延迟时间应该略大于弹窗动画时间，确保动画完成后再聚焦
