<template>
  <div class="password-input" @click="focusInput">
    <!-- 隐藏的真实输入框 -->
    <input
      ref="inputRef"
      type="number"
      inputmode="numeric"
      :maxlength="maxLength"
      v-model="password"
      @input="handleInput"
      @focus="isFocused = true"
      @blur="isFocused = false"
      class="hidden-input"
    />
    <!-- 显示的密码格子 -->
    <div class="password-boxes" :class="{ 'err-input': errInput }">
      <div
        v-for="(box, index) in displayBoxes"
        :key="index"
        class="password-box"
        :class="{ active: isFocused && index === password.length }"
      >
        <div class="dot" v-if="password[index]"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, defineProps, defineEmits } from "vue";
import { useSmartFocus } from "@/utils/managers/FocusManager";

const props = defineProps({
  maxLength: {
    type: Number,
    default: 6,
  },
  errInput: {
    type: Boolean,
    default: false,
  },
  autoFocus: {
    type: Boolean,
    default: true,
  },
  focusDelay: {
    type: Number,
    default: 300, // 默认延迟300ms聚焦，避免弹窗切换时的页面跳动
  },
  // 监听弹窗显示状态，用于重新聚焦
  visible: {
    type: Boolean,
    default: undefined, // undefined表示不监听，true/false表示监听状态变化
  },
});

const emit = defineEmits(["update:modelValue", "complete"]);

const password = ref("");
const isFocused = ref(false);
const inputRef = ref<HTMLInputElement | null>(null);

// 使用智能聚焦管理
const { smartFocus } = useSmartFocus(props.autoFocus, props.focusDelay);

// 生成密码格子的数组，下标 0 ～ maxLength - 1
const displayBoxes = computed(() => Array.from({ length: props.maxLength }, (_, i) => i));

// 处理输入时只允许数字，超出长度则截断
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const value = target.value.replace(/[^0-9]/g, "").slice(0, props.maxLength);
  password.value = value;
  emit("update:modelValue", value);
  if (value.length === props.maxLength) {
    emit("complete", value);
  }
};

const focusInput = () => {
  inputRef.value?.focus();
};

// 智能延迟聚焦，避免弹窗切换时的页面跳动
const delayedFocus = () => {
  smartFocus(() => {
    focusInput();
  });
};

// 监听弹窗显示状态变化
watch(
  () => props.visible,
  (newVal, oldVal) => {
    // 当弹窗从隐藏变为显示时，重新聚焦
    if (newVal === true && oldVal === false) {
      nextTick(() => {
        delayedFocus();
      });
    }
  }
);

// 重置密码并聚焦的方法
const resetAndFocus = () => {
  password.value = "";
  delayedFocus();
};

onMounted(() => {
  // 首次挂载时聚焦（如果没有传入visible属性或visible为true）
  if (props.visible === undefined || props.visible === true) {
    delayedFocus();
  }
});

defineExpose({ focusInput, delayedFocus, resetAndFocus });
</script>

<style scoped lang="scss">
.password-input {
  width: 100%;
  display: flex;
  justify-content: center;
  user-select: none;
  cursor: pointer;
  position: relative;
}

/* 隐藏真实输入框 */
.hidden-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  width: 0;
  height: 0;
}

.password-boxes {
  display: flex;
  gap: clamp(8px, 2vw, 16px);
  width: 100%;
  justify-content: center;
  max-width: 400px; // 限制最大宽度，避免在大屏幕上过大
  margin: 0 auto;

  .password-box {
    // 根据父级宽度自适应，保持正方形
    flex: 1;
    aspect-ratio: 1;
    min-width: 28px;
    max-width: 56px;
    border: 1px solid #e5e5e5;
    border-radius: clamp(6px, 1.5vw, 12px);
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: border-color 0.3s ease;

    &.active {
      /* 当输入框获得焦点并聚焦于当前空格时，激活该输入框样式 */
      border-color: #fac08e;
      box-shadow: 0 0 0 2px rgba(250, 192, 142, 0.2);
    }

    .dot {
      width: clamp(8px, 2.5vw, 12px);
      height: clamp(8px, 2.5vw, 12px);
      border-radius: 50%;
      background: #333;
    }
  }

  &.err-input {
    .password-box {
      border-color: #e5110a;

      &.active {
        box-shadow: 0 0 0 2px rgba(229, 17, 10, 0.2);
      }
    }
  }
}
</style>
