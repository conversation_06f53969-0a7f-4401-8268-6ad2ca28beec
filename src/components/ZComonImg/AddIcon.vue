<!-- 添加 -->
<template>
  <div :class="{ 'btn': true, 'disabled': disabled }">
    <ZIcon type="icon-jia"></ZIcon>
  </div>
</template>
<script setup lang="ts">

import { defineProps } from 'vue';


const props = defineProps({
  // 背景高度
  disabled: {
    type: Boolean,
    required: false,
    default: false,
  },
});

</script>
<style lang="scss" scoped>
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 999px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  background-color: var(--red-color);
  width: 72px;
  height: 48px;

  &.disabled {
    opacity: 0.2;
  }
}
</style>
