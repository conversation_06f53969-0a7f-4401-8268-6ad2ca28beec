<!-- 选中、未选中状态图标组件 -->
<template>
  <div
    class="checked-unchecked-icon"
    :class="{ 'is-checked': isChecked }"
    :style="iconWrapperStyle"
  >
    <ZIcon :type="iconConfig.type" :color="iconConfig.color" :size="size" />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

// 颜色常量
const COLORS = {
  MAYA: "#00A74C",
  GCASH: "#004CDD",
  DEFAULT: "#00A74C",
};

// 图标类型常量
const ICON_TYPES = {
  CHECKED: "icon-xuanzhong2",
  UNCHECKED: "icon-xuanze-weixuanzhong",
} as const;

interface Props {
  /** 图标大小 */
  size?: number;
  /** 是否选中状态 */
  isChecked?: boolean;
  /** 类型，用于确定颜色主题 */
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  isChecked: false,
  type: "",
});

// 计算图标配置
const iconConfig = computed(() => {
  const iconType = props.isChecked ? ICON_TYPES.CHECKED : ICON_TYPES.UNCHECKED;

  // 根据类型确定颜色
  let color = COLORS.DEFAULT;

  if (props.type?.toLocaleLowerCase() === "maya") {
    color = COLORS.MAYA;
  } else if (props.type?.toLocaleLowerCase() === "gcash") {
    color = COLORS.GCASH;
  }

  return {
    type: iconType,
    color,
  };
});

// 计算包装器样式
const iconWrapperStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
}));
</script>

<style lang="scss" scoped>
.checked-unchecked-icon {
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  // 可以添加一些交互效果
  &.is-checked {
    transform: scale(1.05);
  }

  // 悬停效果（如果需要）
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
