<!-- 提现/充值类型图标组件 -->
<template>
  <div
    class="withdraw-type-icon"
    :class="{
      'has-icon': hasValidIcon,
      'fallback-icon': !hasValidIcon,
      loading: finalIcon && !imageLoaded && !imageLoadError,
    }"
    :style="containerStyle"
  >
    <!-- 有效图标时显示图片 -->
    <van-image
      v-if="hasValidIcon && imageLoaded"
      :src="finalIcon"
      :width="size"
      :height="size"
      fit="contain"
      class="icon-image"
      :alt="iconAltText"
    >
      <template #error>
        <div class="error-fallback">
          <!-- <ZIcon type="icon-qianbao1" color="#ccc" :size="size * 0.6" /> -->
          <img v-if="fallbackIcon" :src="fallbackIcon" :style="imageStyle" :alt="iconAltText" />
          <ZIcon v-else type="icon-qianbao1" color="#999" :size="size * 0.6" />
        </div>
      </template>
    </van-image>

    <!-- 加载中状态 -->
    <div v-else-if="finalIcon && !imageLoaded && !imageLoadError" class="loading-placeholder">
      <ZIcon type="icon-qianbao1" color="#ccc" :size="size * 0.6" />
    </div>

    <!-- 加载失败或无图标时显示备用图标或默认图标 -->
    <div v-else class="default-icon">
      <ZIcon type="icon-qianbao1" color="#999" :size="size * 0.6" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import GCashLogo from "@/assets/images/gcash_icon.png";
import MayaLogo from "@/assets/images/maya_icon.png";
import { getServerSideImageUrl } from "@/utils/core/tools";

// 类型定义
interface Props {
  /** 背景颜色 */
  backgroundColor?: string;
  /** 图标宽度 */
  width?: number;
  /** 图标高度 */
  height?: number;
  /** 自定义图标URL */
  icon?: string;
  /** 类型标识 */
  type?: string;
}

const props = withDefaults(defineProps<Props>(), {
  backgroundColor: "#fff",
  width: 36,
  height: 36,
  icon: "",
  type: "",
});

// 内置图标映射
const BUILT_IN_LOGOS: Record<string, string> = {
  gcash: GCashLogo,
  maya: MayaLogo,
};

// 计算属性
const size = computed(() => Math.max(props.width, props.height));

const containerStyle = computed(() => ({
  background: props.backgroundColor,
  width: `${props.width}px`,
  height: `${props.height}px`,
}));

const imageStyle = computed(() => ({
  width: `${props.width}px`,
  height: `${props.height}px`,
}));

// 获取最终图标URL
const finalIcon = computed(() => {
  // 优先使用自定义图标
  if (props.icon) {
    return getServerSideImageUrl(props.icon);
  }

  // 使用内置图标
  if (props.type && BUILT_IN_LOGOS[props.type.toLocaleLowerCase()]) {
    return BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()];
  }

  return "";
});

// 获取备用图标
const fallbackIcon = computed(() => {
  return props.type && BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()]
    ? BUILT_IN_LOGOS[props.type?.toLocaleLowerCase()]
    : "";
});

// 图片加载状态
const imageLoadError = ref(false);
const imageLoaded = ref(false);

// 检查图片是否可以加载
const checkImageLoad = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

// 监听图片URL变化，重新检查加载状态
watch(
  finalIcon,
  async (newUrl) => {
    imageLoadError.value = false;
    imageLoaded.value = false;

    if (newUrl) {
      const canLoad = await checkImageLoad(newUrl);
      imageLoaded.value = canLoad;
      imageLoadError.value = !canLoad;
    }
  },
  { immediate: true }
);

// 是否有有效图标（URL存在且能够加载）
const hasValidIcon = computed(() => {
  return Boolean(finalIcon.value) && !imageLoadError.value;
});

// 图标描述文本
const iconAltText = computed(() => {
  return props.type ? `${props.type} logo` : "Payment method logo";
});
</script>

<style lang="scss" scoped>
.withdraw-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  transition: all 0.2s ease;

  &.has-icon {
    background: #fff;
  }

  &.fallback-icon {
    background-color: #f5f5f5;
  }

  &.loading {
    background-color: #f8f9fa;

    .loading-placeholder {
      animation: pulse 1.5s ease-in-out infinite;
    }
  }

  // 悬停效果
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.icon-image {
  display: block;
  border-radius: 50%;
}

.loading-placeholder,
.error-fallback,
.default-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.loading-placeholder {
  background-color: #f8f9fa;
}

.error-fallback img {
  border-radius: 50%;
}

// 脉冲动画
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
</style>
