<template>
  <div class="page-container">
    <header class="page-header" v-if="calcShowNarBar" :style="narBarStyle">
      <div class="nav-bar">
        <div class="left" @click="handleBack">
          <ZIcon
            type="icon-fanhui1"
            v-show="showNarBack"
            :color="narBarStyle?.color || '#000'"
            :size="28"
          ></ZIcon>
        </div>
        <div class="center">
          {{ title || $route.meta.title }}
        </div>
        <div class="right">
          <slot name="right"></slot>
        </div>
      </div>
    </header>
    <main
      class="page-content"
      :style="{
        paddingTop: calcShowNarBar
          ? 'calc(44px + env(safe-area-inset-top, 0px))'
          : 'env(safe-area-inset-top, 0px)',
        backgroundColor: backgroundColor,
      }"
    >
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <ZLoading />
      </div>

      <!-- 异常状态 -->
      <ExceptionRefresh v-else-if="hasError" @refresh="handleExceptionRefresh" />
      <!-- 正常内容 -->
      <slot v-else></slot>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { CSSProperties } from "@vue/runtime-dom";
import ExceptionRefresh from "@/components/ExceptionRefresh.vue";

// 接收父组件传入的标题和异步请求函数
const props = defineProps({
  // 是否显示顶部导航
  showNarBar: {
    type: Boolean,
    default: true,
    required: false,
  },
  // 顶部导航样式
  narBarStyle: {
    type: Object as () => CSSProperties,
    default: () => ({}),
    required: false,
  },
  // 顶部导航返回
  showNarBack: {
    type: Boolean,
    default: true,
    required: false,
  },
  backgroundColor: {
    type: String,
    required: false,
    default: "#fff",
  },
  // 页面请求
  request: {
    type: Function,
    required: false,
  },
  title: {
    type: String,
    required: false,
    default: "",
  },
  onBack: {
    type: Function,
    required: false,
  },
  // 异常处理相关 props
  enableExceptionHandling: {
    type: Boolean,
    default: true,
    required: false,
  },
  // 自定义错误过滤器
  errorFilter: {
    type: Function,
    required: false,
  },
});

// 内部维护的状态
const loading = ref(true);
const hasError = ref(false);
const errorInfo = ref<any>(null);
const route = useRoute();
const router = useRouter();

const calcShowNarBar = computed(() => {
  return props.showNarBar && (route.meta.title || props.title);
});

const handleBack = () => {
  if (props.onBack) {
    props.onBack();
    return;
  }
  router.back();
};

// 默认错误过滤器
const defaultErrorFilter = (error: any) => {
  // 网络错误、超时错误显示 ExceptionRefresh
  return (
    error?.code === "NETWORK_ERROR" ||
    error?.code === "TIMEOUT" ||
    error?.message?.includes("timeout") ||
    error?.message?.includes("network") ||
    error?.status >= 500
  );
};

// 异步请求数据
const fetchData = async () => {
  try {
    // 重置错误状态
    hasError.value = false;
    errorInfo.value = null;

    if (props.request) {
      // 确保 loading 至少显示 300ms，避免闪烁（特别是 iOS）
      const startTime = Date.now();
      await props.request(); // 调用传入的异步请求函数

      const elapsed = Date.now() - startTime;
      const minLoadingTime = 300; // 最小显示时间

      if (elapsed < minLoadingTime) {
        await new Promise((resolve) => setTimeout(resolve, minLoadingTime - elapsed));
      }
    } else {
      // 如果没有请求函数，也显示一小段时间确保用户能看到 loading
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  } catch (error: any) {
    console.error("ZPage request failed:", error);

    // 保存错误信息
    errorInfo.value = error;

    // 判断是否显示异常页面
    if (props.enableExceptionHandling) {
      const errorFilter = props.errorFilter || defaultErrorFilter;
      if (errorFilter(error)) {
        hasError.value = true;
      }
    }
  } finally {
    loading.value = false; // 请求完成后隐藏 loading
  }
};

// 处理异常刷新
const handleExceptionRefresh = async () => {
  await fetchData();
};

// 组件挂载时执行请求
onMounted(() => {
  fetchData();
});

// 计算并设置 --vh 变量
const setVhVariable = () => {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty("--vh", `${vh}px`);
};

onMounted(() => {
  // 初始设置
  setVhVariable();
  // 监听窗口大小变化
  window.addEventListener("resize", setVhVariable);
});

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener("resize", setVhVariable);
});

// 暴露方法给父组件
defineExpose({
  // 手动刷新数据
  refresh: fetchData,
  // 手动设置错误状态
  setError: (error: any) => {
    errorInfo.value = error;
    hasError.value = true;
    loading.value = false;
  },
  // 重置状态
  reset: () => {
    hasError.value = false;
    errorInfo.value = null;
    loading.value = false;
  },
  // 获取当前状态
  getState: () => ({
    loading: loading.value,
    hasError: hasError.value,
    errorInfo: errorInfo.value,
  }),
});
</script>

<style scoped lang="scss">
.page-container {
  width: 100%;
  position: relative;

  /* 兼容性方案 */
  height: 100vh;
  /* 基础方案 */
  height: calc(var(--vh, 1vh) * 100);
  /* JavaScript 辅助方案 */
  height: 100dvh;
  /* 现代浏览器方案 */

  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 44px;
    background: #fff;
    display: flex;
    align-items: center;
    // box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
    color: #222;
    text-align: center;

    /* 导航栏标题 */
    font-family: Inter;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: capitalize;
    padding-top: constant(safe-area-inset-top); /* iOS < 11.2 */
    padding-top: env(safe-area-inset-top); /* iOS >= 11.2 */
    padding-left: constant(safe-area-inset-left);
    padding-left: env(safe-area-inset-left);
    padding-right: constant(safe-area-inset-right);
    padding-right: env(safe-area-inset-right);

    .nav-bar {
      width: 100%;
      padding: 0 16px;
      box-sizing: border-box;
      display: flex;
      align-items: center;

      .left {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
      }

      .center {
        flex: 1;
        text-align: center;
        // font-size: 20px;
        font-size: 18px;
        font-weight: 500;
        font-family: Inter;
      }

      .right {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }

  .page-content {
    flex: 1;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    -webkit-overflow-scrolling: touch;
    box-sizing: border-box;
    // background-color: #f3f6f9;
    background-color: #fff;
    padding-bottom: constant(safe-area-inset-bottom); ///兼容 IOS<11.2/
    padding-bottom: env(safe-area-inset-bottom); ///兼容 IOS>11.2/
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 10;
  // iOS Safari 兼容性
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  // 确保在 iOS 上正确显示
  min-height: 200px;
}
</style>
