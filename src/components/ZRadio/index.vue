<template>
  <label class="z-radio-label" :class="{ 'is-checked': isChecked, 'is-disabled': disabled }">
    <!-- 原生 input 使用 value prop 作为实际值 -->
    <input type="radio" :name="radioGroup?.name || uniqueName" :value="value" :checked="isChecked" :disabled="disabled"
      @change="handleChange" class="sr-only" />
    <div class="wrap">
      <!-- 显示 label prop 作为文本 -->
      <span class="z-radio-label-text">{{ label }}</span>
      <span class="z-radio-circle">
        <span class="z-radio-dot" v-if="isChecked"></span>
      </span>
    </div>
  </label>
</template>

<script setup lang="ts">
import { computed, inject, PropType, ref, unref } from 'vue';

const props = defineProps({
  // 显示的文本标签
  label: {
    type: String,
    required: true,
  },
  // 实际选中的值（与 RadioGroup 绑定的值）
  value: {
    type: [String, Number, Boolean] as PropType<string | number | boolean>,
    required: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['change']);

// 注入父级 RadioGroup 上下文
const radioGroup = inject('ZRadioGroupContext', null);

// 生成唯一 name 属性
const uniqueName = ref(`z-radio-${Math.random().toString(36).slice(2)}`);

// 判断是否选中：基于 value 而非 label
const isChecked = computed(() => {
  if (radioGroup) {
    return unref(radioGroup.modelValue) === props.value;
  }
  return false;
});

// 处理选中事件
const handleChange = () => {
  if (props.disabled) return;

  if (radioGroup) {
    // 传递 value 而非 label
    radioGroup.changeEvent(props.value);
    emit('change', props.value);
  }
};
</script>

<style scoped lang="scss">
.z-radio-label {
  display: block;
  width: 100%;
  align-items: center;
  cursor: pointer;
  user-select: none;
  margin-bottom: 16px;
  transition: all 0.2s;

  .wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.z-radio-circle {
  width: 18px;
  height: 18px;
  border: 2px solid #ccc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  transition: all 0.2s;
}

.z-radio-dot {
  width: 10px;
  height: 10px;
  background-color: #b32d5d;
  border-radius: 50%;
  transform: scale(0);
  transition: transform 0.2s;
  transform: scale(1);
}

.is-checked .z-radio-circle {
  border-color: #b32d5d;
}

.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
</style>
