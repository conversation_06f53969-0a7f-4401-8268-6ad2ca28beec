<template>
  <div class="frame-animation" :style="containerStyle">
    <!-- 动态切换图片帧 - 优化中心点对齐 -->
    <img :src="currentFrameUrl" :alt="animationName" class="frame-image" :style="computedImageStyle"
      @load="handleImageLoad" @error="handleImageError">
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 组件参数
const props = defineProps({
  // 动画名称（用于区分不同动画）
  animationName: { type: String, default: 'idle' },
  // 序列帧图片路径数组（如：['frame1.png', 'frame2.png']）
  frameUrls: { type: Array, required: true },
  // 每帧图片的尺寸配置数组（可选）
  // 格式：[{ width: 100, height: 120 }, { width: 110, height: 100 }, ...]
  frameSizes: { type: Array, default: () => [] },
  // 帧率（每秒播放的帧数）
  fps: { type: Number, default: 12 },
  // 是否循环播放
  loop: { type: Boolean, default: true },
  // 动画完成后是否隐藏
  hideOnComplete: { type: Boolean, default: false },
  // 是否自动播放（挂载时自动开始）
  autoPlay: { type: Boolean, default: true },
  // 图片样式
  imageStyle: { type: Object, default: () => ({ width: '100px', height: 'auto' }) },
  // 是否启用中心点对齐（防抖动）
  centerAlign: { type: Boolean, default: true },
  // 固定容器尺寸（防止容器大小变化导致抖动）
  fixedSize: { type: Boolean, default: true },
  // 图片适应方式
  objectFit: { type: String, default: 'contain' },
  // 预加载帧数（提前加载后续帧，减少卡顿）
  preloadFrames: { type: Number, default: 3 },
  // 容器尺寸计算方式：'max'(最大尺寸), 'first'(第一帧尺寸), 'fixed'(固定尺寸)
  containerSizeMode: { type: String, default: 'max' }
});

// 状态管理
const currentFrameIndex = ref(0); // 当前帧索引
const isPlaying = ref(false); // 是否正在播放
const timer = ref(null); // 定时器ID
const preloadedImages = ref(new Set()); // 已预加载的图片
const imageLoadErrors = ref(new Set()); // 加载失败的图片
const detectedFrameSizes = ref(new Map()); // 检测到的每帧图片尺寸

// 计算当前帧图片路径
const currentFrameUrl = computed(() => {
  return props.frameUrls[currentFrameIndex.value] || '';
});

// 获取指定帧的尺寸
const getFrameSize = (frameIndex) => {
  // 优先使用用户指定的frameSizes
  if (props.frameSizes.length > frameIndex && props.frameSizes[frameIndex]) {
    return props.frameSizes[frameIndex];
  }

  // 其次使用检测到的尺寸
  // const url = props.frameUrls[frameIndex];
  // if (url && detectedFrameSizes.value.has(url)) {
  //   return detectedFrameSizes.value.get(url);
  // }

  // 最后使用默认尺寸
  return {
    width: parseInt(props.imageStyle.width) || 100,
    height: parseInt(props.imageStyle.height) || 100
  };
};

// 计算当前帧的尺寸
const currentFrameSize = computed(() => {
  return getFrameSize(currentFrameIndex.value);
});

// 计算容器尺寸
const computedContainerSize = computed(() => {
  if (props.containerSizeMode === 'fixed') {
    // 固定尺寸模式：使用imageStyle指定的尺寸
    return {
      width: parseInt(props.imageStyle.width) || 100,
      height: parseInt(props.imageStyle.height) || 100
    };
  } else if (props.containerSizeMode === 'first') {
    // 第一帧尺寸模式
    return getFrameSize(0);
  } else {
    // 最大尺寸模式（默认）
    let maxWidth = 0;
    let maxHeight = 0;

    for (let i = 0; i < props.frameUrls.length; i++) {
      const size = getFrameSize(i);
      maxWidth = Math.max(maxWidth, size.width);
      maxHeight = Math.max(maxHeight, size.height);
    }

    return {
      width: maxWidth || 100,
      height: maxHeight || 100
    };
  }
});

// 计算优化的图片样式
const computedImageStyle = computed(() => {
  const frameSize = currentFrameSize.value;

  const baseStyle = {
    objectFit: props.objectFit,
    objectPosition: 'center center',
    display: 'block',
    // 使用当前帧的尺寸
    width: `${frameSize.width}px`,
    height: `${frameSize.height}px`,
    // 关键：确保图片居中对齐，防止抖动
    position: props.centerAlign ? 'absolute' : 'static',
    top: props.centerAlign ? '50%' : 'auto',
    left: props.centerAlign ? '50%' : 'auto',
    transform: props.centerAlign ? 'translate(-50%, -50%)' : 'none',
    // 防止图片闪烁
    imageRendering: 'auto',
    backfaceVisibility: 'hidden',
    // 硬件加速
    willChange: 'auto',
    // 确保图片完全填充指定尺寸
    boxSizing: 'border-box'
  };

  return baseStyle;
});

// 计算容器样式
const containerStyle = computed(() => {
  const containerSize = computedContainerSize.value;

  const baseStyle = {
    display: props.hideOnComplete && !isPlaying.value && currentFrameIndex.value >= props.frameUrls.length - 1
      ? 'none'
      : 'block',
    // 固定容器尺寸，防止抖动
    position: 'relative',
    overflow: 'hidden',
    boxSizing: 'border-box'
  };

  // 设置容器尺寸
  if (props.fixedSize) {
    baseStyle.width = `${containerSize.width}px`;
    baseStyle.height = `${containerSize.height}px`;
    // 确保容器尺寸不会因为内容变化而改变
    baseStyle.minWidth = baseStyle.width;
    baseStyle.minHeight = baseStyle.height;
    baseStyle.maxWidth = baseStyle.width;
    baseStyle.maxHeight = baseStyle.height;
  }

  return baseStyle;
});

// 预加载图片
const preloadImages = (startIndex = 0) => {
  const endIndex = Math.min(startIndex + props.preloadFrames, props.frameUrls.length);

  for (let i = startIndex; i < endIndex; i++) {
    const url = props.frameUrls[i];
    if (!preloadedImages.value.has(url) && !imageLoadErrors.value.has(url)) {
      const img = new Image();
      img.onload = () => {
        preloadedImages.value.add(url);
      };
      img.onerror = () => {
        imageLoadErrors.value.add(url);
        console.warn(`帧动画图片加载失败: ${url}`);
      };
      img.src = url;
    }
  }
};

// 图片加载成功处理
const handleImageLoad = (event) => {
  const url = event.target.src;
  const img = event.target;

  // 记录图片实际尺寸（如果没有用户指定的frameSizes）
  if (props.frameSizes.length === 0) {
    detectedFrameSizes.value.set(url, {
      width: img.naturalWidth,
      height: img.naturalHeight
    });
  }

  preloadedImages.value.add(url);

  // 预加载后续帧
  const nextStartIndex = currentFrameIndex.value + 1;
  if (nextStartIndex < props.frameUrls.length) {
    preloadImages(nextStartIndex);
  }
};

// 图片加载失败处理
const handleImageError = (event) => {
  const url = event.target.src;
  imageLoadErrors.value.add(url);
  console.warn(`帧动画图片加载失败: ${url}`);
};

// 播放下一帧
const playNextFrame = () => {
  currentFrameIndex.value++;

  // 非循环模式且播放到最后一帧时停止
  if (!props.loop && currentFrameIndex.value >= props.frameUrls.length) {
    stopAnimation();
    currentFrameIndex.value = props.frameUrls.length - 1; // 停留在最后一帧
    return;
  }

  // 循环模式：重置索引
  if (currentFrameIndex.value >= props.frameUrls.length) {
    currentFrameIndex.value = 0;
  }

  // 预加载后续帧
  const nextStartIndex = currentFrameIndex.value + 1;
  if (nextStartIndex < props.frameUrls.length) {
    preloadImages(nextStartIndex);
  }
};

// 开始动画
const startAnimation = () => {
  if (isPlaying.value || props.frameUrls.length === 0) return;

  isPlaying.value = true;
  // 计算帧间隔时间（毫秒）
  const interval = 1000 / props.fps;
  timer.value = setInterval(playNextFrame, interval);
};

// 停止动画
const stopAnimation = () => {
  if (!isPlaying.value) return;

  isPlaying.value = false;
  clearInterval(timer.value);
  timer.value = null;
};

// 重置动画
const resetAnimation = () => {
  stopAnimation();
  currentFrameIndex.value = 0;
};

// 组件挂载时预加载图片，根据 autoPlay 决定是否自动播放
onMounted(() => {
  // 预加载前几帧图片
  if (props.frameUrls.length > 0) {
    preloadImages(0);
  }

  // 根据 autoPlay 属性决定是否自动开始动画
  if (props.autoPlay) {
    // 延迟启动动画，确保第一帧加载完成
    setTimeout(() => {
      startAnimation();
    }, 50);
  }
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAnimation();
});

// 暴露方法给父组件
defineExpose({
  startAnimation,
  stopAnimation,
  resetAnimation
});
</script>

<style scoped lang="scss">
.frame-animation {
  position: relative;
  display: inline-block;
  display: flex;
  /* 开启 Flexbox */
  justify-content: center;
  /* 水平居中 */
  align-items: center;
  /* 垂直居中 */

  // 优化渲染性能
  transform: translateZ(0); // 启用硬件加速
  backface-visibility: hidden; // 防止背面可见
  perspective: 1000px; // 3D 透视

  .frame-image {
    display: block;
    object-fit: contain;

    // 防抖动优化
    image-rendering: auto;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;

    // 性能优化
    transform: translateZ(0); // 硬件加速
    backface-visibility: hidden;
    will-change: auto; // 提示浏览器优化

    // 平滑过渡（可选，用于帧切换时的微调）
    transition: opacity 0.016s ease-out; // 约1帧的时间

    // 确保图片不会因为加载导致布局跳动
    max-width: 100%;
    height: auto;

    // 防止图片拖拽
    user-select: none;
    -webkit-user-drag: none;
    -webkit-touch-callout: none;

    // 确保图片清晰度
    &:not([src]) {
      opacity: 0; // 隐藏未加载的图片
    }
  }
}

// 针对高DPI屏幕的优化
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .frame-animation .frame-image {
    image-rendering: -webkit-optimize-contrast;
  }
}
</style>
