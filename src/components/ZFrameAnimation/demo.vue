<template>
  <div class="example-page">
    <h3>角色行走动画 - 优化版本</h3>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h4>基础用法（默认优化）</h4>
      <ZFrameAnimation ref="walkAnimation" animationName="walk" :frameUrls="walkFrames" :fps="12"
        :imageStyle="{ width: '200px', height: '200px' }" />
    </div>

    <!-- 高级用法 -->
    <div class="demo-section">
      <h4>高级用法（自定义优化参数）</h4>
      <ZFrameAnimation ref="advancedAnimation" animationName="advanced" :frameUrls="walkFrames" :fps="15"
        :imageStyle="{ width: '261px', height: '241px' }" :centerAlign="true" :fixedSize="true" objectFit="contain"
        :preloadFrames="5" />
    </div>

    <div class="controls">
      <button @click="handlePlay">播放</button>
      <button @click="handleStop">停止</button>
      <button @click="handleReset">重置</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ZFrameAnimation from './index.vue';

// 准备行走动画的序列帧（实际项目中替换为真实图片路径）
const walkFrames = [
  require('@/assets/frames/walk1.png'),
  require('@/assets/frames/walk2.png'),
  require('@/assets/frames/walk3.png'),
  require('@/assets/frames/walk4.png'),
  require('@/assets/frames/walk5.png'),
  require('@/assets/frames/walk6.png'),
];

const walkAnimation = ref(null);

const advancedAnimation = ref(null);

// 控制动画
const handlePlay = () => {
  walkAnimation.value?.startAnimation();
  advancedAnimation.value?.startAnimation();
};
const handleStop = () => {
  walkAnimation.value?.stopAnimation();
  advancedAnimation.value?.stopAnimation();
};
const handleReset = () => {
  walkAnimation.value?.resetAnimation();
  advancedAnimation.value?.resetAnimation();
};
</script>

<style scoped lang="scss">
.example-page {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h3 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;

    h4 {
      color: #666;
      margin-bottom: 15px;
      font-size: 16px;
    }

    // 确保动画容器居中
    :deep(.frame-animation) {
      display: block;
      margin: 0 auto;
      border: 2px dashed #ddd;
      border-radius: 4px;
      padding: 10px;
    }
  }

  .controls {
    text-align: center;
    margin-top: 30px;

    button {
      margin: 0 10px;
      padding: 10px 20px;
      background: #42b983;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s;

      &:hover {
        background: #369870;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}
</style>
</script>
