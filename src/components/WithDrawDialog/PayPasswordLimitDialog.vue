<!-- 密码次数限制 -->
<template>
  <ZActionSheet v-model="showPaymentPassworLimtDialog" title="Payment Password Limit" confirmText="Copy email"
    :showCancelButton="false" :onConfirm="handConfirm">

  </ZActionSheet>
</template>

<script setup lang="ts">
import { useWithdrawStore } from "@/stores/withdraw";
import { useClipboard } from "@/hooks/useClipboard";
const withdrawStore = useWithdrawStore();
const { showPaymentPassworLimtDialog, customerEmail } = storeToRefs(withdrawStore)
const { copy } = useClipboard();

const handConfirm = () => {
  copy(customerEmail.value)
  withdrawStore.handlePasswordLimtConfirm()
}



</script>

<style scoped lang="scss"></style>
