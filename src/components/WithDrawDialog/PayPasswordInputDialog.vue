<!-- 支付密码确认 -->
<script lang="ts" setup>
import { useWithdrawStore } from "@/stores/withdraw";
import { FocusManager } from "@/utils/managers/FocusManager";

const withdrawStore = useWithdrawStore();
const { showPaymentPasswordInputDialog } = storeToRefs(withdrawStore)
const paymentPassword = ref('');     // 支付密码（第一次）
const pwdRef = ref()

// 可选：通知聚焦管理器弹窗切换（如果有复杂的弹窗切换场景）
watch(
  () => withdrawStore.showPaymentPasswordInputDialog,
  (newVal) => {
    if (newVal) {
      // 通知聚焦管理器开始弹窗切换
      FocusManager.getInstance().startTransition(300);
    } else {
      paymentPassword.value = '';
      // pwdRef.value?.resetAndFocus();
    }
  }
);

</script>
<template>
  <ZDialog v-model="showPaymentPasswordInputDialog" title="Security Verification" :showClose="true"
    :showCancelButton="false" :showConfirmButton="false" class="z-dialog">
    <div class="content">
      <div class="tip">Enter your payment password</div>
      <ZPasswordInput ref="pwdRef" v-model="paymentPassword" :visible="showPaymentPasswordInputDialog"
        @complete="() => withdrawStore.handleCompletePayPwd(paymentPassword)" />
    </div>
  </ZDialog>
</template>

<style lang="scss" scoped>
.content {
  padding: 0 16px;
}

.tip {
  color: #666;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  margin-bottom: 16px;
}
</style>
