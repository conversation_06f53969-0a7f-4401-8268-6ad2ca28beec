<!-- 风控弹窗的进度条 -->
<template>
  <div class="progress-bar-container">
    <div class="progress-bar" :style="{ width: progressWidth }"></div>
    <div class="progress-text">
      {{ amountFormatThousands(current) }}/{{ amountFormatThousands(total) }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { amountFormatThousands } from "@/utils/core/tools";


// 接收的 props，可根据需要扩展校验等
const props = defineProps({
  current: {
    type: Number,
    required: true
  },
  total: {
    type: Number,
    required: true
  }
});

// 计算进度条宽度的百分比
const progressWidth = computed(() => {
  const ratio = props.current / props.total;
  return `${Math.min(ratio, 1) * 100}%`;
});
</script>

<style scoped>
.progress-bar-container {
  display: flex;
  align-items: center;
  width: 100%;
  /* 可根据实际调整宽度 */
  height: 16px;
  background-color: #000;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background-color: green;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  color: #FFF;
  text-align: center;
  font-family: DIN;
  font-size: 11px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}
</style>
