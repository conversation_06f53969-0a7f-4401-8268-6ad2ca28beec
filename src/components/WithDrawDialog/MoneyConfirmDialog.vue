<!-- 小程序二次确认弹窗 -->
<template>
  <ZActionSheet v-model="showMoneyConfirmDialog" title="Withdrawal" confirmText="Confirm" :showCancelButton="false"
    :onConfirm="withdrawStore.handleMoneyConfirm">
    <div class="money">₱{{ amountFormatThousands(+selectedAmount, 2) }}</div>
    <div>
      <div class="row">
        <span class="row_title">Type</span>
        <span class="row_value">{{ curRechangeMethods }}</span>
      </div>
      <div class="row">
        <span class="row_title">Total</span>
        <span class="row_value">₱{{ amountFormatThousands(+selectedAmount, 2) }}</span>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { useWithdrawStore } from "@/stores/withdraw";
import { amountFormatThousands } from "@/utils/core/tools";
const withdrawStore = useWithdrawStore();
const { showMoneyConfirmDialog, selectedAmount, curRechangeMethods } = storeToRefs(withdrawStore)

</script>

<style scoped lang="scss">
.money {
  text-align: center;
  color: #222;
  font-family: D-DIN;
  font-size: 28px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.09);
  padding-bottom: 20px;
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 14px 12px;

  .row_title {
    color: #666;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .row_value {
    color: #222;
    text-align: right;
    font-family: Inter;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
</style>
