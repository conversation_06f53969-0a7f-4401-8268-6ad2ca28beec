<template>
  <div class="circle-progress">
    <!-- 使用 van-circle 组件展示进度 -->
    <van-circle v-model:current-rate="progress" :rate="rate" :speed="speed" :text="displayText"
      :layer-color="layerColor" :size="size" :color="color" :stroke-width="strokeWidth" :clockwise="clockwise" />
    <!-- 底部文本 -->
    <div v-if="bottomText" class="bottom-text" :class="{ 'animated': animated }">
      {{ bottomText }}
    </div>
  </div>
</template>

<script setup>
import { computed, watch, ref } from 'vue'

const props = defineProps({
  // 当前进度值
  modelValue: {
    type: Number,
    default: 0
  },
  // 最大进度值
  rate: {
    type: Number,
    default: 100
  },
  // 动画速度
  speed: {
    type: Number,
    default: 50
  },
  // 自定义显示文本
  text: {
    type: String,
    default: ''
  },
  // 是否显示百分比
  showPercentage: {
    type: <PERSON>olean,
    default: true
  },
  // 背景色
  layerColor: {
    type: String,
    default: 'rgba(155, 34, 66, 0.1)'
  },
  // 进度条颜色
  color: {
    type: String,
    default: '#9b2242'
  },
  // 圆环大小
  size: {
    type: Number,
    default: 80
  },
  // 线条宽度
  strokeWidth: {
    type: Number,
    default: 24
  },
  // 是否顺时针
  clockwise: {
    type: Boolean,
    default: true
  },
  // 底部文本
  bottomText: {
    type: String,
    default: ''
  },
  // 是否启用底部文本动画
  animated: {
    type: Boolean,
    default: true
  },
  // 文本颜色
  textColor: {
    type: String,
    default: '#9b2242'
  },
  // 底部文本颜色
  bottomTextColor: {
    type: String,
    default: '#9b2242'
  }
})

const emit = defineEmits(['update:modelValue'])

// 内部进度值
const progress = ref(props.modelValue)

// 显示文本
const displayText = computed(() => {
  if (props.text) {
    return props.text
  }
  if (props.showPercentage) {
    return Math.round(progress.value) + '%'
  }
  return ''
})

// 监听外部进度变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== progress.value) {
    progress.value = newValue
  }
})

// 监听内部进度变化，向外发射事件
watch(progress, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>

<style lang="scss" scoped>
.circle-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;

  // 自定义 van-circle 样式
  :deep(.van-circle) {
    position: relative;
    filter: drop-shadow(0 4px 12px rgba(155, 34, 66, 0.2));
  }

  // 环形进度条内的文字样式
  :deep(.van-circle__text) {
    font-size: 20px;
    font-weight: 700;
    color: v-bind(textColor);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  // 环形进度条背景层样式
  :deep(.van-circle__layer) {
    transition: all 0.3s ease;
  }

  // 环形进度条进度层样式
  :deep(.van-circle__hover) {
    filter: drop-shadow(0 0 8px rgba(155, 34, 66, 0.4));
  }
}

// 底部文本样式
.bottom-text {
  font-size: 16px;
  color: v-bind(bottomTextColor);
  font-weight: 600;
  text-align: center;
  margin-top: 1rem;
  letter-spacing: 1px;
  text-transform: uppercase;

  // 底部文本动画
  &.animated {
    animation: pulse 2s ease-in-out infinite;
  }
}

// 脉动动画
@keyframes pulse {

  0%,
  100% {
    opacity: 0.8;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}
</style>
