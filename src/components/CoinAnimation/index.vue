<template>
  <div class="coin-animation-container" ref="containerRef" v-show="shouldShowContainer">
    <audio ref="audioRef" :src="audioSrc" preload="auto" loop="false" @ended="handleAudioEnd" @error="handleAudioError">
      您的浏览器不支持音频播放
    </audio>

    <div class="coins-container">
      <div v-for="coin in coins" :key="coin.id" class="coin" :style="{
        left: `${coin.x}px`,
        top: `${coin.y}px`,
        opacity: coin.visible ? 1 : 0,
        transition: getCoinTransition(coin),
        'z-index': coin.zIndex
      }">
        <FrameCoin :ref="el => setCoinRef(coin.id, el)" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue';
import audioSrc from '@/assets/audio/coins_fly.mp3';
import FrameCoin from './frameCoin.vue'

// 组件参数
const props = defineProps({
  coinCount: { type: Number, default: 10 },
  riseDuration: { type: Number, default: 0.5 }, // 延长上升时长，确保视觉可见（原0.5s）
  flyDuration: { type: Number, default: 1 },
  flyInterval: { type: Number, default: 0.12 },
  startRef: { type: Object, default: null },
  endRef: { type: Object, default: null },
  playAudio: { type: Boolean, default: true },
  scatterRadius: { type: Number, default: 50 },
  riseHeight: { type: Number, default: 100 }, // 增加上升高度，更明显（原100px）
  centerDensity: { type: Number, default: 0.8 }
});

// 组件事件
const emit = defineEmits(['animation-start', 'animation-end', 'audio-error', 'last-coin-arrive']);

// 数据
const coins = ref([]);
const coinPool = ref([]);
const isAnimating = ref(false);
const audioRef = ref(null);
const containerRef = ref(null);
const timers = ref([]);
const isMusicPlaying = ref(false);
const coinRefs = ref(new Map()); // 存储金币组件引用

// 设置金币组件引用
const setCoinRef = (coinId, el) => {
  if (el) {
    coinRefs.value.set(coinId, el);
  } else {
    coinRefs.value.delete(coinId);
  }
};

// 控制指定金币的动画
const controlCoinAnimation = (coinId, shouldPlay) => {
  const coinRef = coinRefs.value.get(coinId);
  if (coinRef) {
    if (shouldPlay) {
      coinRef.startAnimation();
    } else {
      coinRef.stopAnimation();
    }
  }
};

// 计算属性：控制容器显示/隐藏
const shouldShowContainer = computed(() => {
  const hasVisibleCoins = coins.value.some(coin => coin.visible);
  const shouldShow = isAnimating.value || hasVisibleCoins;
  if (!shouldShow) {
    // console.log('🚫 隐藏金币动画容器');
  }
  return shouldShow;
});

// 初始化金币池
onMounted(() => {
  for (let i = 0; i < 20; i++) {
    coinPool.value.push({
      id: `coin-${i}`,
      x: 0,
      y: 0,
      visible: false,
      transitionTime: 0,
      delay: 0,
      zIndex: 0,
      phase: 'hidden', // hidden, rising, flying, impact
      targetX: 0,
      targetY: 0
    });
  }
  coins.value = [...coinPool.value];

  // 预加载音频
  if (props.playAudio) {
    nextTick(() => {
      try {
        audioRef.value?.load();
      } catch (error) {
        console.warn('音频预加载失败:', error);
      }
    });
  }
});

// 组件卸载清理
onUnmounted(() => {
  timers.value.forEach(timer => clearTimeout(timer));
  stopAudio();
  // 清理金币引用
  coinRefs.value.clear();
});

// 播放音频
const playAudio = () => {
  if (!props.playAudio || !audioRef.value || isMusicPlaying.value) return;
  try {
    audioRef.value.currentTime = 0;
    audioRef.value.play().then(() => {
      isMusicPlaying.value = true;
      const audioTimer = setTimeout(() => {
        stopAudio();
      }, 1000);
      timers.value.push(audioTimer);
    }).catch(error => {
      console.warn('音频播放失败:', error);
      emit('audio-error', error);
    });
  } catch (error) {
    console.error('音频播放异常:', error);
    emit('audio-error', error);
  }
};

// 停止音频
const stopAudio = () => {
  if (!audioRef.value) return;
  audioRef.value.pause();
  audioRef.value.currentTime = 0;
  isMusicPlaying.value = false;
};

// 音频事件处理
const handleAudioEnd = () => { isMusicPlaying.value = false; };
const handleAudioError = (error) => {
  console.error('音频加载错误:', error);
  emit('audio-error', error);
};

// 过渡效果计算
const getCoinTransition = (coin) => {
  if (coin.transitionTime === 0) return 'none';
  switch (coin.phase) {
    case 'rising':
      return `all ${coin.transitionTime}s cubic-bezier(0.25, 0.46, 0.45, 0.94)`; // 上升用ease-out，更自然
    case 'flying':
      return `all ${coin.transitionTime}s cubic-bezier(0.175, 0.885, 0.32, 1.275)`; // 飞行用加速曲线
    default:
      return `all ${coin.transitionTime}s ease-in-out`;
  }
};

// 生成轨迹（初始位置→上升峰值→目标位置）
const generateCoinTrajectories = (startPos, endPos, count) => {
  const trajectories = [];
  const circleCenter = { x: startPos.x, y: startPos.y - props.riseHeight }; // 上升后的圆心位置
  for (let i = 0; i < count; i++) {
    let angle = count === 1 ? -Math.PI / 2 : (-Math.PI + (i / (count - 1)) * Math.PI);
    if (count > 1) {
      angle += (Math.random() - 0.5) * 0.4 * Math.pow(Math.sin((i / (count - 1)) * Math.PI), props.centerDensity);
      angle = Math.max(-Math.PI, Math.min(0, angle));
    }
    const distance = props.scatterRadius * (0.7 + Math.pow(Math.random(), props.centerDensity) * 0.6);
    const peakX = circleCenter.x + distance * Math.cos(angle);
    const peakY = circleCenter.y + distance * Math.sin(angle);
    trajectories.push({
      startX: startPos.x,
      startY: startPos.y,
      peakX: peakX,
      peakY: peakY,
      endX: endPos.x + (Math.random() * 8 - 4),
      endY: endPos.y + (Math.random() * 8 - 4),
    });
  }
  return trajectories;
};

// 计算飞行时序
const calculateFlyTiming = (coinCount, flyInterval) => {
  const musicDuration = 1.0;
  const lastCoinStartDelay = (coinCount - 1) * flyInterval;
  const minFlyTime = 0.3;
  const lastCoinFlyTime = musicDuration - lastCoinStartDelay;
  if (lastCoinFlyTime < minFlyTime) {
    const adjustedInterval = (musicDuration - minFlyTime) / (coinCount - 1);
    return { flyInterval: Math.max(0.05, adjustedInterval), totalMusicTime: musicDuration };
  }
  return { flyInterval, totalMusicTime: musicDuration };
};

// 动态元素启动动画
const startAnimationWithElements = (startElement, endElement, coinCount = props.coinCount) => {
  if (isAnimating.value) return;
  hideAllCoins();
  isAnimating.value = true;
  isMusicPlaying.value = false;
  const containerEl = containerRef.value;
  if (!containerEl || !startElement || !endElement) {
    console.error('❌ 容器或元素不存在');
    isAnimating.value = false;
    return;
  }
  executeAnimation(startElement, endElement, coinCount);
};

// 执行动画核心逻辑
const executeAnimation = (startElement, endElement, coinCount = props.coinCount) => {
  const containerEl = containerRef.value;
  if (!containerEl) {
    console.error('❌ 容器不存在');
    isAnimating.value = false;
    return;
  }
  // 计算起始/目标位置（相对于容器）
  const startRect = startElement.getBoundingClientRect();
  const endRect = endElement.getBoundingClientRect();
  const containerRect = containerEl.getBoundingClientRect();
  const startPos = {
    x: startRect.left - containerRect.left + startRect.width / 2 - 25,
    y: startRect.top - containerRect.top + startRect.height / 2 - 25
  };
  const endPos = {
    x: endRect.left - containerRect.left + endRect.width / 2 - 25,
    y: endRect.top - containerRect.top + endRect.height / 2 - 25
  };
  const trajectories = generateCoinTrajectories(startPos, endPos, coinCount);
  playAudio();
  const timing = calculateFlyTiming(coinCount, props.flyInterval);
  executeAnimationSequence(trajectories, coinCount, timing);
};

// 动画序列核心（重点修改部分）
const executeAnimationSequence = (trajectories, coinCount, timing) => {
  const riseCompleteTime = props.riseDuration * 1000; // 上升动画总时长（确保所有金币完成上升）
  coins.value.forEach((coin, index) => {
    if (index < coinCount) {
      const trajectory = trajectories[index];
      const flyDelay = index * timing.flyInterval;

      // 阶段0：设置初始位置（隐藏状态，等待渲染）
      Object.assign(coin, {
        x: trajectory.startX,
        y: trajectory.startY,
        visible: true,
        transitionTime: 0,
        zIndex: 10000 + index,
        phase: 'hidden' // 初始隐藏状态，不应用过渡
      });

      // 等待初始位置渲染后再执行后续动画
      nextTick(() => {
        // 阶段1：显示在初始位置（确保用户看到金币从起点出现）
        const showTimer = setTimeout(() => {
          Object.assign(coin, {
            phase: 'rising', // 切换到上升阶段
            transitionTime: 0, // 无过渡显示
            visible: true
          });

          // 开始金币动画
          controlCoinAnimation(coin.id, true);

          // 阶段2：执行上升动画（从初始位置→峰值位置）
          // 延迟100ms确保初始位置已渲染，让上升过程可见
          const riseTimer = setTimeout(() => {
            Object.assign(coin, {
              x: trajectory.peakX,
              y: trajectory.peakY,
              transitionTime: props.riseDuration, // 应用上升过渡时长
              phase: 'rising'
            });
          }, 100); // 关键：延长初始显示到上升的延迟，确保视觉捕捉
          timers.value.push(riseTimer);

          // 阶段3：上升完成后，按间隔飞向目标
          // 等待所有金币上升完成（riseCompleteTime）+ 各自延迟（flyDelay）
          const flyStartDelay = riseCompleteTime + (flyDelay * 1000);
          const flyTimer = setTimeout(() => {
            if (index === 0) emit('animation-start');
            const individualFlyTime = timing.totalMusicTime - (index * timing.flyInterval);
            const actualFlyTime = Math.max(0.3, individualFlyTime);
            Object.assign(coin, {
              x: trajectory.endX,
              y: trajectory.endY,
              transitionTime: actualFlyTime,
              phase: 'flying'
            });

            // 阶段4：到达目标后消失
            const arriveTimer = setTimeout(() => {
              // 停止金币动画
              controlCoinAnimation(coin.id, false);

              Object.assign(coin, {
                visible: false,
                phase: 'impact',
                transitionTime: 0
              });
              if (index === coinCount - 1) {
                emit('last-coin-arrive');
                const endTimer = setTimeout(() => {
                  isAnimating.value = false;
                  hideAllCoins();
                  emit('animation-end');
                }, 100);
                timers.value.push(endTimer);
              }
            }, actualFlyTime * 1000);
            timers.value.push(arriveTimer);
          }, flyStartDelay);
          timers.value.push(flyTimer);
        }, 50); // 初始显示延迟，确保DOM渲染
        timers.value.push(showTimer);
      });
    }
  });
};

// 启动动画（使用props引用）
const startAnimation = () => {
  if (isAnimating.value) return;
  hideAllCoins();
  isAnimating.value = true;
  isMusicPlaying.value = false;
  const startEl = props.startRef?.$el || props.startRef;
  const endEl = props.endRef?.$el || props.endRef;
  if (!startEl || !endEl) {
    console.error('❌ 起始/目标元素不存在');
    isAnimating.value = false;
    return;
  }
  executeAnimation(startEl, endEl, props.coinCount);
};

// 隐藏所有金币
const hideAllCoins = () => {
  coins.value.forEach(coin => {
    // 停止金币动画
    controlCoinAnimation(coin.id, false);

    coin.visible = false;
    coin.phase = 'hidden';
    coin.transitionTime = 0;
  });
};

// 重置动画
const resetAnimation = () => {
  timers.value.forEach(timer => clearTimeout(timer));
  timers.value = [];
  stopAudio();
  isAnimating.value = false;

  // 停止所有金币动画并重置
  coins.value.forEach(coin => {
    controlCoinAnimation(coin.id, false);
    coin.x = 0;
    coin.y = 0;
  });

  hideAllCoins();
};

// 暴露方法
defineExpose({ startAnimation, startAnimationWithElements, resetAnimation, hideAllCoins });
</script>

<style scoped lang="scss">
.coin-animation-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  pointer-events: none;
  z-index: 99999;

  audio {
    display: none;
  }

  .coins-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .coin {
    position: absolute;
    width: 50px;
    height: 50px;
    will-change: transform, opacity, left, top; // 优化动画性能
    backface-visibility: hidden;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}
</style>
