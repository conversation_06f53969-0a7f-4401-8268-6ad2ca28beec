<template>
  <img class="coin-icon" src="@/assets/images/gold.png" alt="coin"
    :style="{ width: size + 'px', height: size + 'px' }" />
</template>

<script setup lang="ts">
interface Props {
  size?: number
}

const props = withDefaults(defineProps<Props>(), {
  size: 20
})
</script>

<style lang="scss" scoped>
.coin-icon {
  display: inline-block;
  vertical-align: middle;
  object-fit: contain;
}
</style>
