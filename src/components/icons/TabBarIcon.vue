<script setup>
defineProps({
  type: {
    type: String,
    default: "home", // 区分icon
  },
  color: {
    type: String,
    default: "#AC1140", // 默认继承父级颜色
  },
  size: {
    type: [String, Number],
    default: 25,
  },
});
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    :width="size"
    :height="size"
    :viewBox="`0 0 ${size} ${size}`"
    fill="none"
  >
    <!-- Home -->
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M10.6092 0.989128L17.2572 4.67313C17.9652 5.05713 18.3972 5.78913 18.3972 6.59313V6.66513L17.5572 16.3251C17.5332 17.5131 16.5492 18.4851 15.3492 18.4851H3.30117C2.10117 18.4851 1.11717 17.5251 1.09317 16.3251L0.253174 6.59313C0.253174 5.80113 0.697174 5.05713 1.39317 4.67313L8.04117 0.989128C8.84517 0.545128 9.80517 0.545128 10.6092 0.989128ZM9.32517 9.86502C7.49871 9.86502 6.01807 11.3457 6.01807 13.1721V16.6613H12.6323V13.1721C12.6323 11.3457 11.1516 9.86502 9.32517 9.86502Z"
      :fill="color"
      v-if="type === 'Home'"
    />
    <!-- Promos -->
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M15.4458 6.57004C16.0907 6.32967 16.7767 6.20069 17.4861 6.20069C18.2072 6.20069 18.9166 6.32967 19.5732 6.58763C19.585 6.44693 19.5967 6.30622 19.5967 6.16551C19.5967 3.90245 17.7499 2.06738 15.4751 2.06738C14.2674 2.06738 13.1886 2.58331 12.4323 3.40411C13.0713 3.75588 13.6576 4.21318 14.1794 4.76429C14.6953 5.30367 15.1175 5.91341 15.4458 6.57004ZM9.53605 18.2547H10.8141L10.3041 16.8124C13.3469 16.2379 15.6569 13.4237 15.6569 10.035C15.6569 6.23586 12.7489 3.152 9.16082 3.152C5.57277 3.152 2.66479 6.23586 2.66479 10.035C2.66479 13.3885 4.93372 16.1851 7.92963 16.7948L7.50164 18.2547H8.60972L8.42797 18.882C8.31071 19.2807 8.42797 19.6969 8.7387 19.9959C8.87355 20.1249 8.897 20.3125 8.80319 20.4708L7.95894 21.8076C7.83582 22.001 7.91204 22.2414 8.12896 22.3469C8.19932 22.3821 8.27554 22.3997 8.34589 22.3997C8.49832 22.3997 8.65076 22.3294 8.7387 22.2004L9.58295 20.8636C9.87023 20.4122 9.79401 19.8494 9.4012 19.4683C9.29567 19.3686 9.25463 19.2279 9.29567 19.0931L9.53605 18.2547ZM8.54523 5.93685C8.5335 5.93685 7.54268 6.17723 6.67498 6.89249C5.50241 7.86573 5.09201 9.20245 5.45551 10.8558C5.52 11.1489 5.33239 11.4421 5.03925 11.5066C4.99821 11.5183 4.95717 11.5183 4.922 11.5183H4.92199C4.66989 11.5183 4.4471 11.3483 4.38847 11.0903C3.81391 8.48719 4.93958 6.91595 5.9773 6.05411C7.02089 5.19227 8.16414 4.90499 8.29312 4.87568C8.58627 4.80532 8.87941 4.98707 8.9439 5.28021C9.02012 5.57335 8.83837 5.8665 8.54523 5.93685ZM17.4802 6.79869C20.523 6.79869 22.9912 9.41352 22.9912 12.6439C22.9912 15.6868 20.7985 18.1902 17.9961 18.4775L18.2248 19.3041H17.6736L17.5681 19.6676C17.5564 19.7204 17.574 19.779 17.615 19.8201C17.9316 20.1249 17.9902 20.5705 17.7616 20.934L17.1694 21.8662C17.0815 21.9952 16.9291 22.0655 16.7766 22.0655C16.7063 22.0655 16.6301 22.0479 16.5597 22.0128C16.3428 21.9072 16.2666 21.6669 16.3897 21.4734L16.9818 20.5412C17.0229 20.4826 17.0111 20.4005 16.9584 20.3536C16.7063 20.1073 16.6125 19.779 16.7063 19.4624L16.7532 19.2983H16.1786L16.3897 18.3778C15.0764 18.0964 13.9273 17.3166 13.1358 16.232C13.5052 15.9623 13.8569 15.6574 14.1853 15.3115C15.5161 13.8986 16.249 12.0283 16.249 10.035C16.249 9.01484 16.0555 8.02989 15.6861 7.11528C16.249 6.91009 16.8529 6.79869 17.4802 6.79869ZM18.6176 16.6365C18.6879 16.6365 18.7583 16.6248 18.8286 16.5955C20.136 16.0385 21.0975 14.9422 21.3966 13.6524C21.461 13.3651 21.2793 13.0719 20.9862 13.0016C20.6989 12.9312 20.4057 13.1188 20.3354 13.412C20.1126 14.35 19.3915 15.165 18.4006 15.5871C18.1251 15.7043 17.9961 16.0268 18.1134 16.3024C18.2072 16.5134 18.4065 16.6365 18.6176 16.6365Z"
      v-else-if="type === 'Promos'"
      :fill="color"
    />

    <!-- News -->
    <path
      d="M16.7649 2.80206C17.7288 2.80206 18.5101 3.58341 18.5101 4.54726V20.069L19.5075 20.1024L19.5524 5.99337H20.3056C21.2694 5.99337 22.0508 6.7747 22.0508 7.73856V19.8148C22.0508 20.7751 21.275 21.555 20.3147 21.56L4.70547 21.6415C3.74165 21.6465 2.95622 20.8693 2.95119 19.9054V19.9009L2.95117 4.54726C2.95117 3.58341 3.73252 2.80206 4.69637 2.80206H16.7649ZM14.1467 16.1157H6.21747C5.79062 16.1157 5.44459 16.4618 5.44459 16.8886C5.44459 17.3155 5.79062 17.6615 6.21747 17.6615H14.1467C14.5736 17.6615 14.9196 17.3155 14.9196 16.8887C14.9196 16.4618 14.5736 16.1157 14.1467 16.1157ZM14.1467 13.0741H6.21747C5.79062 13.0741 5.44459 13.4201 5.44459 13.8469C5.44459 14.2738 5.79062 14.6198 6.21747 14.6198H14.1467C14.5736 14.6198 14.9196 14.2738 14.9196 13.8469C14.9196 13.4201 14.5736 13.0741 14.1467 13.0741ZM9.43416 6.84106H6.19253C5.77945 6.84106 5.44459 7.17592 5.44459 7.589V10.8302C5.44459 11.2433 5.77945 11.5781 6.19253 11.5781H9.43416C9.84724 11.5781 10.1821 11.2433 10.1821 10.8302V7.589C10.1821 7.17592 9.84724 6.84106 9.43416 6.84106ZM14.1467 10.0323H11.9523C11.5255 10.0323 11.1795 10.3784 11.1795 10.8052C11.1795 11.2321 11.5255 11.5781 11.9523 11.5781H14.1467C14.5736 11.5781 14.9196 11.2321 14.9196 10.8053C14.9196 10.3784 14.5736 10.0323 14.1467 10.0323ZM14.1467 6.84106H11.9523C11.5255 6.84106 11.1795 7.18706 11.1795 7.61393C11.1795 8.0408 11.5255 8.38682 11.9523 8.38682H14.1467C14.5736 8.38682 14.9196 8.04082 14.9196 7.61395C14.9196 7.18708 14.5736 6.84106 14.1467 6.84106Z"
      v-else-if="type === 'News'"
      :fill="color"
    />

    <!-- Account -->
    <path
      v-else-if="type === 'Account'"
      d="M12.3253 2.35284C16.3462 2.35284 19.6058 5.61245 19.6058 9.63339C19.6058 13.6543 16.3462 16.9139 12.3253 16.9139C8.30434 16.9139 5.04474 13.6543 5.04474 9.63339C5.04474 5.61245 8.30434 2.35284 12.3253 2.35284ZM13.98 11.6823C13.7627 11.8997 13.5048 12.072 13.2208 12.1896C12.9369 12.3072 12.6326 12.3678 12.3253 12.3678C12.018 12.3678 11.7137 12.3072 11.4297 12.1896C11.1458 12.072 10.8878 11.8997 10.6705 11.6823C10.523 11.5427 10.3268 11.4662 10.1237 11.4689C9.92058 11.4717 9.72656 11.5536 9.5829 11.6972C9.43923 11.8408 9.35724 12.0348 9.35437 12.2379C9.3515 12.441 9.42797 12.6372 9.56751 12.7848C10.2989 13.5162 11.2909 13.927 12.3253 13.927C13.3596 13.927 14.3516 13.5162 15.083 12.7848C15.2226 12.6372 15.2991 12.441 15.2962 12.2379C15.2933 12.0348 15.2113 11.8408 15.0677 11.6972C14.924 11.5536 14.73 11.4717 14.5269 11.4689C14.3238 11.4662 14.1276 11.5427 13.98 11.6823ZM12.3253 22.08C17.2079 22.08 21.1659 22.4373 21.1659 20.8423C21.1659 19.2474 17.2079 17.954 12.3253 17.954C7.44264 17.954 3.48462 19.2474 3.48462 20.8423C3.48462 22.4373 7.44264 22.08 12.3253 22.08Z"
      :fill="color"
    />
  </svg>
</template>
