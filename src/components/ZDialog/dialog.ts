import { h, render, AppContext } from "vue";
import { showDialog, showConfirmDialog } from "vant";
import BaseDialog from "./index.vue";

type DialogOptions = {
  title?: string;
  message?: string;
  describe?: string;
  showConfirmButton?: boolean;
  showCancelButton?: boolean;
  cancelText?: string;
  confirmText?: string;
  showClose?: boolean;
  onConfirm?: (() => void) | (() => Promise<void>);
  onCancel?: (() => void) | (() => Promise<void>);
  animationDuration?: number; // 动画时长，单位 ms
};

export function useDialog(appContext?: AppContext) {
  // 组件式调用
  const DialogComponent = (options: DialogOptions) => {
    const container = document.createElement("div");

    const onConfirm = async () => {
      try {
        if (options.onConfirm) {
          await options.onConfirm();
        }
        unmount();
      } catch (error) {
        console.error("Dialog confirm error:", error);
      }
    };

    const onCancel = async () => {
      try {
        if (options.onCancel) {
          await options.onCancel();
        }
        unmount();
      } catch (error) {
        console.error("Dialog cancel error:", error);
      }
    };

    const unmount = () => {
      setTimeout(() => {
        render(null, container);
        container.remove();
      }, options.animationDuration || 300); // 默认动画时长 300ms
    };

    const vnode = h(BaseDialog, {
      ...options,
      modelValue: true,
      onConfirm,
      onCancel,
    });

    if (appContext) {
      vnode.appContext = appContext;
    }
    render(vnode, container);
    document.body.appendChild(container);
    return {
      unmount,
    };
  };

  // 函数式调用（兼容Vant原生方式）
  DialogComponent.alert = (options: DialogOptions) => {
    return showDialog({
      ...options,
    });
  };

  DialogComponent.confirm = (options: DialogOptions) => {
    return showConfirmDialog({
      ...options,
    });
  };

  return DialogComponent;
}
