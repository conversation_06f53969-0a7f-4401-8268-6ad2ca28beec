<!-- filepath: /src/components/ZPopDialog/LeaderboardJiliPopup.vue -->
<template>
  <ZPopOverlay :show="showLeaderBoardPopJILITip">
    <div class="leaderboard-content" :style="{ backgroundImage: `url(${rankJiliImage})` }">
      <div class="popup-date">{{ popupTitle }}</div>
      <div class="rank-list">
        <div v-for="(item, index) in topRankList" :key="item.player_id" :class="'rank-item rank-' + index">
          <div :class="'player-id rank-player-id-' + index">{{ item.player_id }}</div>
          <div :class="'bet rank-bet-' + index">{{ item.total_bet_amount }}</div>
          <div :class="'bonus rank-bonus-' + index">{{ item.award }}</div>
        </div>
      </div>
      <div class="detail-btn" :style="{ backgroundImage: `url(${rankJili2Image})` }" @click="handleDetail"></div>
      <div class="close-btn" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="28" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useAutoPopMgrStore, STORE_KEY_MAP } from '@/stores/autoPopMgr';
import { AutoPopMgr } from '@/utils/AutoPopMgr';
import { amountFormatThousands, isNumeric, getToday } from '@/utils/core/tools';
import { setLocalStorage, getLocalStorage } from '@/utils/core/Storage';
import router from '@/router';
// 导入图片资源，确保与预加载使用相同的路径
import rankJiliImage from '@/assets/images/popDialog/rank_jili.png';
import rankJili2Image from '@/assets/images/popDialog/rank_jili2.png';

const autoPopStore = useAutoPopMgrStore();
const { showLeaderBoardPopJILITip } = storeToRefs(autoPopStore);

const topRankList = ref<any[]>([]);
const popupTitle = ref('');

// 将排行榜数据（前 3 名）处理并格式化
const processRankList = (list: any[]) => {
  const result: any[] = [];
  list?.forEach((item, index) => {
    if (index < 3) {
      result.push({
        ...item,
        total_bet_amount: isNumeric(item.total_bet_amount)
          ? amountFormatThousands(item.total_bet_amount)
          : item.total_bet_amount,
        award: isNumeric(item.award) ? amountFormatThousands(item.award) : item.award,
      });
    }
  });
  topRankList.value = result;
};

// 格式化标题，格式例如 "MAR.15 TOP 3 WINNERS"
const formatPopupTitle = (dateStr: string) => {
  const dateToFormat = dateStr || getToday();
  const dateObj = new Date(dateToFormat);
  const monthAbbr = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
  const month = monthAbbr[dateObj.getMonth()];
  const day = dateObj.getDate();
  const formattedTitle = `${month}.${day} TOP 3 WINNERS`;
  popupTitle.value = formattedTitle;
};

// 记录排行榜弹窗进入的次数（用于控制每日显示次数）
const recordEntry = () => {
  const currentDate = new Date();
  const currentDateStr = `${currentDate.getFullYear()}-${currentDate.getMonth() + 1}-${currentDate.getDate()}`;
  const lastEntry = getLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME_JILI) || '';
  if (!lastEntry) {
    setLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME_JILI, currentDateStr + '_1');
  } else {
    const [, countStr] = lastEntry.split('_');
    const newCount = parseInt(countStr) + 1;
    setLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME_JILI, currentDateStr + '_' + newCount);
  }
};

const handleDetail = () => {
  showLeaderBoardPopJILITip.value = false;
  router.push('/promos/promo_7');
};

const handleClose = () => {
  showLeaderBoardPopJILITip.value = false;
  AutoPopMgr.destroyCurrentPopup();
};

// 当弹窗显示时记录进入信息（例如统计展示次数）
watch(() => autoPopStore.showLeaderBoardPopJILITip, (show) => {
  if (show) {
    recordEntry();
  }
});

// 监听排行榜数据变更
watch(() => autoPopStore.leaderBoardPopJILIInfo, (data: any) => {
  if (data && data.list) {
    processRankList(data.list.rank);
    formatPopupTitle(data.list.time);
  }
}, { deep: true, immediate: true });
</script>

<style lang="scss" scoped>
.leaderboard-content {
  width: 285px;
  height: 400px;
  padding: 20px;
  border-radius: 33px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  z-index: 9;

  .rank-list {
    position: absolute;
    top: 130px;
    left: 130px;

    .player-id,
    .bet,
    .bonus {
      font-family: 'D-DIN';
      font-size: 12px;
      font-weight: 700;
      line-height: normal;
      height: 18px;
    }

    .player-id {
      padding-left: 10px;
    }

    .bet,
    .bonus {
      padding-left: 33px;
    }

    .rank-0 {
      color: #ffae00;
    }

    .rank-1 {
      margin-top: 17px;
      color: #7cadd5;
    }

    .rank-2 {
      margin-top: 17px;
      color: #ee9e59;
    }
  }

  .popup-date {
    width: 100%;
    position: absolute;
    top: 94px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    text-align: center;
    font-family: 'Inter';
    font-size: 11px;
    font-weight: 500;
  }

  .detail-btn {
    width: 180px;
    height: 40px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    position: absolute;
    top: 345px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }

  .close-btn {
    position: absolute;
    top: 410px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
