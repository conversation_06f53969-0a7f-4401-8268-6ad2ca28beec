<!-- filepath: /src/components/ZPopDialog/EnvelopeTip.vue -->
<template>
  <ZPopOverlay :show="showEnvelopeTip">
    <div class="content" :style="{ backgroundImage: `url(${envelope2Image})` }">
      <div class="bonus">{{ envelopeInfo.first_deposit_default_bonus_rate }} Bonus</div>
      <div class="tip">
        {{ amountFormatThousands(envelopeInfo.today_user_recharge_count, 0) }} Players Claimed Today
      </div>
      <div
        class="btn"
        :style="{ backgroundImage: `url(${envelope1Image})` }"
        @click="handleConfirm"
      ></div>
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="36" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore, STORE_KEY_MAP, POP_FORM } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { getToday, amountFormatThousands } from "@/utils/core/tools";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
// 导入图片资源，确保与预加载使用相同的路径
import envelope1Image from "@/assets/images/popDialog/envelope1.png";
import envelope2Image from "@/assets/images/popDialog/envelope2.png";

const depositStore = useDepositStore();
const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore();
const { showEnvelopeTip, envelopeInfo, sourceEnvelope } = storeToRefs(autoPopMgrStore);

// 每天最多弹出次数（与 isNeedShowEnvelopePop 保持一致）
const MAX_SHOW_COUNT = 3; // 允许显示3次

/**
 * 记录弹窗次数
 */
const handleCount = () => {
  const userId = globalStore.userInfo?.user_id;
  if (!userId) return;

  const storageKey = `${userId}${STORE_KEY_MAP._ENVELOPE}`;
  let storageValue = getLocalStorage(storageKey) || "";
  const today = getToday();
  let [countStr, date] = storageValue.split("_");

  // 如果日期不匹配，重置计数
  if (date !== today) {
    countStr = "0";
  }

  const count = parseInt(countStr || "0");

  // 只有在未达到最大次数时才增加计数
  if (count < MAX_SHOW_COUNT) {
    setLocalStorage(storageKey, `${count + 1}_${today}`);
    console.log(`首冲弹窗显示次数: ${count + 1}/${MAX_SHOW_COUNT}`);
  } else {
    console.log(`首冲弹窗今日已达到最大显示次数: ${MAX_SHOW_COUNT}`);
  }
};

const handleConfirm = () => {
  showEnvelopeTip.value = false;
  depositStore.openDialog(envelopeInfo.value.first_deposit_default_bonus);
};

const handleClose = () => {
  showEnvelopeTip.value = false;
  if (sourceEnvelope.value === POP_FORM.AUTO) {
    AutoPopMgr.destroyCurrentPopup();
  }
};

// 当 showEnvelopeTip 变为 true 时记录弹出次数
watch(
  () => autoPopMgrStore.showEnvelopeTip,
  (newVal) => {
    if (newVal) {
      handleCount();
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  width: 375px;
  height: 667px;
  padding: 20px;
  border-radius: 33px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  z-index: 9;

  .bonus {
    width: 100%;
    position: absolute;
    top: 250px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    text-align: center;
    font-family: D-DIN;
    font-size: 40px;
    font-weight: 700;
    letter-spacing: -1.68px;
  }

  .tip {
    width: 100%;
    position: absolute;
    top: 360px;
    left: 50%;
    transform: translateX(-50%);
    color: #882b00;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
  }

  .btn {
    width: 228px;
    height: 56px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    position: absolute;
    top: 400px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }

  .close {
    position: absolute;
    top: 500px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
