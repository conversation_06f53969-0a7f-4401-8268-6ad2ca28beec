<template>
  <div class="password-input-container">
    <div class="relative">
      <input :type="inputType" v-model="inputValue" :placeholder="placeholder" class="password-input"
        @input="handleInput" />
      <button type="button" @click="togglePasswordVisibility" class="toggle-button">
        <ZIcon :size="24" :type="passwordVisible ? 'icon-yanjing_xianshi_o' : 'icon-yanjing_yincang_o'" color="">
        </ZIcon>
      </button>
    </div>
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Please enter your password'
  },
  errorMessage: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:modelValue', 'input'])

const passwordVisible = ref(true)
const inputType = computed(() => passwordVisible.value ? 'text' : 'password')
const inputValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
  }
})

const handleInput = (event) => {
  const value = event.target.value
  emits('update:modelValue', value)
  emits('input', value)
}

const togglePasswordVisibility = () => {
  passwordVisible.value = !passwordVisible.value
}
</script>

<style lang="scss" scoped>
.password-input-container {
  width: 100%;

  .relative {
    position: relative;
  }

  .password-input {
    width: 100%;
    transition: all 0.2s ease;
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #F4F7FD;

    &:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
    }
  }

  .toggle-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.2s ease;
  }

  .error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 4px;
  }
}
</style>
