<template>
  <!-- 为了保持重复播放效果，隔段时间重新加载图片，并加时间戳防止缓存 -->
  <div class="loading-gif">
    <img :src="gifSrc" alt="Loading..." @load="handleImageLoad" @error="handleImageError"
      :style="{ opacity: imageLoaded ? 1 : 0 }" />
    <!-- iOS 兼容性备用方案 - 只在图片加载失败或超时时显示 -->
    <div v-if="showFallback" class="fallback-spinner">
      <div class="spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import loadingGif from "@/assets/loading.gif";

const gifSrc = ref("");
const imageLoaded = ref(false);
const showFallback = ref(false);
let timer: number | undefined;
let fallbackTimer: number | undefined;

function refreshGif() {
  // 加上时间戳和随机数，防止缓存（iOS Safari 兼容）
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  gifSrc.value = `${loadingGif}?t=${timestamp}&r=${random}`;
  imageLoaded.value = false;

  // 重新设置备用方案定时器
  if (fallbackTimer) {
    clearTimeout(fallbackTimer);
  }
  fallbackTimer = window.setTimeout(() => {
    if (!imageLoaded.value) {
      showFallback.value = true;
    }
  }, 500);
}

function handleImageLoad() {
  imageLoaded.value = true;
  showFallback.value = false;
  // 清除备用方案定时器，因为图片已成功加载
  if (fallbackTimer) {
    clearTimeout(fallbackTimer);
    fallbackTimer = undefined;
  }
}

function handleImageError() {
  console.warn('Loading GIF failed, using fallback spinner');
  showFallback.value = true;
}

onMounted(() => {
  refreshGif();

  // 定期刷新 GIF（iOS Safari 需要更频繁的刷新）
  timer = window.setInterval(refreshGif, 1200); // 缩短间隔以适应 iOS
});

onBeforeUnmount(() => {
  if (timer) clearInterval(timer);
  if (fallbackTimer) clearTimeout(fallbackTimer);
});
</script>

<style scoped lang="scss">
.loading-gif {
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  img {
    width: 100px;
    height: auto;
    transition: opacity 0.3s ease;
    // iOS Safari 兼容性
    -webkit-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  .fallback-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(172, 17, 64, 0.1);
      border-top-color: #ac1140;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      // iOS Safari 优化
      -webkit-animation: spin 1s linear infinite;
      will-change: transform;
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}
</style>
