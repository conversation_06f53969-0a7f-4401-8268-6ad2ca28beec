<template>
  <van-floating-bubble
    v-model:offset="offset"
    :axis="axis as any"
    :magnetic="magnetic as any"
    :gap="gap"
    @click="handleClick"
    @offset-change="handleOffsetChange"
    teleport="body"
  >
    <slot>Bubble</slot>
  </van-floating-bubble>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";

// 定义组件 Props
const props = defineProps({
  // 距离左边的距离（单位 px），与 right 二选一
  left: {
    type: Number,
    default: null,
  },
  // 距离右边的距离（单位 px），未提供 left 时生效
  right: {
    type: Number,
    default: null,
  },
  // 距离上边的距离（单位 px），与 bottom 二选一
  top: {
    type: Number,
    default: null,
  },
  // 距离下边的距离（单位 px），未提供 top 时生效
  bottom: {
    type: Number,
    default: null,
  },
  // 拖拽轴向，可选值为 x、y、xy
  axis: {
    type: String as () => "x" | "y" | "xy",
    default: "xy",
  },
  // 是否开启磁吸效果
  magnetic: {
    type: String as () => "x" | "y" | "xy",
    default: "x",
  },
  // 距离窗口边缘的最小间距
  gap: {
    type: Number,
    default: 16,
  },
});

// 定义自定义事件
const emit = defineEmits(["click", "offset-change"]);

// 气泡位置偏移量
const offset = ref({ x: 0, y: 0 });

// 获取实际可用的屏幕尺寸（考虑 PostCSS 最大宽度限制）
const getEffectiveScreenSize = () => {
  const container = document.querySelector(".h5-app-container") as HTMLElement;
  const width = container ? container.offsetWidth - 10 : 480;
  const height = container ? container.offsetHeight : window.innerHeight;
  return { width, height, offsetX: 0 };
};

// 初始化位置
const initPosition = () => {
  const { width: screenWidth, height: screenHeight, offsetX } = getEffectiveScreenSize();

  // 获取气泡实际尺寸（通过临时元素测量）
  const tempElement = document.createElement("div");
  tempElement.innerHTML = "<slot>Bubble</slot>";
  tempElement.style.visibility = "hidden";
  tempElement.style.position = "absolute";
  document.body.appendChild(tempElement);

  const bubbleWidth = tempElement.offsetWidth || 40; // 默认 40px
  const bubbleHeight = tempElement.offsetHeight || 40; // 默认 40px

  document.body.removeChild(tempElement);

  let x = 0;
  let y = 0;

  // 横向位置计算（相对于有效容器）
  if (props.left !== null) {
    x = props.left;
  } else if (props.right !== null) {
    // 注意：这里的 screenWidth 是有效容器宽度（480px），不是整个窗口宽度
    x = screenWidth - props.right - bubbleWidth;
  } else {
    // 默认值：距离左边 24px
    x = 24;
  }

  // 纵向位置计算
  if (props.top !== null) {
    y = props.top;
  } else if (props.bottom !== null) {
    y = screenHeight - props.bottom - bubbleHeight;
  } else {
    // 默认值：距离顶部 200px
    y = 200;
  }

  // 确保位置在有效容器范围内（相对于有效容器的坐标）
  x = Math.max(props.gap, Math.min(x, screenWidth - bubbleWidth - props.gap));
  y = Math.max(props.gap, Math.min(y, screenHeight - bubbleHeight - props.gap));

  // 转换为相对于整个窗口的绝对坐标
  const finalX = x + offsetX;

  offset.value = { x: finalX, y };
};

// 处理点击事件
const handleClick = (event: Event) => {
  emit("click", event);
};

// 处理位置变化事件
const handleOffsetChange = (newOffset: { x: number; y: number }) => {
  // 在位置变化时也检查是否需要调整
  setTimeout(() => {
    forceAdjustPosition();
  }, 100);

  emit("offset-change", newOffset);
};

// 监听窗口大小变化，重新计算位置
const handleResize = () => {
  const { width: screenWidth, height: screenHeight, offsetX } = getEffectiveScreenSize();

  // 获取气泡实际尺寸
  const bubbleElement = document.querySelector(".van-floating-bubble") as HTMLElement;
  const bubbleWidth = bubbleElement?.offsetWidth || 40;
  const bubbleHeight = bubbleElement?.offsetHeight || 40;

  // 当前位置需要减去容器偏移量来获取相对位置
  const currentRelativeX = offset.value.x - offsetX;

  // 确保气泡仍在有效容器范围内（相对坐标）
  const newRelativeX = Math.max(
    props.gap,
    Math.min(currentRelativeX, screenWidth - bubbleWidth - props.gap)
  );
  const newY = Math.max(
    props.gap,
    Math.min(offset.value.y, screenHeight - bubbleHeight - props.gap)
  );

  // 转换为相对于整个窗口的绝对坐标
  const newAbsoluteX = newRelativeX + offsetX;
  if (newAbsoluteX !== offset.value.x || newY !== offset.value.y) {
    offset.value = { x: newAbsoluteX, y: newY };
  }
};

// 强制调整 DOM 元素位置（用于大屏设备）
const forceAdjustPosition = () => {
  // 只在大屏设备上执行
  if (window.innerWidth <= 480) return;

  const bubbleElement = document.querySelector(".van-floating-bubble") as HTMLElement;
  if (!bubbleElement) return;

  // 解析当前 transform 的 x/y
  const match = bubbleElement.style.transform.match(
    /translate3d\(([-\d.]+)px,\s*([-\d.]+)px,\s*0px\)/
  );
  let x = 0,
    y = 0;
  if (match) {
    x = parseFloat(match[1]);
    y = parseFloat(match[2]);
  }

  const bubbleWidth = bubbleElement.offsetWidth || 60;
  // 计算 #app 容器的右边界
  const containerRightEdge = window.innerWidth / 2 + 240; // (100vw / 2) + (480px / 2)
  const maxX = containerRightEdge - bubbleWidth - (props.gap || 16);

  // 限制 x 最大值
  if (x > maxX) {
    bubbleElement.style.transform = `translate3d(${maxX}px, ${y}px, 0px)`;
  }
};

// 组件挂载时初始化位置
onMounted(() => {
  initPosition();
  window.addEventListener("resize", handleResize);

  // 延迟执行强制位置调整，确保 DOM 已渲染
  setTimeout(() => {
    forceAdjustPosition();

    // 设置定时器持续监控位置
    const positionMonitor = setInterval(() => {
      forceAdjustPosition();
    }, 1000);

    // 5秒后停止监控（避免性能问题）
    setTimeout(() => {
      clearInterval(positionMonitor);
    }, 5000);
  }, 500);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 监听 props 变化，重新计算位置
watch(
  () => [props.left, props.right, props.top, props.bottom],
  () => {
    initPosition();
  },
  { deep: true }
);

// 暴露方法给父组件
defineExpose({
  // 设置位置（接收相对于有效容器的坐标）
  setPosition: (x: number, y: number) => {
    const { offsetX } = getEffectiveScreenSize();
    const absoluteX = x + offsetX;
    console.log("📍 setPosition 坐标转换:", { relativeX: x, absoluteX, offsetX, y });
    offset.value = { x: absoluteX, y };
  },
  // 获取当前位置（返回相对于有效容器的坐标）
  getPosition: () => {
    const { offsetX } = getEffectiveScreenSize();
    const relativeX = offset.value.x - offsetX;
    return { x: relativeX, y: offset.value.y };
  },
  // 重置到初始位置
  resetPosition: () => {
    initPosition();
  },
});
</script>

<style lang="scss">
// 注意：van-floating-bubble 的样式需要全局样式，不能使用 scoped
.van-floating-bubble {
  // 重置 vant 默认样式
  width: fit-content !important;
  height: fit-content !important;
  background: none !important;
  border-radius: 0 !important;
  z-index: 99;

  // PC端拖拽优化
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;

  // 确保拖拽在PC端也能正常工作
  cursor: move;
  touch-action: none; // 防止触摸滚动干扰

  // 容器本身需要响应拖拽事件
  pointer-events: auto !important;

  // 子元素不应该干扰拖拽（但仍需要响应点击事件）
  img {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    pointer-events: none !important; // 只禁用图片的鼠标事件
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
  }

  // 其他子元素保持可点击
  * {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }

  // 关键修复：在大屏设备上，强制限制浮动气泡的最大 left 值
  @media (min-width: 481px) {
    // 计算 #app 容器的右边界：(100vw - 480px) / 2 + 480px
    // 减去气泡宽度（约60px）和间距，确保不会超出容器
    &[style*="left:"] {
      // 使用 JavaScript 动态设置 CSS 变量来限制位置
      left: min(var(--bubble-left, 0px), calc(50vw + 240px - 80px)) !important;
    }
  }
}
</style>

<style scoped>
/* scoped 样式用于组件内部 */
</style>
