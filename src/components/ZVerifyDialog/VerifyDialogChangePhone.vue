<!-- 绑定、变更手机号弹窗 -->
<template>
  <ZActionSheet
    v-model="visible"
    :title="getTypeInfo?.title"
    confirmText="Confirm"
    :showCancelButton="false"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
  >
    <div class="phone-input" v-if="dialogStep === 1">
      <label for="changePhone">Your phone number (09xx xxxx xxx)</label>
      <div class="phone-input-container">
        <span class="country-code">+63</span>
        <input
          id="changePhone"
          v-model="phone"
          type="number"
          inputmode="numeric"
          placeholder="Phone Number"
          maxlength="10"
        />
      </div>
    </div>
    <div class="dialog-content" v-if="dialogStep === 2">
      <div class="send-code-tip">
        A text message with a 6-digit code was just sent to
        <b>{{ formattedUserPhone }}</b>
      </div>
      <div class="phone-input-code">
        <label for="verCode">Enter a verification code</label>
        <div class="phone-input-container">
          <input
            id="verCode"
            v-model="verificationCode"
            maxlength="6"
            type="number"
            inputmode="numeric"
            placeholder="Enter the code"
            @input="handleCodeInput"
          />
          <CodeCountdownButton ref="countdownButtonRef" @click="checkPhoneIsRegister" />
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { sendCodeMsg, changePhone, bindPhone } from "@/api/setPhoneNumber";
import { GeetestMgr, GEETEST_TYPE } from "@/utils/GeetestMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { isPhilippinePhoneNumber } from "@/utils/core/tools";
import { GlobalEnum } from "@/utils/config/GlobalEnum";
import { PN_VERIFY_TYPE } from "./types";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";

const globalStore = useGlobalStore();
const { userInfo } = storeToRefs(globalStore);
const countdownButtonRef = ref();

const props = defineProps({
  // 显示弹窗
  showNextDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 设置手机号0、更改手机号2
  verifyType: {
    type: Number,
    default: 2,
    required: true,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
});
// 当前流程进度
const dialogStep = ref(1);
// 弹窗是否显示
const visible = ref(props.showNextDialog);
// 手机号
const phone = ref("");
// 验证码
const verificationCode = ref("");
// 是否已发送验证码
const hasSentCode = ref(false);

const resetData = () => {
  dialogStep.value = 1;
  phone.value = "";
  verificationCode.value = "";
  hasSentCode.value = false;
};

watch(
  () => props.showNextDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showNextDialog) {
      emit("update:showNextDialog", val);
    }
  }
);

const formattedUserPhone = computed(() => {
  const newPhone = phone.value || "";
  return newPhone.slice(0, 2) + "****" + newPhone.slice(6, 10);
});

// 处理验证码输入，只允许数字且限制6位
const handleCodeInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;

  // 只保留数字
  value = value.replace(/\D/g, "");

  // 限制最多6位
  if (value.length > 6) {
    value = value.slice(0, 6);
  }

  verificationCode.value = value;
  target.value = value;
};

const getTypeInfo = computed(() => {
  if (props.verifyType === PN_VERIFY_TYPE.SetPhoneNumber) {
    return {
      title: "Set Phone Number",
      gtype: GEETEST_TYPE.bind_pt_phone_code,
      msgType: GlobalEnum.SMS_TYPE.BIND_PHONE,
      geetestDevice: GEETEST_TYPE.bind_pt_phone,
    };
  } else if (props.verifyType === PN_VERIFY_TYPE.ChangePhoneNumber) {
    return {
      title: "Change Phone Number",
      gtype: GEETEST_TYPE.change_pt_phone_code,
      msgType: GlobalEnum.SMS_TYPE.UPDATE_PHONE,
      geetestDevice: GEETEST_TYPE.change_pt_phone,
    };
  }
});

//发送验证码之前先 geetest
const checkPhoneIsRegister = () => {
  GeetestMgr.instance.geetest_device(getTypeInfo.value.gtype, (succ) => {
    if (succ) {
      let ret = {};
      if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
        ret = succ;
      }
      checkPhoneIsRegister_true(ret);
    }
  });
};
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  let params = {
    phone: phone.value,
    telephoneCode: "+63",
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
    type: getTypeInfo.value?.msgType,
  };
  const { code, msg } = await sendCodeMsg(params);
  if (code === 200) {
    showToast("Verification code sent successfully");
    hasSentCode.value = true;
    if (countdownButtonRef.value) {
      countdownButtonRef.value.start();
    }
  } else {
    if (code === 600) {
      showToast("The Number is Linked to an Existing Account.");
    } else {
      msg && showToast(msg);
    }
  }
};

const emit = defineEmits(["update:showNextDialog", "complete"]);

const handleCancel = () => {
  emit("update:showNextDialog", false);
};

const handleConfirm = async () => {
  if (dialogStep.value === 1) {
    if (!isPhilippinePhoneNumber(phone.value)) {
      showToast("Wrong phone number");
      return;
    }
    dialogStep.value = 2;
  } else if (dialogStep.value === 2) {
    if (verificationCode.value.length !== 6) {
      showToast("Code Error,Please Try Again");
      return;
    }
    //加个验证
    GeetestMgr.instance.geetest_device(getTypeInfo.value.geetestDevice, async (succ) => {
      if (succ) {
        let ret = {};
        if (Object.getPrototypeOf(succ) !== Boolean.prototype) {
          ret = succ;
        }
        if (props.verifyType === PN_VERIFY_TYPE.SetPhoneNumber) {
          await handleBindPhone(ret);
        } else {
          await handleChangePhone(ret);
        }
      }
    });
  }
};
// 更新手机号
const handleChangePhone = async (ret) => {
  const { code, msg } = await changePhone({
    old_phone: userInfo.value.phone,
    phone: phone.value,
    verifyCode: verificationCode.value,
    telephoneCode: "+63",
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
  });
  if (code === 200) {
    showToast("Phone number set successfully.");
    handleCancel();
    globalStore.updateUserInfo({ phone: phone.value });
    props.succCallBack && props.succCallBack();
    emit("complete"); // 通知父组件完成
  } else {
    msg && showToast(msg);
  }
};

// 绑定手机号
const handleBindPhone = async (ret) => {
  const { code, msg } = await bindPhone({
    appPackageName: ALL_APP_SOURCE_CONFIG.appPackageName,
    deviceId: ALL_APP_SOURCE_CONFIG.deviceId,
    appVersion: ALL_APP_SOURCE_CONFIG.appVersion,
    phone: phone.value,
    verifyCode: verificationCode.value,
    telephoneCode: "+63",
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
  });
  if (code === 200) {
    showToast("Phone number set successfully.");
    handleCancel();
    globalStore.updateUserInfo({ phone: phone.value });
    emit("complete"); // 通知父组件完成
    props.succCallBack && props.succCallBack();
  } else {
    msg && showToast(msg);
  }
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }

  .phone-input-code {
    label {
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-bottom: 6px;
      display: inline-block;
      margin-bottom: 12px;
    }

    .phone-input-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      input {
        flex: 1;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 20px;
        padding: 0 12px;
        outline: none;
        background-color: #f4f7fd;
      }

      .get-code-btn {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 12px 16px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        background: #ac1140;
        color: #fff;
        text-align: center;
        font-size: 14px;
        cursor: pointer;

        &.is-counting {
          background: rgba(172, 17, 64, 0.5);
          cursor: not-allowed;
        }
      }
    }
  }
}

.phone-input {
  label {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 6px;
    display: inline-block;
    margin-bottom: 12px;
  }

  .phone-input-container {
    display: flex;
    align-items: center;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    background-color: #f4f7fd;
    overflow: hidden;

    .country-code {
      padding: 0 12px;
      color: #333;
      font-weight: 500;
      background-color: #f8f9fa;
      border-right: 1px solid #eee;
      display: flex;
      align-items: center;
      height: 100%;
      font-size: 14px;
    }

    input {
      flex: 1;
      height: 100%;
      border: none;
      outline: none;
      padding: 0 12px;
      background-color: transparent;
      font-size: 14px;

      &::placeholder {
        color: #999;
      }
    }
  }

  // 保持向后兼容，如果没有使用 phone-input-container
  > input {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #f4f7fd;
    width: 100%;
  }
}
</style>
