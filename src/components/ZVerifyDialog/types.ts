export enum PN_VERIFY_TYPE {
  SetPhoneNumber = 0,
  ChangePhoneNumber,
  ForgetPassword,
  AddWithdrawAccount,
  ChangeWithdrawAccount,
  SetPaymentPassword,
  ChangePaymentPassword,
  SetLoginPassword,
  ChangeLoginPassword,
}

export const PN_TITLE = {
  SetPhoneNumber: "Set Phone Number",
  ChangePaymentPwd: "Change Payment Password",
  ForgetPassword: "Change Login Password",
  ChangePhoneNumber: "Change Phone Number",
  AddWithdrawAccount: "Add Fund Account",
  ChangeWithdrawAccount: "Change Fund Account",
};

/**
 * 根据 PN_VERIFY_TYPE 枚举值获取对应标题
 * @param type PN_VERIFY_TYPE 枚举值
 * @returns 对应的标题文本，未匹配时返回默认文本
 */
export const getPNTitleByType = (type: PN_VERIFY_TYPE): string => {
  // 枚举值与标题的直接映射（严格对应 PN_TITLE 中的键值）
  const titleMap: Record<PN_VERIFY_TYPE, string> = {
    [PN_VERIFY_TYPE.SetPhoneNumber]: PN_TITLE.SetPhoneNumber, // 对应 "Set Phone Number"
    [PN_VERIFY_TYPE.ChangePhoneNumber]: PN_TITLE.ChangePhoneNumber, // 对应 "Change Phone Number"
    [PN_VERIFY_TYPE.ForgetPassword]: PN_TITLE.ForgetPassword, // 对应 "Change Login Password"
    [PN_VERIFY_TYPE.AddWithdrawAccount]: PN_TITLE.AddWithdrawAccount, // 对应 "Add Fund Account"
    [PN_VERIFY_TYPE.ChangeWithdrawAccount]: PN_TITLE.ChangeWithdrawAccount, // 对应 "Change Fund Account"
    [PN_VERIFY_TYPE.SetPaymentPassword]: PN_TITLE.ChangePaymentPwd, // 对应 "Change Payment Password"
    [PN_VERIFY_TYPE.ChangePaymentPassword]: PN_TITLE.ChangePaymentPwd, // 对应 "Change Payment Password"
    [PN_VERIFY_TYPE.SetLoginPassword]: PN_TITLE.ForgetPassword, // 复用 "Change Login Password"
    [PN_VERIFY_TYPE.ChangeLoginPassword]: PN_TITLE.ForgetPassword, // 复用 "Change Login Password"
  };

  // 返回对应标题，未匹配时返回默认文本
  return titleMap[type] || "Verification";
};
