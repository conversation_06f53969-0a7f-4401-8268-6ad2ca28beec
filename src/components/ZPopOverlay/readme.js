{
  /* <template>
  <div>
    <button @click="isShow = true">显示弹窗</button>

    <ZPopOverlay
      :show="isShow"
      @update:show="isShow = $event"
      animation-type="slideBottom"
      @click="isShow = false"
    >
      <div class="popup-content">
        <h3>自定义弹窗</h3>
        <p>这是使用 show 属性控制的弹窗</p>
        <button @click.stop="isShow = false">关闭</button>
      </div>
    </ZPopOverlay>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const isShow = ref(false);
</script> */
}
