<template>
  <van-overlay
    :show="internalShow"
    :z-index="zIndex"
    :position="position"
    @click="handleOverlayClick"
    class="animated-overlay"
    teleport="body"
  >
    <div
      class="overlay-content"
      :class="[
        'content-animate',
        animationType,
        { 'fade-in': animationType === 'fade' },
        { 'scale-in': animationType === 'scale' },
        { 'slide-in-top': animationType === 'slideTop' },
        { 'slide-in-bottom': animationType === 'slideBottom' },
        { 'slide-in-left': animationType === 'slideLeft' },
        { 'slide-in-right': animationType === 'slideRight' },
      ]"
      :style="contentStyleWithAnimation"
      @click.stop="handleContentClick"
    >
      <slot></slot>
    </div>
  </van-overlay>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

// 定义 props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  zIndex: {
    type: Number,
    default: 1000,
  },
  position: {
    type: String,
    default: "center",
  },
  // 自定义动画相关 props
  animationType: {
    type: String,
    default: "scale",
    values: ["fade", "scale", "slideTop", "slideBottom", "slideLeft", "slideRight"],
  },
  // 内容样式
  contentStyle: {
    type: Object,
    default: () => ({}),
  },
  // 动画持续时间（ms）
  animationDuration: {
    type: Number,
    default: 300,
  },
});
const internalShow = ref(props.show);

// 定义 emits，其中支持 update:show 用于 v-model 双向绑定
const emit = defineEmits(["update:show", "click"]);

// 计算带动画时长的样式
const contentStyleWithAnimation = computed(() => {
  return {
    ...props.contentStyle,
    "animation-duration": `${props.animationDuration}ms`,
    "transition-duration": `${props.animationDuration}ms`,
  };
});

// 处理点击事件
const handleOverlayClick = (e: MouseEvent) => {
  emit("click", e);
  emit("update:show", false);
};

// 处理内容区域点击（阻止冒泡）
const handleContentClick = (e: MouseEvent) => {
  // emit('content-click', e);
  // 不执行关闭操作，事件已被阻止冒泡
};

// 检测是否为真实移动设备
const isMobileDevice = () => {
  return (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
    "ontouchstart" in window
  );
};

// 监听外部 show 属性变化
watch(
  () => props.show,
  (newVal) => {
    internalShow.value = newVal;

    // 只对真实移动设备进行特殊处理
    if (newVal && isMobileDevice()) {
      setTimeout(() => {
        const overlayContent = document.querySelector(".overlay-content") as HTMLElement;
        if (overlayContent) {
          // 强制重绘
          overlayContent.style.display = "none";
          overlayContent.offsetHeight; // 触发重排
          overlayContent.style.display = "block";
          overlayContent.style.visibility = "visible";
          overlayContent.style.opacity = "1";
        }
      }, 100);
    }
  }
);

// 监听内部 show 变化，同步到外部
watch(internalShow, (newVal) => {
  emit("update:show", newVal);
});
</script>

<style scoped>
.animated-overlay {
  display: flex;
  justify-content: center;
  /* align-items: flex-start; */
  align-items: center;
  /* 顶部对齐 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.overlay-content {
  position: relative;
  z-index: 1000;
  will-change: transform, opacity;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* 淡入动画 */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 缩放动画 */
.scale-in {
  transform: scale(0.8);
  opacity: 0;
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 顶部滑入动画 */
.slide-in-top {
  transform: translateY(-50px);
  opacity: 0;
  animation: slideInTop 0.3s ease-out forwards;
}

@keyframes slideInTop {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 底部滑入动画 */
.slide-in-bottom {
  transform: translateY(50px);
  opacity: 0;
  animation: slideInBottom 0.3s ease-out forwards;
}

@keyframes slideInBottom {
  from {
    transform: translateY(50px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 左侧滑入动画 */
.slide-in-left {
  transform: translateX(-50px);
  opacity: 0;
  animation: slideInLeft 0.3s ease-out forwards;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 右侧滑入动画 */
.slide-in-right {
  transform: translateX(50px);
  opacity: 0;
  animation: slideInRight 0.3s ease-out forwards;
}

@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 真实移动设备特殊处理 - 使用更精准的媒体查询 */
@media (max-width: 768px) and (pointer: coarse) and (hover: none) {
  .overlay-content {
    /* 强制显示，解决真实移动设备渲染问题 */
    /* display: block !important; */
    visibility: visible !important;
    opacity: 1 !important;
    /* 确保层级正确 */
    z-index: 1000 !important;
    /* 防止被其他元素遮挡 */
    position: relative !important;
    display: flex !important;
    justify-content: center;
    align-items: center;
  }
}

/* iOS Safari 特殊处理 */
@supports (-webkit-touch-callout: none) {
  @media (max-width: 768px) {
    .overlay-content {
      /* iOS Safari 特殊优化 */
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      will-change: transform;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000 !important;
    }
  }
}
</style>
