<template>
  <div class="empty-placeholder">
    <!-- 图片区域，支持自定义图片地址 -->
    <template v-if="image">
      <img :src="image" :alt="altText" class="empty-image">
    </template>
    <DefaultImage v-else></DefaultImage>
    <!-- 文案区域，支持自定义文案 -->
    <p class="empty-text">
      {{ text }}
    </p>
    <p class="empty-desc" v-if="desc">{{ desc }}</p>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import DefaultImage from '@/assets/images/noData.svg'


// 定义 props
const props = defineProps({
  // 空状态图片地址
  image: {
    type: String,
    default: '' // 可设置默认图片，如需要可替换为实际默认图地址
  },
  // 空状态文案
  text: {
    type: String,
    default: 'No Data'
  },
  // 空状态文案描述
  desc: {
    type: String,
    default: ''
  },
  // 图片 alt 文本（可选）
  altText: {
    type: String,
    default: 'empty placeholder'
  }
})
</script>

<style scoped>
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70%;
  /* background-color: #fff; */
}

.empty-image {
  width: 130px;
  height: 130px;
  flex-shrink: 0;
  margin-bottom: 12px;
}

.empty-text {
  color: #999;
  text-align: center;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}

.empty-desc {
  margin-top: 10px;
  color: #999;
  text-align: center;
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
}
</style>
