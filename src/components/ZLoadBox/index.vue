<template>
  <div>
    <!-- 加载/失败状态 -->
    <div v-show="status !== 'success'" class="vant-pro-load-box" v-bind="$attrs">
      <ZTransition mode="out-in">
        <!-- 失败状态 -->
        <div v-if="status === 'fail'" class="vant-pro-load-box__fail" @click="handleRetry">
          <slot name="fail">
            <Icon :name="computedIcon" :color="color" class="icon" />
            <div class="vant-pro-load-box__fail__text" :style="{ color }">
              {{ computedText }}
            </div>
          </slot>
        </div>
        <!-- 加载中状态 -->
        <div v-else class="vant-pro-load-box__loading">
          <slot name="loading">
            <Loading :color="color" :text-color="color" :type="loadingType" vertical>
              {{ computedText }}
            </Loading>
          </slot>
        </div>
      </ZTransition>
    </div>
    <!-- 成功状态内容 -->
    <div v-show="status === 'success'" class="vant-pro-content-box" v-bind="$attrs">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { Loading, Icon } from "vant";

type LoadingType = Loading["Type"];
type LoadStatus = "loading" | "success" | "fail";

interface Props {
  /** 数据获取函数（返回 Promise） */
  fetchData: () => Promise<any>;
  /** 是否自动加载（默认 true） */
  autoLoad?: boolean;
  /** 加载中图标类型 */
  loadingType?: LoadingType;
  /** 加载中文本 */
  loadingText?: string;
  /** 失败图标名称 */
  failIcon?: string;
  /** 失败文本 */
  failText?: string;
  /** 颜色 */
  color?: string;
  /** 最大重试次数（默认 3 次） */
  maxRetry?: number;
  /** 重试间隔时间（毫秒，默认 1000） */
  retryInterval?: number;
}

const props = withDefaults(defineProps<Props>(), {
  autoLoad: true,
  loadingType: "circle",
  loadingText: "loading...",
  failIcon: "warning-o",
  failText: "Failed to load. Click to retry.",
  color: "#1989fa",
  maxRetry: 3,
  retryInterval: 1000,
});

const emit = defineEmits<{
  (e: "success", data: any): void; // 加载成功事件
  (e: "fail", error: Error): void; // 加载失败事件
  (e: "retry"): void; // 重试事件
}>();

// 内部状态
const status = ref<LoadStatus>("loading");
const retryCount = ref(0); // 重试次数计数

const loadData = async () => {
  status.value = "loading";
  retryCount.value++;
  try {
    const data = await props?.fetchData();
    status.value = "success";
    // emit("success", data); // 触发成功事件
  } catch (error) {
    if (retryCount.value < props.maxRetry) {
      // 等待重试间隔后自动重试
      await new Promise((resolve) => setTimeout(resolve, props.retryInterval));
      await loadData();
    } else {
      status.value = "fail";
      // emit("fail", error as Error); // 触发失败事件
    }
  }
};

// 组件挂载后自动加载
onMounted(() => {
  if (props.autoLoad) {
    loadData();
  }
});

// 计算当前图标
const computedIcon = computed(
  () => props.icon || (status.value === "fail" ? props.failIcon : props.loadingType)
);

// 计算当前文本
const computedText = computed(
  () => props.text || (status.value === "fail" ? props.failText : props.loadingText)
);

// 重试处理
const handleRetry = () => {
  retryCount.value = 0; // 重置重试次数
  loadData();
};
// 暴露 loadData 方法给父组件
defineExpose({
  loadData,
});
</script>

<style lang="scss" scoped>
.vant-pro-load-box {
  /* 保持原有样式，新增点击反馈 */
  &__fail {
    cursor: pointer;
    &:active {
      opacity: 0.8;
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
}
.vant-pro-load-box__fail {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
