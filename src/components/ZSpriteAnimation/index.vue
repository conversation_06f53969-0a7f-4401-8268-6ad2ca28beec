<template>
  <div class="sprite-animation" :style="spriteStyle"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// 组件参数
const props = defineProps({
  // 雪碧图路径
  spriteUrl: { type: String, required: true },
  // 总帧数
  frameCount: { type: Number, required: true },
  // 单帧宽度（像素）
  frameWidth: { type: Number, required: true },
  // 单帧高度（像素）
  frameHeight: { type: Number, required: true },
  // 帧率
  fps: { type: Number, default: 12 },
  // 是否循环
  loop: { type: Boolean, default: true },
  // 动画方向（horizontal/vertical）
  direction: { type: String, default: 'horizontal' }
});

// 状态管理
const currentFrameIndex = ref(0);
const isPlaying = ref(false);
const timer = ref(null);

// 计算雪碧图样式（核心：动态切换background-position）
const spriteStyle = computed(() => {
  let position = '0 0';
  // 水平排列的雪碧图（左右切换帧）
  if (props.direction === 'horizontal') {
    position = `-${currentFrameIndex.value * props.frameWidth}px 0`;
  }
  // 垂直排列的雪碧图（上下切换帧）
  else {
    position = `0 -${currentFrameIndex.value * props.frameHeight}px`;
  }

  return {
    width: `${props.frameWidth}px`,
    height: `${props.frameHeight}px`,
    backgroundImage: `url(${props.spriteUrl})`,
    backgroundPosition: position,
    backgroundRepeat: 'no-repeat',
    backgroundSize: props.direction === 'horizontal'
      ? `${props.frameWidth * props.frameCount}px ${props.frameHeight}px`
      : `${props.frameWidth}px ${props.frameHeight * props.frameCount}px`
  };
});

// 播放下一帧
const playNextFrame = () => {
  currentFrameIndex.value++;

  if (!props.loop && currentFrameIndex.value >= props.frameCount) {
    stopAnimation();
    currentFrameIndex.value = props.frameCount - 1;
    return;
  }

  if (currentFrameIndex.value >= props.frameCount) {
    currentFrameIndex.value = 0;
  }
};

// 控制方法
const startAnimation = () => {
  if (isPlaying.value || props.frameCount === 0) return;

  isPlaying.value = true;
  const interval = 1000 / props.fps;
  timer.value = setInterval(playNextFrame, interval);
};

const stopAnimation = () => {
  isPlaying.value = false;
  clearInterval(timer.value);
};

const resetAnimation = () => {
  stopAnimation();
  currentFrameIndex.value = 0;
};

onMounted(() => {
  startAnimation();
});

onUnmounted(() => {
  stopAnimation();
});

defineExpose({
  startAnimation,
  stopAnimation,
  resetAnimation
});
</script>

<style scoped lang="scss">
.sprite-animation {
  display: inline-block;
}
</style>
