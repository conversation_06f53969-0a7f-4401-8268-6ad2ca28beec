<template>
  <div class="z-image-container">
    <van-image class="progressive-image" :lazy-load="lazyLoad" :fit="fit" :src="src" :alt="alt" draggable="false"
      @dragstart.prevent>
      <!-- 自定义加载状态 -->
      <template #loading>
        <div class="placeholder-container loading" :style="containerStyle">
          <img :src="computedPlaceholder.loading" :style="placeholderImageStyle" alt="Loading..."
            class="placeholder-image" />
        </div>
      </template>

      <!-- 自定义错误状态 -->
      <template #error>
        <div class="placeholder-container error" :style="containerStyle">
          <img :src="computedPlaceholder.error" :style="placeholderImageStyle" alt="Error loading image"
            class="placeholder-image" />
        </div>
      </template>
    </van-image>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import nsLogoBase64 from "@/assets/constants/nsLogoBase64";
import homeBannerBase64 from "@/assets/constants/homeBannerBase64";
import homeVideoBase64 from "@/assets/constants/homeVideoBase64";
import promosBase64 from "@/assets/constants/promosBase64";
import type { PlaceholderType, ImageFit } from "@/types/ZImage";

// Props 接口定义
interface Props {
  lazyLoad?: boolean;
  type?: PlaceholderType;
  src: string;
  fit?: ImageFit;
  background?: string;
  alt?: string;
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  lazyLoad: true,
  type: 'game',
  fit: 'cover',
  background: '#E3E3E3',
  alt: ''
});

// 占位符配置映射
const PLACEHOLDER_CONFIG = {
  game: { loading: nsLogoBase64, error: nsLogoBase64 },
  promos: { loading: promosBase64, error: promosBase64 },
  video: { loading: homeVideoBase64, error: homeVideoBase64 },
  banner: { loading: homeBannerBase64, error: homeBannerBase64 },
} as const;

// 计算属性：根据 type 获取对应的占位符图片
const computedPlaceholder = computed(() => {
  return PLACEHOLDER_CONFIG[props.type] || PLACEHOLDER_CONFIG.game;
});

// 计算属性：容器样式
const containerStyle = computed(() => ({
  backgroundColor: props.background
}));

// 计算属性：占位符图片样式
const placeholderImageStyle = computed(() => {
  if (props.type === 'game') {
    return {
      width: '90px',
      height: '90px'
    };
  }
  return {
    width: '100%',
    height: '100%'
  };
});
</script>

<style lang="scss" scoped>
.z-image-container {
  width: 100%;
  height: 100%;
  position: relative;

  .progressive-image {
    width: 100%;
    height: 100%;
    /* 图片本身的层级 */

    img {
      transition: filter 0.3s ease-in-out;
    }

    &.blur {
      filter: blur(10px);
    }
  }

  .placeholder-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    /* 确保加载状态不会遮挡其他UI元素 */

    &.loading {}

    &.error {
      opacity: 0.8;
      /* 错误状态的层级也要低于游戏标签和按钮 */
    }

    .placeholder-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      // iOS Safari 优化
      -webkit-user-select: none;
      user-select: none;
      -webkit-touch-callout: none;
      -webkit-tap-highlight-color: transparent;
    }
  }

  // 深度选择器优化 van-image 组件
  &:deep(.van-image) {
    width: 100%;
    height: 100%;
    /* 确保 van-image 组件的层级正确 */

    .van-image__loading,
    .van-image__error,
    .van-image__img {
      width: 100%;
      height: 100%;
      /* 确保所有状态的层级都低于UI元素 */
    }

    .van-image__img {
      object-fit: inherit;
      transition: opacity 0.3s ease-in-out;
    }

  }
}
</style>
