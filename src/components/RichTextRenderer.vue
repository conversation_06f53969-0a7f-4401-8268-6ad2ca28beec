<script setup lang="ts">
import { defineProps } from "vue";

defineProps({
  content: {
    type: String,
    required: true,
  },
});
</script>

<template>
  <div class="rich-text-container" v-html="content"></div>
</template>

<style scoped lang="scss">
.rich-text-container {
  font-size: 14px;
  color: #222;
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  :deep(p) {
    margin-bottom: 16px;
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 28px;
    /* 200% */
  }

  :deep(img) {
    width: 100%;
    max-width: 335px;
    height: auto;
    border-radius: 20px;
    margin: 16px 0;
    object-fit: cover;
  }

  :deep(a) {
    text-decoration: underline;
    // color: #007bff;
    // text-decoration: none;


    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
