<template>
  <ZDialog
    v-model="visible"
    title="Tips"
    :show-close="true"
    confirm-text="Got it"
    cancel-text="View Instructions"
    :on-confirm="handleGotIt"
    :on-cancel="handleViewInstructions"
    class="safari-tips-dialog"
    :style="{ width: '375px' }"
  >
    <!-- 自定义内容 -->
    <div class="safari-tips-body">
      <p class="message">
        To ensure you can enter the game smoothly, please make sure pop-up windows are enabled in
        your Safari settings.
      </p>

      <!-- 单选框 -->
      <div class="checkbox-container" @click="toggleDontShow">
        <div class="checkbox" :class="{ checked: dontShowToday }">
          <van-icon v-if="dontShowToday" name="success" size="16" color="#fff" />
        </div>
        <span class="checkbox-text">Don't show this content again today.</span>
      </div>
    </div>
  </ZDialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from "vue";
import ZDialog from "@/components/ZDialog/index.vue";

interface Props {
  modelValue: boolean;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "gotIt", dontShowToday: boolean): void;
  (e: "viewInstructions"): void;
  (e: "close"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dontShowToday = ref(false);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

const toggleDontShow = () => {
  dontShowToday.value = !dontShowToday.value;
};

const handleGotIt = () => {
  emit("gotIt", dontShowToday.value);
  visible.value = false;
};

const handleViewInstructions = () => {
  emit("viewInstructions");
  visible.value = false;
};

// 监听弹窗显示状态，当显示时设置按钮样式
</script>

<style lang="scss" scoped>
.safari-tips-body {
  .message {
    margin: 0 0 20px 0;
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
    text-align: left;
  }

  .checkbox-container {
    display: flex;
    align-items: center;
    gap: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s;

    .checkbox {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background-color: #b91c5c;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s;

      &:not(.checked) {
        background-color: transparent;
        border: 2px solid #b91c5c;
      }
    }

    .checkbox-text {
      flex: 1;
      color: #999;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>

<style lang="scss">
.safari-tips-dialog {
  width: 350px;
  .van-button__text {
    font-size: 14px !important;
    font-family: Inter;
    font-weight: 700;
  }
  .custom-footer {
    .van-button:first-child {
      width: auto;
    }
    .van-button:last-child {
      width: 131px;
    }
  }
}
</style>
