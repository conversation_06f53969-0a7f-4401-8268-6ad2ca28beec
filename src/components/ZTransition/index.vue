<template>
  <!-- 动态切换 Transition 或 TransitionGroup -->
  <component :is="transitionComponent" v-bind="transitionProps">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Transition, TransitionGroup } from "vue";

// 过渡缓动函数类型
type TransitionEasing = "linear" | "ease" | "ease-in" | "ease-out" | "ease-in-out";

interface Props {
  /** 过渡持续时间（秒） */
  duration?: number;
  /** 过渡缓动函数 */
  easing?: TransitionEasing;
  /** 是否启用列表过渡（使用 TransitionGroup） */
  list?: boolean;
  /** 初始渲染是否应用过渡 */
  appear?: boolean;
  /** 过渡类名前缀 */
  name?: string;
  /** 是否使用 CSS 过渡 */
  css?: boolean;
  /** 过渡结束后是否保留 DOM */
  persisted?: boolean;
  /** 过渡模式（out-in/in-out） */
  mode?: "out-in" | "in-out";
}

const props = withDefaults(defineProps<Props>(), {
  duration: 0.3,
  easing: "ease-out",
  list: false,
  appear: true,
  name: "v-transition",
  css: true,
  persisted: false,
  mode: "out-in",
});

// 动态选择过渡组件
const transitionComponent = computed(() => (props.list ? TransitionGroup : Transition));

// 合并过渡属性
const transitionProps = computed(() => ({
  // 基础属性
  css: props.css,
  name: props.name,
  appear: props.appear,
  persisted: props.persisted,

  // 动态计算的样式属性
  style: {
    "--transition-duration": `${props.duration}s`,
    "--transition-easing": props.easing,
  },

  // 仅对单个元素有效的属性
  ...(!props.list && { mode: props.mode }),

  // 列表过渡特有属性
  ...(props.list && {
    tag: "div",
    moveClass: `${props.name}-move`,
  }),
}));
</script>

<style lang="scss" scoped>
/* 基础过渡样式 */
.v-transition {
  &-enter-active,
  &-leave-active {
    transition: opacity var(--transition-duration) var(--transition-easing),
      transform var(--transition-duration) var(--transition-easing);
    will-change: opacity, transform;
  }

  &-enter-from,
  &-leave-to {
    opacity: 0;
    transform: scale(0.95);
  }

  /* 列表过渡移动动画 */
  &-move {
    transition: transform var(--transition-duration) var(--transition-easing);
    will-change: transform;
  }
}
</style>
