import { onMounted, onUnmounted } from "vue";
import { MayaTokenManager } from "@/utils/managers/MayaTokenManage";

/**
 * Vue Composition API Hook for Maya Token Management
 */
export function useMayaToken() {
  const tokenManager = MayaTokenManager.getInstance();

  onMounted(() => {
    // 组件挂载时启动token刷新
    tokenManager.startTokenRefreshSchedule();
  });

  onUnmounted(() => {
    // 组件卸载时停止token刷新
    tokenManager.stopTokenRefreshSchedule();
  });

  return {
    setToken: (token: string) => tokenManager.setToken(token),
    getToken: () => tokenManager.getToken(),
    startRefresh: () => tokenManager.startTokenRefreshSchedule(),
    stopRefresh: () => tokenManager.stopTokenRefreshSchedule(),
  };
}
