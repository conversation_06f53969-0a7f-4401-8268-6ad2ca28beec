/**
 * 异常处理 Hook
 * 用于统一处理页面接口异常和 ExceptionRefresh 组件的状态管理
 */

import { ref, computed } from 'vue';

interface ExceptionHandlerOptions {
  /** 自动重试次数 */
  autoRetryCount?: number;
  /** 自动重试间隔（毫秒） */
  autoRetryDelay?: number;
  /** 是否在错误时显示 toast */
  showErrorToast?: boolean;
  /** 错误类型过滤器，返回 true 表示显示 ExceptionRefresh */
  errorFilter?: (error: any) => boolean;
}

interface ExceptionState {
  /** 是否有错误 */
  hasError: boolean;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 错误信息 */
  errorMessage: string;
  /** 重试次数 */
  retryCount: number;
}

export function useExceptionHandler(options: ExceptionHandlerOptions = {}) {
  const {
    autoRetryCount = 2,
    autoRetryDelay = 1000,
    showErrorToast = false,
    errorFilter = (error) => {
      // 默认过滤器：网络错误、超时错误显示 ExceptionRefresh
      return error?.code === 'NETWORK_ERROR' || 
             error?.code === 'TIMEOUT' || 
             error?.message?.includes('timeout') ||
             error?.message?.includes('network');
    }
  } = options;

  // 响应式状态
  const state = ref<ExceptionState>({
    hasError: false,
    isLoading: false,
    errorMessage: '',
    retryCount: 0
  });

  // 计算属性
  const shouldShowException = computed(() => state.value.hasError);
  const isRetrying = computed(() => state.value.isLoading && state.value.retryCount > 0);

  /**
   * 执行异步操作，自动处理异常
   */
  const executeWithHandler = async <T>(
    asyncFn: () => Promise<T>,
    customRetryCount?: number
  ): Promise<T | null> => {
    const maxRetries = customRetryCount ?? autoRetryCount;
    
    // 重置状态
    if (state.value.retryCount === 0) {
      state.value.hasError = false;
      state.value.errorMessage = '';
    }
    
    state.value.isLoading = true;

    try {
      const result = await asyncFn();
      
      // 成功时重置所有状态
      state.value.hasError = false;
      state.value.errorMessage = '';
      state.value.retryCount = 0;
      
      return result;
    } catch (error: any) {
      console.error('Execute failed:', error);
      
      // 判断是否需要自动重试
      if (state.value.retryCount < maxRetries) {
        state.value.retryCount++;
        
        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, autoRetryDelay));
        return executeWithHandler(asyncFn, customRetryCount);
      }
      
      // 重试次数用完，处理错误
      state.value.errorMessage = error?.message || 'Unknown error';
      
      // 根据错误类型决定是否显示 ExceptionRefresh
      if (errorFilter(error)) {
        state.value.hasError = true;
      } else if (showErrorToast) {
        // 显示 toast 错误提示
        const { showToast } = await import('vant');
        showToast(state.value.errorMessage);
      }
      
      return null;
    } finally {
      state.value.isLoading = false;
    }
  };

  /**
   * 手动触发刷新
   */
  const refresh = async <T>(asyncFn: () => Promise<T>): Promise<T | null> => {
    // 重置重试计数
    state.value.retryCount = 0;
    return executeWithHandler(asyncFn);
  };

  /**
   * 重置状态
   */
  const reset = () => {
    state.value.hasError = false;
    state.value.isLoading = false;
    state.value.errorMessage = '';
    state.value.retryCount = 0;
  };

  /**
   * 手动设置错误状态
   */
  const setError = (error: string | Error, showException = true) => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    state.value.errorMessage = errorMessage;
    state.value.hasError = showException;
    state.value.isLoading = false;
  };

  /**
   * 手动设置加载状态
   */
  const setLoading = (loading: boolean) => {
    state.value.isLoading = loading;
  };

  return {
    // 状态
    state: computed(() => state.value),
    hasError: computed(() => state.value.hasError),
    isLoading: computed(() => state.value.isLoading),
    errorMessage: computed(() => state.value.errorMessage),
    retryCount: computed(() => state.value.retryCount),
    shouldShowException,
    isRetrying,
    
    // 方法
    executeWithHandler,
    refresh,
    reset,
    setError,
    setLoading
  };
}

/**
 * 页面数据加载 Hook
 * 专门用于页面初始化数据加载的场景
 */
export function usePageDataLoader() {
  const exceptionHandler = useExceptionHandler({
    autoRetryCount: 1,
    autoRetryDelay: 1500,
    showErrorToast: false
  });

  /**
   * 加载页面数据
   */
  const loadPageData = async <T>(
    dataLoader: () => Promise<T>,
    onSuccess?: (data: T) => void,
    onError?: (error: any) => void
  ): Promise<void> => {
    const result = await exceptionHandler.executeWithHandler(dataLoader);
    
    if (result !== null) {
      onSuccess?.(result);
    } else {
      onError?.(new Error(exceptionHandler.errorMessage.value));
    }
  };

  /**
   * 刷新页面数据
   */
  const refreshPageData = async <T>(
    dataLoader: () => Promise<T>,
    onSuccess?: (data: T) => void
  ): Promise<void> => {
    const result = await exceptionHandler.refresh(dataLoader);
    
    if (result !== null) {
      onSuccess?.(result);
    }
  };

  return {
    ...exceptionHandler,
    loadPageData,
    refreshPageData
  };
}

/**
 * 多接口并行加载 Hook
 */
export function useMultiApiLoader() {
  const exceptionHandler = useExceptionHandler({
    autoRetryCount: 1,
    autoRetryDelay: 1000
  });

  /**
   * 并行加载多个接口
   */
  const loadMultipleApis = async <T extends Record<string, any>>(
    apiMap: Record<keyof T, () => Promise<any>>,
    criticalKeys?: (keyof T)[]
  ): Promise<Partial<T> | null> => {
    const result = await exceptionHandler.executeWithHandler(async () => {
      const promises = Object.entries(apiMap).map(async ([key, apiFn]) => {
        try {
          const data = await (apiFn as () => Promise<any>)();
          return { key, data, success: true };
        } catch (error) {
          return { key, error, success: false };
        }
      });

      const results = await Promise.all(promises);
      
      // 检查关键接口是否失败
      if (criticalKeys) {
        const criticalFailed = results.some(result => 
          criticalKeys.includes(result.key as keyof T) && !result.success
        );
        
        if (criticalFailed) {
          throw new Error('Critical API failed');
        }
      }

      // 构建结果对象
      const data: Partial<T> = {};
      results.forEach(result => {
        if (result.success) {
          (data as any)[result.key] = result.data;
        }
      });

      return data;
    });

    return result;
  };

  return {
    ...exceptionHandler,
    loadMultipleApis
  };
}
