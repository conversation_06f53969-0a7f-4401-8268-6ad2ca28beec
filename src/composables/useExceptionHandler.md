# useExceptionHandler 异常处理 Hook

## 概述

`useExceptionHandler` 是一个 Vue 3 Composition API Hook，用于统一处理页面接口异常和 ExceptionRefresh 组件的状态管理。它提供了自动重试、错误过滤、状态管理等功能，让异常处理变得简单而优雅。

## 功能特性

- ✅ **自动重试机制**：接口失败时自动重试指定次数
- ✅ **智能错误过滤**：区分网络错误和业务错误，只对网络错误显示异常页面
- ✅ **状态管理**：统一管理加载、错误、重试等状态
- ✅ **多种使用模式**：基础模式、页面数据加载模式、多接口并行模式
- ✅ **TypeScript 支持**：完整的类型定义和智能提示

## 安装使用

```typescript
import { 
  useExceptionHandler, 
  usePageDataLoader, 
  useMultiApiLoader 
} from '@/composables/useExceptionHandler';
```

## API 文档

### useExceptionHandler(options?)

基础异常处理 Hook

#### 参数

```typescript
interface ExceptionHandlerOptions {
  /** 自动重试次数，默认 2 */
  autoRetryCount?: number;
  /** 自动重试间隔（毫秒），默认 1000 */
  autoRetryDelay?: number;
  /** 是否在错误时显示 toast，默认 false */
  showErrorToast?: boolean;
  /** 错误类型过滤器，返回 true 表示显示 ExceptionRefresh */
  errorFilter?: (error: any) => boolean;
}
```

#### 返回值

```typescript
{
  // 状态
  state: ComputedRef<ExceptionState>;
  hasError: ComputedRef<boolean>;
  isLoading: ComputedRef<boolean>;
  errorMessage: ComputedRef<string>;
  retryCount: ComputedRef<number>;
  shouldShowException: ComputedRef<boolean>;
  isRetrying: ComputedRef<boolean>;
  
  // 方法
  executeWithHandler: <T>(asyncFn: () => Promise<T>) => Promise<T | null>;
  refresh: <T>(asyncFn: () => Promise<T>) => Promise<T | null>;
  reset: () => void;
  setError: (error: string | Error, showException?: boolean) => void;
  setLoading: (loading: boolean) => void;
}
```

### usePageDataLoader()

专门用于页面初始化数据加载的 Hook

#### 返回值

```typescript
{
  // 继承 useExceptionHandler 的所有属性和方法
  ...useExceptionHandler,
  
  // 额外方法
  loadPageData: <T>(
    dataLoader: () => Promise<T>,
    onSuccess?: (data: T) => void,
    onError?: (error: any) => void
  ) => Promise<void>;
  
  refreshPageData: <T>(
    dataLoader: () => Promise<T>,
    onSuccess?: (data: T) => void
  ) => Promise<void>;
}
```

### useMultiApiLoader()

用于多接口并行加载的 Hook

#### 返回值

```typescript
{
  // 继承 useExceptionHandler 的所有属性和方法
  ...useExceptionHandler,
  
  // 额外方法
  loadMultipleApis: <T>(
    apiMap: Record<keyof T, () => Promise<any>>,
    criticalKeys?: (keyof T)[]
  ) => Promise<Partial<T> | null>;
}
```

## 使用示例

### 1. 基础用法

```vue
<template>
  <div class="page">
    <div v-if="!shouldShowException">
      <div v-if="isLoading">加载中...</div>
      <div v-else>{{ data }}</div>
    </div>
    
    <ExceptionRefresh 
      v-if="shouldShowException"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useExceptionHandler } from '@/composables/useExceptionHandler';
import ExceptionRefresh from '@/components/ExceptionRefresh.vue';

const data = ref(null);

const {
  shouldShowException,
  isLoading,
  executeWithHandler,
  refresh
} = useExceptionHandler({
  autoRetryCount: 2,
  autoRetryDelay: 1500
});

// 获取数据
const fetchData = async () => {
  const response = await api.getData();
  return response.data;
};

// 初始化
const init = async () => {
  const result = await executeWithHandler(fetchData);
  if (result) {
    data.value = result;
  }
};

// 刷新
const handleRefresh = async () => {
  const result = await refresh(fetchData);
  if (result) {
    data.value = result;
  }
};

// 页面加载时初始化
onMounted(init);
</script>
```

### 2. 页面数据加载模式

```vue
<template>
  <div class="home-page">
    <div v-if="!shouldShowException">
      <ZLoading v-if="isLoading" />
      <div v-else>
        <Banner :banners="banners" />
        <GameList :games="games" />
        <PromoList :promos="promos" />
      </div>
    </div>
    
    <ExceptionRefresh 
      v-if="shouldShowException"
      title="首页加载失败"
      message="无法加载首页内容，请检查网络连接"
      @refresh="refreshPage"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { usePageDataLoader } from '@/composables/useExceptionHandler';

const banners = ref([]);
const games = ref([]);
const promos = ref([]);

const {
  shouldShowException,
  isLoading,
  loadPageData,
  refreshPageData
} = usePageDataLoader();

// 加载首页数据
const loadHomeData = async () => {
  const [bannersRes, gamesRes, promosRes] = await Promise.all([
    api.getBanners(),
    api.getGames(),
    api.getPromos()
  ]);
  
  return {
    banners: bannersRes.data,
    games: gamesRes.data,
    promos: promosRes.data
  };
};

// 初始化页面
const initPage = async () => {
  await loadPageData(
    loadHomeData,
    (data) => {
      banners.value = data.banners;
      games.value = data.games;
      promos.value = data.promos;
    }
  );
};

// 刷新页面
const refreshPage = async () => {
  await refreshPageData(
    loadHomeData,
    (data) => {
      banners.value = data.banners;
      games.value = data.games;
      promos.value = data.promos;
    }
  );
};

onMounted(initPage);
</script>
```

### 3. 多接口并行加载模式

```vue
<template>
  <div class="dashboard">
    <div v-if="!shouldShowException">
      <ZLoading v-if="isLoading" />
      <div v-else>
        <UserProfile v-if="userData" :user="userData" />
        <WalletCard v-if="walletData" :wallet="walletData" />
        <RecentOrders v-if="ordersData" :orders="ordersData" />
        <Notifications v-if="notificationsData" :notifications="notificationsData" />
      </div>
    </div>
    
    <ExceptionRefresh 
      v-if="shouldShowException"
      @refresh="refreshDashboard"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useMultiApiLoader } from '@/composables/useExceptionHandler';

const userData = ref(null);
const walletData = ref(null);
const ordersData = ref(null);
const notificationsData = ref(null);

const {
  shouldShowException,
  isLoading,
  loadMultipleApis
} = useMultiApiLoader();

// 定义接口映射
const apiMap = {
  user: () => api.getUserInfo(),
  wallet: () => api.getWalletInfo(),
  orders: () => api.getRecentOrders(),
  notifications: () => api.getNotifications()
};

// 关键接口（这些接口失败会显示异常页面）
const criticalKeys = ['user', 'wallet'];

// 加载仪表板数据
const loadDashboard = async () => {
  const result = await loadMultipleApis(apiMap, criticalKeys);
  
  if (result) {
    userData.value = result.user;
    walletData.value = result.wallet;
    ordersData.value = result.orders;
    notificationsData.value = result.notifications;
  }
};

// 刷新仪表板
const refreshDashboard = async () => {
  await loadDashboard();
};

onMounted(loadDashboard);
</script>
```

### 4. 自定义错误过滤器

```typescript
const {
  shouldShowException,
  executeWithHandler
} = useExceptionHandler({
  autoRetryCount: 3,
  showErrorToast: true,
  errorFilter: (error) => {
    // 自定义错误过滤逻辑
    const networkErrors = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'CONNECTION_REFUSED'
    ];
    
    return networkErrors.includes(error?.code) || 
           error?.message?.includes('timeout') ||
           error?.status >= 500;
  }
});
```

### 5. 手动控制状态

```typescript
const {
  shouldShowException,
  isLoading,
  setError,
  setLoading,
  reset
} = useExceptionHandler();

// 手动设置错误
const handleCustomError = () => {
  setError('自定义错误信息', true);
};

// 手动设置加载状态
const handleCustomLoading = () => {
  setLoading(true);
  
  setTimeout(() => {
    setLoading(false);
  }, 2000);
};

// 重置所有状态
const handleReset = () => {
  reset();
};
```

## 最佳实践

### 1. 错误分类处理

```typescript
// 推荐的错误过滤器配置
const errorFilter = (error) => {
  // 网络相关错误 -> 显示 ExceptionRefresh
  if (error?.code === 'NETWORK_ERROR' || 
      error?.code === 'TIMEOUT' ||
      error?.message?.includes('timeout') ||
      error?.status >= 500) {
    return true;
  }
  
  // 业务错误 -> 显示 Toast
  return false;
};
```

### 2. 重试策略

```typescript
// 不同场景的重试配置
const criticalDataLoader = useExceptionHandler({
  autoRetryCount: 3,      // 关键数据多重试几次
  autoRetryDelay: 2000    // 重试间隔长一些
});

const normalDataLoader = useExceptionHandler({
  autoRetryCount: 1,      // 普通数据少重试
  autoRetryDelay: 1000    // 重试间隔短一些
});
```

### 3. 组合使用

```typescript
// 在一个页面中组合使用多个 Hook
const criticalData = usePageDataLoader();
const optionalData = useExceptionHandler({
  showErrorToast: true,
  errorFilter: () => false  // 可选数据错误不显示异常页面
});
```

## 注意事项

1. **避免重复初始化**：在同一个组件中不要创建多个相同配置的 Hook
2. **合理设置重试次数**：根据接口的重要性和稳定性设置合适的重试次数
3. **错误过滤器的性能**：错误过滤器会在每次错误时调用，避免复杂的计算
4. **内存泄漏**：Hook 内部会自动清理，但如果手动创建定时器等，需要在组件卸载时清理

## 类型定义

```typescript
interface ExceptionState {
  hasError: boolean;
  isLoading: boolean;
  errorMessage: string;
  retryCount: number;
}

interface ExceptionHandlerOptions {
  autoRetryCount?: number;
  autoRetryDelay?: number;
  showErrorToast?: boolean;
  errorFilter?: (error: any) => boolean;
}
```

通过使用这些 Hook，你可以轻松地在项目中实现统一的异常处理逻辑，提升用户体验和代码的可维护性。
