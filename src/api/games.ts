import http from "@/utils/http";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import { useGlobalStore } from "@/stores/global";

interface requestData {
  token: string;
  action: string;
}

// 获取游戏列表
export const getGames = (data = {}) => {
  return http.post("/vdr/api/hall/game/list", data, {
    type: "formData",
    retry: 3,
  });
};
// 获取游戏配置
export const getGameConfig = (data = {}) => {
  const globalStore = useGlobalStore();
  const params = {
    appChannel: globalStore.channel,
    appSource: "mania",
    appVersion: ALL_APP_SOURCE_CONFIG.app_version,
    appPackageName: ALL_APP_SOURCE_CONFIG.app_bundle_id,
    token: undefined,
  };
  return http.post(
    "/common/api/set/get",
    { ...params, ...data },
    { type: "formData", retry: 3, transformResult: (res) => res.data }
  );
};

// 点击游戏卡片跳转
export const gameItemLogin = (data: {}) => {
  return http.post("/vdr/api/jili/login", data, {
    type: "formData",
    retry: 3,
    transformResult: (res) => res.data,
  });
};

// 查询游戏详情
export const getGameInfo = (data: {}) => {
  return http.post("/vdr/api/get/third/party/game/list", data, {
    type: "formData",
  });
};
// 喜欢
export const likeGame = (data: {}) => {
  return http.post("/vdr/api/is/like", data, {
    type: "formData",
  });
};
// like列表
export const likeHistory = (data = {}) => {
  return http.post(
    "/vdr/api/get/history/or/like",
    {
      type: 1,
      ...data,
    },
    {
      type: "formData",
    }
  );
};
// banner
export const getBanners = (data: any) => {
  return http.post("/avt/api/banner/activity/list", { ...data }, { type: "formData" });
};

// 获取待播放的跑马灯数据
export const getMarquee = (params) => {
  return http.get("/common/api/marquee/list", { params });
};

interface BackhallResponse {
  balance: number;
}

//  查询余额
export const backhall = (data = {}) => {
  return http.post<BackhallResponse>("/common/api/jili/backhall", data);
};

//获取所有 返奖 活动奖励 弹窗
export const getBonusList = (data: requestData) => {
  return http.post("/avt/api/activity/bonus_list", data);
};

//获取 是否维护状态
export const getUpdateList = (data = {}) => {
  return http.post("/vdr/api/maintenance/game/list", { is_new: 1, ...data });
};
