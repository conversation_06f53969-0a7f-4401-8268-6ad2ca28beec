import http from "@/utils/http";

// jili排行榜 type 1 today   2  yesterday
export const rankSolt = (data = {}) => {
  return http.post("/avt/api/rank/slot", data, { type: "formData" });
};
// 反水 today
export const dailyToday = (data = {}) => {
  return http.post("/avt/api/activity/daily-rebate", data, { type: "formData" });
};
// 反水 yesterday
export const dailyYesterday = (data = {}) => {
  return http.post("/avt/api/activity/daily-rebate-yesterday", data, { type: "formData" });
};
// 反水 type : 1 today   2 yesterday
export const cashback = (data = {}) => {
  return http.post("/avt/api/activity/jili_game/cashback", data, { type: "formData" });
};
// casino 排行
export const casinoRank = (data = {}) => {
  return http.post("/avt/api/rank/casino", data, { type: "formData" });
};
// 周统计配置
export const weeklyConfig = (params = {}) => {
  return http.get("/activity/weekly/config", {
    params,
    transformResult: (res) => res.data,
  });
};
// 周统计信息
export const weeklyInfo = (params = {}) => {
  return http.get("/activity/weekly/receive/info", {
    params,
    transformResult: (res) => res.data,
  });
};

// 获取自定义排行榜活动列表
export const getTournamentList = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/tournament-list", data, {
    type: "formData",
  });
};
// 获取待报名活动列表
export const getPendingTournamentList = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/pending-registration", data, {
    type: "formData",
  });
};

// 报名接口
export const registerTournament = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/register", data, {
    type: "formData",
  });
};

// 获取活动详情
export const tournamentDetails = (data = {}) => {
  return http.post(
    "/bmp-activity/v1/custom-leaderboard/details",
    { ...data, action: null },
    {
      type: "formData",
    }
  );
};

// 获取排行榜列表
export const getLeaderboardList = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/leaderboard-list", data, {
    type: "formData",
  });
};

// 获取玩家投注汇总
export const playerBettingSummary = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/player-betting-summary", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

// 获取玩家游戏分布
export const playerGameDistribution = (data = {}) => {
  return http.post("/bmp-activity/v1/custom-leaderboard/player-game-distribution", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

export const getBonusWalletList = (params = {}) => {
  return http.get("/open/api/wallet/list", {
    params,
    transformResult: (res) => res.data,
  });
};
// 领取任务
export const receiveWalletTask = (data = {}) => {
  return http.post("/open/api/wallet/receive/task", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// 领取任务奖励 user_id  task_id
export const receiveWalletReward = (data = {}) => {
  return http.post("/open/api/wallet/receive/reward", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
