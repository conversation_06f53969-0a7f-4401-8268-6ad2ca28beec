/**
 * 游戏相关类型定义
 */

import type { Game, Category, Provider, LiveGame } from "./store";

// ==================== API 响应类型 ====================

/**
 * 游戏配置接口响应
 */
export interface GameConfigResponse {
  /** 游戏分类列表 */
  game_type: Category[];
  /** 第三方厂商列表 */
  third_company: Provider[];
  /** 直播游戏列表 */
  live_game: LiveGame[];
  /** 登录配置 */
  login_conf: LoginConfig;
  /** 单次充值最大金额 */
  maximum_single_recharge: number;
  /** 其他配置项 */
  [key: string]: any;
}

/**
 * 游戏列表接口响应
 */
export interface GameListResponse extends Array<Game> {}

/**
 * 更新列表接口响应
 */
export interface UpdateListResponse {
  /** 维护中的游戏ID列表 */
  maintenance_list: number[];
  /** 隐藏的游戏ID列表 */
  hide_list: number[];
}

/**
 * 登录配置
 */
export interface LoginConfig {
  /** Facebook登录开关 */
  login_facebook: number;
  /** Google登录开关 */
  login_google: number;
  /** 密码登录开关 */
  login_password: number;
  /** 其他登录配置 */
  [key: string]: any;
}

// ==================== 游戏分类相关 ====================

/**
 * 带游戏的分类
 */
export interface CategoryWithGames extends Category {
  /** 分类下的游戏列表 */
  games: Game[];
}

/**
 * 分类统计信息
 */
export interface CategoryStats {
  /** 分类类型 */
  type: string;
  /** 游戏数量 */
  count: number;
  /** 是否有游戏 */
  hasGames: boolean;
}

// ==================== 厂商相关 ====================

/**
 * 带图片的厂商
 */
export interface ProviderWithImage extends Provider {
  /** 图片URL */
  imageUrl: string;
  /** 图片加载错误标识 */
  imageError: boolean;
}

/**
 * 本地厂商图片
 */
export interface ProviderLocalImage {
  /** 图片名称 */
  name: string;
  /** 图片URL */
  url: string;
}

// ==================== 游戏操作相关 ====================

/**
 * 游戏筛选参数
 */
export interface GameFilterParams {
  /** 是否为新游戏 */
  is_new?: number;
  /** 厂商ID */
  provider_id?: number;
  /** 游戏类型 */
  game_type?: string;
  /** 其他筛选参数 */
  [key: string]: any;
}

/**
 * 游戏排序选项
 */
export type GameSortOption = 'sort' | 'name' | 'created_at' | 'popularity';

/**
 * 游戏排序方向
 */
export type GameSortDirection = 'asc' | 'desc';

// ==================== Store 操作相关 ====================

/**
 * 游戏Store初始化选项
 */
export interface GameStoreInitOptions {
  /** 是否启用轮询 */
  enablePolling?: boolean;
  /** 轮询间隔（毫秒） */
  pollingInterval?: number;
  /** 是否预加载图片 */
  preloadImages?: boolean;
}

/**
 * 游戏分配结果
 */
export interface GameAssignmentResult {
  /** 成功分配的游戏数量 */
  successCount: number;
  /** 失败的游戏数量 */
  failureCount: number;
  /** 分配后的分类列表 */
  categories: CategoryWithGames[];
  /** 错误信息列表 */
  errors: string[];
}

// ==================== 错误处理相关 ====================

/**
 * 游戏Store错误类型
 */
export enum GameStoreErrorType {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 数据解析错误 */
  PARSE_ERROR = 'PARSE_ERROR',
  /** 配置错误 */
  CONFIG_ERROR = 'CONFIG_ERROR',
  /** 游戏列表错误 */
  GAME_LIST_ERROR = 'GAME_LIST_ERROR',
  /** 维护列表错误 */
  MAINTENANCE_ERROR = 'MAINTENANCE_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 游戏Store错误信息
 */
export interface GameStoreError {
  /** 错误类型 */
  type: GameStoreErrorType;
  /** 错误消息 */
  message: string;
  /** 原始错误对象 */
  originalError?: Error;
  /** 错误发生时间 */
  timestamp: number;
  /** 错误上下文 */
  context?: Record<string, any>;
}

// ==================== 工具类型 ====================

/**
 * 可选的游戏属性
 */
export type PartialGame = Partial<Game>;

/**
 * 游戏ID类型
 */
export type GameId = number;

/**
 * 分类类型
 */
export type CategoryType = string;

/**
 * 厂商ID类型
 */
export type ProviderId = number;

// ==================== 常量定义 ====================

/**
 * 默认轮询间隔（毫秒）
 */
export const DEFAULT_POLLING_INTERVAL = 30000;

/**
 * 默认游戏分页大小
 */
export const DEFAULT_PAGE_SIZE = 20;

/**
 * 支持的游戏类型
 */
export const SUPPORTED_GAME_TYPES = [
  'SLOT',
  'LIVE',
  'SPORT',
  'LOTTERY',
  'FISHING',
  'CARD'
] as const;

/**
 * 支持的游戏类型联合类型
 */
export type SupportedGameType = typeof SUPPORTED_GAME_TYPES[number];
