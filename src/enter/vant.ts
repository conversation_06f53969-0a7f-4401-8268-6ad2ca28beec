// config/vant.js
import { App } from "vue";
import {
  Toast,
  Dialog,
  ActionSheet,
  ConfigProvider,
  List,
  Radio,
  RadioGroup,
  Tabs,
  Tab,
  Cell,
  CellGroup,
} from "vant";
import { useDialog } from "@/components/ZDialog/dialog";
import { useActionSheet } from "@/components/ZActionSheet/actionSheet";

// 引入样式
import "vant/es/toast/style";
import "vant/es/dialog/style";
import "vant/es/list/style";
import "vant/es/radio/style";
import "vant/es/radio-group/style";
import "vant/es/tabs/style";
import "vant/es/tab/style";
import "vant/es/cell/style";
import "vant/es/cell-group/style";

export function registerVantComponents(app: App) {
  app.use(Toast);
  app.use(Dialog);
  app.use(ActionSheet);
  app.use(ConfigProvider);
  app.use(List);
  app.use(Radio);
  app.use(RadioGroup);
  app.use(Tabs);
  app.use(Tab);
  app.use(Cell);
  app.use(CellGroup);
}

let globalDialog: ReturnType<typeof useDialog> | null = null;

export const setGlobalDialog = (dialogInstance: ReturnType<typeof useDialog>) => {
  globalDialog = dialogInstance;
};

export const getGlobalDialog = () => {
  if (!globalDialog) {
    throw new Error("$dialog is not initialized");
  }
  return globalDialog;
};

let globalActionSheet: ReturnType<typeof useActionSheet> | null = null;

export const setGlobalActionSheet = (actionSheetInstance: ReturnType<typeof useActionSheet>) => {
  globalActionSheet = actionSheetInstance;
};

export const getGlobalActionSheet = () => {
  if (!globalActionSheet) {
    throw new Error("$actionSheet is not initialized");
  }
  return globalActionSheet;
};
