/**
 * Type declarations for pinia-plugin-persistedstate
 * 
 * This file extends Pinia's DefineStoreOptionsBase interface to include
 * the persist property for store persistence configuration.
 * 
 * Note: This is needed because the official types from pinia-plugin-persistedstate
 * are not being automatically loaded in our TypeScript configuration.
 */

import 'pinia'

declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    /**
     * Persist store in storage
     * @see https://prazdevs.github.io/pinia-plugin-persistedstate
     */
    persist?: 
      | boolean
      | {
          /**
           * Storage key for the persisted data
           * @default store.$id
           */
          key?: string
          
          /**
           * Storage instance to use
           * @default localStorage
           */
          storage?: {
            getItem: (key: string) => string | null
            setItem: (key: string, value: string) => void
          }
          
          /**
           * Array of dot-notation paths to pick what should be persisted
           * @default undefined (persist whole state)
           */
          pick?: string[]
          
          /**
           * Array of dot-notation paths to omit from what should be persisted
           * @default undefined (persist whole state)
           */
          omit?: string[]
          
          /**
           * Custom serializer for data persistence
           */
          serializer?: {
            serialize: (value: any) => string
            deserialize: (value: string) => any
          }
          
          /**
           * Hook function run before hydrating a store state
           */
          beforeHydrate?: (context: any) => void
          
          /**
           * Hook function run after rehydrating a persisted state
           */
          afterHydrate?: (context: any) => void
          
          /**
           * Enable debug mode for persistence
           * @default false
           */
          debug?: boolean
        }
      | Array<{
          key?: string
          storage?: {
            getItem: (key: string) => string | null
            setItem: (key: string, value: string) => void
          }
          pick?: string[]
          omit?: string[]
          serializer?: {
            serialize: (value: any) => string
            deserialize: (value: string) => any
          }
          beforeHydrate?: (context: any) => void
          afterHydrate?: (context: any) => void
          debug?: boolean
        }>
  }
}
